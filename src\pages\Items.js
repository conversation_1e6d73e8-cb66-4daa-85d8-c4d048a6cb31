import React, { useState, useEffect, useRef, useCallback } from 'react';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaFileExcel, FaBoxes, FaExclamationTriangle } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import ItemImporter from '../components/ItemImporter';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import DataTable from '../components/DataTable';
import useFormReset from '../hooks/useFormReset';
import useInputHandler from '../hooks/useInputHandler';
import ItemAutocomplete from '../components/ItemAutocomplete';
import './Items.css';
import '../assets/css/items.css';

const Items = () => {
  const { items, loading, addItem, updateItem, deleteItem, loadItems } = useApp();
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [currentItem, setCurrentItem] = useState(null);
  const [duplicateItemError, setDuplicateItemError] = useState(false);
  const [showAutocomplete, setShowAutocomplete] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);

  // استخدام هوك معالجة الإدخال المحسن
  const {
    formData,
    handleInputChange,
    updateFormData,
    resetForm: resetFormData
  } = useInputHandler({
    name: '',
    unit: ''
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  const [showImporter, setShowImporter] = useState(false);

  // متغير للتحكم في التحديث التلقائي - مفعل افتراضيًا
  const [autoRefreshEnabled] = useState(true);

  // مرجع لتخزين معرف الفاصل الزمني
  const autoRefreshIntervalRef = useRef(null);

  // مرجع لتتبع مؤقت الإشعارات
  const alertTimeoutRef = useRef(null);

  // مرجع للنموذج
  const formRef = useRef(null);

  // استخدام هوك إعادة تعيين النموذج المحسن
  const { isMounted, enableFormFields, resetFormCompletely, enableFormFieldsAfterDelete } = useFormReset(formRef);

  // وظيفة إعادة تعيين النموذج - تستخدم فقط عند الضغط على زر الإنهاء
  const resetForm = () => {
    if (isMounted.current) {
      resetFormData({
        name: '',
        unit: ''
      });
    }
  };

  // تأثير لتنظيف المؤقت عند إلغاء تحميل المكون
  useEffect(() => {
    return () => {
      // تنظيف مؤقت الإشعارات عند إلغاء تحميل المكون
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);

  // تخزين البيانات الأصلية للأصناف
  const [originalItems, setOriginalItems] = useState([]);

  // تحديث بيانات الأصناف عند تحميل الصفحة
  useEffect(() => {

    const refreshItemsData = async () => {
      try {
        console.log('جاري تحديث بيانات الأصناف...');

        // استدعاء وظيفة الحصول على الأصناف بدون تجاوز التخزين المؤقت للحفاظ على الأداء
        const refreshedItems = await window.api.items.getAll(false);

        if (Array.isArray(refreshedItems)) {
          console.log('تم الحصول على بيانات الأصناف المحدثة:', refreshedItems.length);

          // تخزين البيانات الأصلية
          setOriginalItems(refreshedItems);

          // تطبيق البحث الحالي على البيانات المحدثة
          if (searchTerm && searchTerm.trim() !== '') {
            const filtered = refreshedItems.filter(item =>
              item && item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredItems(filtered);
          } else {
            // إذا لم يكن هناك بحث، عرض جميع الأصناف
            setFilteredItems(refreshedItems);
          }

          console.log('تم تحديث قائمة الأصناف بنجاح');
        } else {
          console.warn('لم يتم استلام مصفوفة صالحة من الأصناف:', refreshedItems);
        }
      } catch (error) {
        console.error('خطأ في تحديث بيانات الأصناف:', error);
      }
    };

    // تعريف وظيفة تحديث الصنف في القائمة الحالية
    window.updateItemInCurrentList = (updatedItemData) => {
      console.log('تحديث الصنف في القائمة الحالية:', updatedItemData);

      if (!updatedItemData || !updatedItemData.id) {
        console.error('بيانات الصنف غير صالحة للتحديث:', updatedItemData);
        return false;
      }

      // تسجيل معلومات إضافية للتشخيص
      console.log('معرف الصنف المحدث:', updatedItemData.id);
      console.log('اسم الصنف المحدث:', updatedItemData.name);
      console.log('وحدة القياس المحدثة:', updatedItemData.unit);

      // تحديث قائمة الأصناف المعروضة
      setFilteredItems(prevItems => {
        // تسجيل عدد العناصر قبل التحديث
        console.log('عدد العناصر قبل التحديث:', prevItems.length);

        // البحث عن الصنف في القائمة الحالية
        const itemExists = prevItems.some(item =>
          item.id === updatedItemData.id ||
          item._id === updatedItemData.id ||
          item.id === updatedItemData._id ||
          item._id === updatedItemData._id
        );

        console.log('هل الصنف موجود في القائمة؟', itemExists);

        if (!itemExists) {
          console.log('الصنف غير موجود في القائمة، سيتم إعادة تحميل القائمة بالكامل');
          // إعادة تحميل القائمة بالكامل
          refreshItemsData();
          return prevItems;
        }

        // تحديث الصنف في القائمة
        const updatedItems = prevItems.map(item => {
          // التحقق من تطابق المعرف بجميع الطرق الممكنة
          if (
            item.id === updatedItemData.id ||
            item._id === updatedItemData.id ||
            item.id === updatedItemData._id ||
            item._id === updatedItemData._id
          ) {
            console.log('تم العثور على الصنف للتحديث:', item);
            console.log('سيتم تحديثه إلى:', { ...item, name: updatedItemData.name, unit: updatedItemData.unit });
            return { ...item, name: updatedItemData.name, unit: updatedItemData.unit };
          }
          return item;
        });

        // تسجيل عدد العناصر بعد التحديث
        console.log('عدد العناصر بعد التحديث:', updatedItems.length);

        return updatedItems;
      });

      // تحديث القائمة الأصلية أيضًا
      setOriginalItems(prevItems => {
        return prevItems.map(item => {
          if (
            item.id === updatedItemData.id ||
            item._id === updatedItemData.id ||
            item.id === updatedItemData._id ||
            item._id === updatedItemData._id
          ) {
            return { ...item, name: updatedItemData.name, unit: updatedItemData.unit };
          }
          return item;
        });
      });

      return true;
    };

    // استدعاء الدالة عند تحميل الصفحة
    refreshItemsData();

    // تعيين وظيفة تحديث الأصناف العالمية
    window.loadItems = refreshItemsData;

    // تعيين وظيفة تحديث قائمة الأصناف في الواجهة
    window.updateItemsList = (items) => {
      console.log('تحديث قائمة الأصناف في الواجهة من خلال window.updateItemsList');
      console.log(`عدد الأصناف المستلمة: ${items.length}`);

      if (Array.isArray(items)) {
        // تخزين البيانات الأصلية
        setOriginalItems(items);

        // تطبيق البحث الحالي على البيانات المحدثة
        if (searchTerm && searchTerm.trim() !== '') {
          const filtered = items.filter(item =>
            item && item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
          console.log(`تم تطبيق البحث، عدد الأصناف بعد التصفية: ${filtered.length}`);
          setFilteredItems(filtered);
        } else {
          console.log('تحديث قائمة الأصناف بدون تصفية');
          setFilteredItems(items);
        }
        return true;
      } else {
        console.warn('البيانات المستلمة ليست مصفوفة صالحة:', items);
        return false;
      }
    };

    // تنظيف الفاصل الزمني والوظائف العالمية عند إلغاء تحميل المكون
    return () => {
      // تنظيف الفاصل الزمني للتحديث التلقائي
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }

      // إزالة الوظائف العالمية
      window.loadItems = null;
      window.updateItemInCurrentList = null;
      window.updateItemsList = null;

      console.log('تم تنظيف الفواصل الزمنية والوظائف العالمية');
    };
  }, [searchTerm]);

  // تطبيق البحث عند تغيير مصطلح البحث
  useEffect(() => {
    if (items && items.length > 0) {
      if (searchTerm && searchTerm.trim() !== '') {
        // تطبيق البحث فقط إذا كان هناك نص في شريط البحث
        setFilteredItems(
          items.filter(item =>
            item && item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())
          )
        );
      } else {
        // إذا كان شريط البحث فارغًا، عرض جميع الأصناف
        setFilteredItems(items);
      }
    } else {
      setFilteredItems([]);
    }
  }, [items, searchTerm]);

  // إنشاء وظيفة التحديث التلقائي باستخدام useCallback
  const setupAutoRefresh = useCallback(() => {
    // إلغاء أي فاصل زمني سابق
    if (autoRefreshIntervalRef.current) {
      clearInterval(autoRefreshIntervalRef.current);
      autoRefreshIntervalRef.current = null;
    }

    // إنشاء فاصل زمني جديد فقط إذا كان التحديث التلقائي ممكّنًا
    if (autoRefreshEnabled) {
      console.log('تم تمكين التحديث التلقائي كل 30 ثانية');

      autoRefreshIntervalRef.current = setInterval(async () => {
        try {
          console.log('بدء التحديث التلقائي للأصناف...');

          // تحديث البيانات الأصلية بدون تجاوز التخزين المؤقت للحفاظ على الأداء
          const refreshedItems = await window.api.items.getAll(false);

          if (Array.isArray(refreshedItems)) {
            console.log('تحديث دوري للأصناف:', refreshedItems.length);

            // تخزين البيانات الأصلية
            setOriginalItems(refreshedItems);

            // تطبيق البحث الحالي على البيانات المحدثة
            if (searchTerm && searchTerm.trim() !== '') {
              const filtered = refreshedItems.filter(item =>
                item && item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())
              );
              setFilteredItems(filtered);
            } else {
              setFilteredItems(refreshedItems);
            }
          }
        } catch (error) {
          console.error('خطأ في التحديث التلقائي للأصناف:', error);
        }
      }, 30000); // تحديث كل 30 ثانية
    } else {
      console.log('تم تعطيل التحديث التلقائي');
    }
  }, [autoRefreshEnabled, searchTerm]);

  // إضافة تأثير لمراقبة تغيير autoRefreshEnabled
  useEffect(() => {
    // إعداد التحديث التلقائي
    setupAutoRefresh();

    // تنظيف الفاصل الزمني عند إلغاء تحميل المكون
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [autoRefreshEnabled, setupAutoRefresh]);

  // معالجة اختيار وحدة القياس
  const handleUnitSelect = (unit) => {
    updateFormData({ unit });
    console.log(`Unit selected: ${unit}`);
  };

  // معالجة اختيار صنف من الإكمال التلقائي
  const handleItemAutocompleteSelect = (item) => {
    console.log('تم اختيار الصنف من الإكمال التلقائي:', item);

    // تعيين الصنف المحدد
    setSelectedItem(item);

    // إعادة تعيين حالة خطأ الصنف المكرر
    setDuplicateItemError(false);

    // تحديث بيانات النموذج باسم الصنف المحدد
    updateFormData({
      name: item.name,
      unit: item.unit || 'قطعة'
    });

    // عرض تنبيه إذا كان الصنف موجودًا بالفعل وكنا في وضع الإضافة
    if (modalMode === 'add') {
      // في وضع الإضافة، نعرض تنبيه للمستخدم بأن هذا الصنف موجود بالفعل
      // ولكن لا نمنع المستخدم من تعديل الاسم وإضافة صنف جديد
      showAlert('warning', 'هذا الصنف موجود بالفعل في قاعدة البيانات. يمكنك تغيير الاسم أو إلغاء الإضافة.', 6000);
    }
  };

  // تم إزالة وظيفة مزامنة المخزون اليدوية

  const handleAddItem = () => {
    console.log('Starting handleAddItem...');

    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isViewer = currentUserRole === 'viewer';

    // المشاهد فقط لا يمكنه إضافة الأصناف (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لإضافة الأصناف. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    // أولاً نقوم بتعيين وضع الإضافة
    setModalMode('add');

    // إعادة تعيين الصنف الحالي إلى null
    setCurrentItem(null);

    // إعادة تعيين النموذج بشكل كامل
    resetFormCompletely();

    // ثم نقوم بإعادة تعيين البيانات
    resetForm();

    // تنظيف أي قيم مؤقتة في هوك useInputHandler
    resetFormData({
      name: '',
      unit: ''
    });

    // إعادة تعيين حالة الإكمال التلقائي
    setShowAutocomplete(true);
    setSelectedItem(null);
    setDuplicateItemError(false);

    // أخيرًا نقوم بفتح النموذج
    setShowModal(true);

    // إعادة تمكين حقول النموذج بعد فتح النموذج
    // استخدام محاولات متعددة بفواصل زمنية مختلفة
    const intervals = [100, 300, 800];
    intervals.forEach(interval => {
      setTimeout(() => {
        enableFormFields();
      }, interval);
    });
  };

  const handleEditItem = async (item) => {
    console.log('Starting handleEditItem for item:', item.id);

    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isViewer = currentUserRole === 'viewer';

    // المشاهد فقط لا يمكنه تعديل الأصناف (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لتعديل الأصناف. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    // التحقق من وجود الصنف
    if (!item) {
      console.error('لا يوجد صنف للتعديل');
      showAlert('danger', 'لا يوجد صنف للتعديل');
      return;
    }

    // التحقق من وجود معرف الصنف
    if (!item.id && !item._id) {
      console.error('معرف الصنف غير موجود:', item);
      showAlert('danger', 'معرف الصنف غير موجود');
      return;
    }

    console.log('تعديل الصنف:', item);

    try {
      // محاولة الحصول على بيانات الصنف المحدثة من قاعدة البيانات
      const itemId = item.id || item._id;
      console.log('محاولة الحصول على بيانات الصنف المحدثة للمعرف:', itemId);

      let updatedItem = item;
      try {
        // محاولة الحصول على بيانات الصنف المحدثة
        const fetchedItem = await window.api.items.getById(itemId);
        if (fetchedItem && !fetchedItem.error) {
          console.log('تم الحصول على بيانات الصنف المحدثة:', fetchedItem);
          updatedItem = fetchedItem;
        }
      } catch (fetchError) {
        console.warn('لم يتم الحصول على بيانات الصنف المحدثة:', fetchError);
        // استمر باستخدام البيانات الحالية
      }

      // أولاً نقوم بتعيين وضع التعديل
      setModalMode('edit');
      setCurrentItem(updatedItem);

      // إعادة تعيين النموذج بشكل كامل
      resetFormCompletely();

      // ثم نقوم بإعادة تعيين البيانات
      resetForm();

      // تعيين البيانات الجديدة
      resetFormData({
        name: updatedItem.name || '',
        unit: updatedItem.unit || ''
      });

      // إعادة تعيين حالة الإكمال التلقائي
      setShowAutocomplete(false); // استخدام الإدخال اليدوي في وضع التعديل
      setSelectedItem(null);
      setDuplicateItemError(false);

      // أخيرًا نقوم بفتح النموذج
      setShowModal(true);

      // إعادة تمكين حقول النموذج بعد فتح النموذج
      // استخدام محاولات متعددة بفواصل زمنية مختلفة
      const intervals = [100, 300, 800];
      intervals.forEach(interval => {
        setTimeout(() => {
          enableFormFields();
        }, interval);
      });
    } catch (error) {
      console.error('خطأ في معالجة تعديل الصنف:', error);
      showAlert('danger', 'حدث خطأ أثناء تحضير نموذج تعديل الصنف');
    }
  };

  const handleDeleteItem = async (id) => {
    if (!id) {
      console.error('معرف الصنف غير صالح:', id);
      showAlert('danger', 'معرف الصنف غير صالح');
      return;
    }

    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isAdmin = currentUserRole === 'admin' || currentUserRole === 'manager';
    const isViewer = currentUserRole === 'viewer';

    // المشاهد فقط لا يمكنه حذف الأصناف (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لحذف الأصناف. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
      try {
        console.log('محاولة حذف الصنف بالمعرف:', id);
        console.log('دور المستخدم الحالي:', currentUserRole, 'هل هو مدير:', isAdmin);

        // استخدام window.api.items.delete بدلاً من window.api.invoke
        console.log('استدعاء window.api.items.delete مع المعرف:', id);
        // هذا سيقوم بتمرير دور المستخدم تلقائيًا
        const result = await window.api.items.delete(id);

        console.log('نتيجة حذف الصنف:', result);

        // التحقق من نجاح العملية
        if (!result || !result.success) {
          // التحقق من صلاحيات المستخدم
          if (result && result.error && result.error.includes('ليس لديك صلاحية')) {
            console.error('خطأ في صلاحيات المستخدم:', result.error);
            showAlert('danger', result.error);
            return;
          }

          // التحقق مما إذا كان الصنف محميًا (له معاملات بيع)
          if (result && result.protected) {
            if (isAdmin) {
              // إذا كان المستخدم مديرًا وفشل الحذف رغم تمرير forceDelete = true
              console.error('فشل في حذف الصنف المحمي حتى مع صلاحيات المدير:', result.error);
              showAlert('danger', result.error || 'فشل في حذف الصنف المحمي');
              return;
            } else {
              // إذا لم يكن المستخدم مديرًا، نعرض رسالة خطأ
              console.error('لا يمكن حذف الصنف المحمي:', result.error);
              showAlert('danger', result.error || 'لا يمكنك حذف هذا الصنف لأنه مرتبط بعمليات بيع. فقط المدير يمكنه حذف الأصناف المحمية.');
              return;
            }
          } else {
            // خطأ آخر غير متعلق بالحماية أو الصلاحيات
            console.error('فشل في حذف الصنف:', result ? result.error : 'سبب غير معروف');
            showAlert('danger', result && result.error ? result.error : 'فشل في حذف الصنف');
            return;
          }
        }

        // إذا نجح الحذف
        // إعادة تعيين حالة المكونات
        setCurrentItem(null);
        setShowAutocomplete(true);
        setSelectedItem(null);
        setDuplicateItemError(false);

        // إعادة تعيين بيانات النموذج
        resetFormData({ name: '', unit: '' });
        resetForm();

        // استخدام الوظيفة المحسنة لإعادة تمكين حقول الإدخال بعد الحذف فورًا
        console.log('استدعاء enableFormFieldsAfterDelete فورًا بعد عملية الحذف...');
        enableFormFieldsAfterDelete();

        // إعادة تمكين حقول النموذج بشكل مباشر
        try {
          console.log('محاولة إعادة تمكين حقول النموذج مباشرة...');

          if (formRef.current) {
            const inputs = formRef.current.querySelectorAll('input, select, textarea, button');
            inputs.forEach(input => {
              input.disabled = false;
              if (input.type === 'text' || input.type === 'number') {
                input.readOnly = false;
              }
              input.style.opacity = '1';
              input.style.pointerEvents = 'auto';

              // تحفيز حدث تغيير لضمان تحديث React
              try {
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
              } catch (e) {
                console.error('خطأ في تحفيز حدث التغيير:', e);
              }
            });
          }
        } catch (directError) {
          console.error('خطأ في إعادة تمكين حقول النموذج مباشرة:', directError);
        }

        // تحديث قائمة الأصناف بإزالة الصنف المحذوف مباشرة
        console.log('تحديث قائمة الأصناف بإزالة الصنف المحذوف، معرف الصنف:', id);

        // استخدام دالة تصفية محسنة للتأكد من إزالة الصنف المحذوف
        const filterDeletedItem = (item) => {
          // التحقق من جميع أشكال المعرف المحتملة
          if (!item) return false;

          const itemId = item.id || item._id;
          if (!itemId) return true; // الاحتفاظ بالعناصر بدون معرف

          // مقارنة المعرف كنص للتأكد من المطابقة
          const idStr = String(id);
          const itemIdStr = String(itemId);

          return itemIdStr !== idStr;
        };

        // تطبيق التصفية على قائمة الأصناف المعروضة
        setFilteredItems(prevItems => {
          const newItems = prevItems.filter(filterDeletedItem);
          console.log(`تم تصفية الأصناف: من ${prevItems.length} إلى ${newItems.length}`);
          return newItems;
        });

        // تطبيق نفس التصفية على قائمة الأصناف الأصلية
        setOriginalItems(prevItems => {
          const newItems = prevItems.filter(filterDeletedItem);
          return newItems;
        });

        // تحديث متغير items في سياق التطبيق إذا كان متاحًا
        if (typeof updateItems === 'function') {
          updateItems(prevItems => prevItems.filter(filterDeletedItem));
        }

        // إعادة تحميل الأصناف من قاعدة البيانات فورًا
        try {
          console.log('إعادة تحميل الأصناف فورًا بعد الحذف...');

          // مسح التخزين المؤقت للأصناف
          if (window.api && window.api.items && window.api.items.clearCache) {
            console.log('مسح التخزين المؤقت للأصناف بعد الحذف');
            await window.api.items.clearCache('delete-item-from-items-page', id);
          }

          // استخدام window.api.items.getAll مع تجاوز التخزين المؤقت للتأكد من الحصول على البيانات المحدثة
          const refreshedItems = await window.api.items.getAll(true);

          if (Array.isArray(refreshedItems)) {
            console.log('تم إعادة تحميل الأصناف بنجاح (فورًا):', refreshedItems.length);
            setFilteredItems(refreshedItems);
            setOriginalItems(refreshedItems);
          }
        } catch (reloadError) {
          console.error('خطأ في إعادة تحميل الأصناف فورًا بعد الحذف:', reloadError);
        }

        // إعادة تحميل الأصناف مرة أخرى بعد فترة قصيرة جدًا
        setTimeout(async () => {
          try {
            console.log('إعادة تحميل الأصناف بعد الحذف (المحاولة الثانية)...');
            const refreshedItems = await window.api.items.getAll(true);

            if (Array.isArray(refreshedItems)) {
              console.log('تم إعادة تحميل الأصناف بنجاح (المحاولة الثانية):', refreshedItems.length);
              setFilteredItems(refreshedItems);
              setOriginalItems(refreshedItems);
            }
          } catch (reloadError) {
            console.error('خطأ في إعادة تحميل الأصناف بعد الحذف (المحاولة الثانية):', reloadError);
          }
        }, 100);

        // محاولة ثالثة لإعادة تحميل الأصناف بعد فترة قصيرة
        setTimeout(async () => {
          try {
            console.log('إعادة تحميل الأصناف بعد الحذف (المحاولة الثالثة)...');

            // استخدام window.api.items.getAll مع تجاوز التخزين المؤقت
            const refreshedItems = await window.api.items.getAll(true);

            if (Array.isArray(refreshedItems)) {
              console.log('تم إعادة تحميل الأصناف بنجاح (المحاولة الثالثة):', refreshedItems.length);
              setFilteredItems(refreshedItems);
              setOriginalItems(refreshedItems);

              // تحديث الأصناف في جميع النوافذ
              if (window.api && window.api.items && window.api.items.refreshAllWindows) {
                console.log('تحديث الأصناف في جميع النوافذ بعد الحذف...');
                await window.api.items.refreshAllWindows();
              }
            }
          } catch (reloadError) {
            console.error('خطأ في إعادة تحميل الأصناف بعد الحذف (المحاولة الثالثة):', reloadError);
          }
        }, 300);

        // محاولة نهائية لإعادة تمكين حقول النموذج
        setTimeout(async () => {
          try {
            // إعادة تمكين حقول النموذج مرة أخرى للتأكد
            if (formRef.current) {
              const inputs = formRef.current.querySelectorAll('input, select, textarea, button');
              inputs.forEach(input => {
                input.disabled = false;
                if (input.type === 'text' || input.type === 'number') {
                  input.readOnly = false;
                }
                input.style.opacity = '1';
                input.style.pointerEvents = 'auto';

                // تحفيز حدث تغيير لضمان تحديث React
                try {
                  const event = new Event('change', { bubbles: true });
                  input.dispatchEvent(event);
                } catch (e) {
                  console.error('خطأ في تحفيز حدث التغيير:', e);
                }

                // تحفيز حدث تركيز لضمان تفاعل المستخدم
                try {
                  const focusEvent = new Event('focus', { bubbles: true });
                  input.dispatchEvent(focusEvent);

                  const blurEvent = new Event('blur', { bubbles: true });
                  input.dispatchEvent(blurEvent);
                } catch (e) {
                  console.error('خطأ في تحفيز أحداث التركيز:', e);
                }
              });
              console.log('تم إعادة تمكين حقول النموذج في المحاولة النهائية');
            }
          } catch (finalError) {
            console.error('خطأ في المحاولة النهائية:', finalError);
          }
        }, 400);

        // عرض رسالة نجاح مع معلومات إضافية إذا تم حذف معاملات
        if (result.deletedTransactions && result.deletedTransactions > 0) {
          console.log(`تم حذف الصنف بنجاح مع ${result.deletedTransactions} معاملة مرتبطة`);
          showAlert('success', `تم حذف الصنف بنجاح مع ${result.deletedTransactions} معاملة مرتبطة`);
        } else {
          console.log('تم حذف الصنف بنجاح');
          showAlert('success', 'تم حذف الصنف بنجاح');
        }
      } catch (err) {
        console.error('خطأ في handleDeleteItem:', err);
        showAlert('danger', err.message || 'فشل في حذف الصنف');
      }
    }
  };

  // معالجة استيراد الأصناف
  const handleImportItems = async (importedItems) => {
    try {
      console.log('بدء استيراد الأصناف:', importedItems.length);

      // التحقق من دور المستخدم الحالي
      const currentUserRole = localStorage.getItem('currentUserRole');
      const isViewer = currentUserRole === 'viewer';

      // المشاهد لا يمكنه استيراد الأصناف
      if (isViewer) {
        showAlert('danger', 'ليس لديك صلاحية لاستيراد الأصناف. المشاهد يمكنه فقط عرض البيانات.');
        return;
      }

      // التحقق من وجود أصناف للاستيراد
      if (!importedItems || importedItems.length === 0) {
        showAlert('warning', 'لا توجد أصناف صالحة للاستيراد');
        return;
      }

      let addedCount = 0;
      let updatedCount = 0;
      let errorCount = 0;

      // إظهار رسالة بدء الاستيراد
      showAlert('info', `جاري استيراد ${importedItems.length} صنف...`);

      // استيراد الأصناف واحدًا تلو الآخر
      for (const item of importedItems) {
        try {
          // التحقق من وجود اسم الصنف
          if (!item.name || item.name.trim() === '') {
            console.warn('تم تخطي صنف بدون اسم');
            errorCount++;
            continue;
          }

          console.log('معالجة الصنف:', item.name);

          // البحث عن الصنف في قائمة الأصناف الحالية
          const existingItem = items.find(i =>
            i && i.name && item && item.name &&
            i.name.toLowerCase() === item.name.toLowerCase()
          );

          if (existingItem) {
            console.log('تحديث الصنف الموجود:', existingItem.name);

            // تحديث الصنف الموجود
            const updatedItem = {
              ...existingItem,
              unit: item.unit || existingItem.unit,
              // يمكن إضافة المزيد من الحقول للتحديث حسب الحاجة
            };

            // استخدام window.api.invoke مباشرة
            const itemId = existingItem.id || existingItem._id;
            await window.api.invoke('update-item', itemId, updatedItem, currentUserRole);

            updatedCount++;
          } else {
            console.log('إضافة صنف جديد:', item.name);

            // إضافة صنف جديد
            const newItem = {
              name: item.name.trim(),
              unit: item.unit || 'قطعة',
              initial_quantity: item.current_quantity || 0,
              avg_price: item.avg_price || 0,
              selling_price: item.selling_price || 0,
              minimum_quantity: item.minimum_quantity || 0
            };

            await window.api.items.add(newItem);
            addedCount++;
          }
        } catch (itemError) {
          console.error('خطأ في استيراد الصنف:', item.name, itemError);
          errorCount++;
        }
      }

      // إعادة تحميل الأصناف بعد الاستيراد
      try {
        console.log('إعادة تحميل الأصناف بعد الاستيراد...');
        const refreshedItems = await window.api.items.getAll(true);

        if (Array.isArray(refreshedItems)) {
          console.log('تم إعادة تحميل الأصناف بنجاح:', refreshedItems.length);
          setFilteredItems(refreshedItems);
        }
      } catch (reloadError) {
        console.error('خطأ في إعادة تحميل الأصناف بعد الاستيراد:', reloadError);
      }

      // عرض رسالة نجاح مع تفاصيل الاستيراد
      showAlert(
        errorCount > 0 ? 'warning' : 'success',
        `تم استيراد الأصناف (${addedCount} جديد، ${updatedCount} محدث${errorCount > 0 ? `، ${errorCount} خطأ` : ''})`
      );
    } catch (err) {
      console.error('Error importing items:', err);
      showAlert('danger', 'فشل في استيراد الأصناف: ' + (err.message || 'خطأ غير معروف'));
    }
  };

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    console.log('Form submitted', formData);

    if (!formData.name || formData.name.trim() === '') {
      showAlert('danger', 'الرجاء إدخال اسم الصنف');
      return;
    }

    try {
      if (modalMode === 'add') {
        // إضافة صنف جديد - إرسال اسم الصنف ووحدة القياس والمعلومات الإضافية
        const newItem = {
          name: formData.name.trim(),
          unit: formData.unit ? formData.unit.trim() : '',
          initial_quantity: 0,
          avg_price: 0,
          selling_price: 0,
          minimum_quantity: 0
        };

        console.log('New item:', newItem);

        // إضافة الصنف الجديد باستخدام window.api.items.add
        console.log('استدعاء window.api.items.add مع البيانات:', newItem);
        const addedItem = await window.api.items.add(newItem);
        console.log('Added item result:', addedItem);

        // التحقق من وجود خطأ في الإضافة
        if (addedItem && addedItem.error) {
          // إذا كان هناك خطأ، عرض رسالة الخطأ
          console.error('خطأ في إضافة الصنف:', addedItem.error);

          // التحقق من نوع الخطأ
          if (addedItem.error.includes('يوجد صنف بنفس الاسم')) {
            // عرض تنبيه بارز للصنف المكرر
            setDuplicateItemError(true);
            showAlert('danger', 'يوجد صنف بنفس الاسم بالفعل. الرجاء استخدام اسم آخر.', 6000);
          } else {
            showAlert('danger', addedItem.error || 'فشل في إضافة الصنف');
          }

          return;
        }

        // مسح التخزين المؤقت للأصناف
        if (window.api && window.api.items && window.api.items.clearCache) {
          console.log('مسح التخزين المؤقت للأصناف بعد إضافة صنف جديد');
          await window.api.items.clearCache('add-item-from-items-page', addedItem?.item?.id);
        }

        // إعادة تحميل الأصناف من قاعدة البيانات
        try {
          console.log('إعادة تحميل الأصناف بعد الإضافة...');

          // استخدام window.api.items.getAll مع تجاوز التخزين المؤقت
          const refreshedItems = await window.api.items.getAll(true);

          if (Array.isArray(refreshedItems)) {
            console.log('تم إعادة تحميل الأصناف بنجاح:', refreshedItems.length);
            setFilteredItems(refreshedItems);

            // استخدام الوظيفة الجديدة لتحديث الأصناف في جميع النوافذ
            if (window.api && window.api.items && window.api.items.refreshAllWindows) {
              console.log('استخدام window.api.items.refreshAllWindows لتحديث الأصناف في جميع النوافذ');

              // تحديث الأصناف في جميع النوافذ
              const refreshResult = await window.api.items.refreshAllWindows();
              console.log('نتيجة تحديث الأصناف في جميع النوافذ:', refreshResult);

              // تحديث الأصناف مرة أخرى بعد فترة قصيرة للتأكد من التحديث
              setTimeout(async () => {
                console.log('تحديث الأصناف في جميع النوافذ مرة أخرى بعد فترة قصيرة');
                await window.api.items.refreshAllWindows();
              }, 1000);

              // تحديث الأصناف مرة ثالثة بعد فترة أطول للتأكد من التحديث
              setTimeout(async () => {
                console.log('تحديث الأصناف في جميع النوافذ مرة ثالثة بعد فترة أطول');
                await window.api.items.refreshAllWindows();
              }, 2000);
            } else {
              console.warn('window.api.items.refreshAllWindows غير متوفرة، استخدام الطريقة القديمة');

              // إرسال حدث إضافة صنف جديد باستخدام window.api.on
              if (window.api && window.api.on) {
                console.log('إرسال حدث إضافة صنف جديد باستخدام window.api.on');
                window.api.on('item-added', addedItem.item);
              }
            }
          }
        } catch (reloadError) {
          console.error('خطأ في إعادة تحميل الأصناف بعد الإضافة:', reloadError);
          // لا نريد إيقاف العملية إذا فشل إعادة التحميل
        }

        // إغلاق النافذة المنبثقة بعد إضافة الصنف بنجاح
        if (isMounted.current) {
          // إعادة تعيين النموذج بشكل كامل
          resetFormCompletely();

          // إعادة تعيين حالة النموذج
          resetForm();

          // تنظيف أي قيم مؤقتة
          resetFormData({
            name: '',
            unit: ''
          });

          // إعادة تعيين الصنف الحالي إلى null
          setCurrentItem(null);

          // إغلاق النافذة المنبثقة
          setShowModal(false);
          showAlert('success', 'تم إضافة الصنف بنجاح');
        }
      } else {
        // تحديث صنف موجود - إرسال فقط اسم الصنف ووحدة القياس
        // إنشاء كائن جديد يحتوي فقط على الحقول المطلوبة
        const updatedItem = {
          // إضافة الاسم (سواء كان موجودًا أم لا)
          name: formData.name !== undefined && formData.name !== null ? formData.name.trim() : '',
          // إضافة وحدة القياس (سواء كانت موجودة أم لا)
          unit: formData.unit !== undefined && formData.unit !== null ? formData.unit.trim() : ''
        };

        // التأكد من أن الاسم ووحدة القياس ليست فارغة
        if (!updatedItem.name) {
          updatedItem.name = currentItem.name || 'صنف بدون اسم';
        }

        if (!updatedItem.unit) {
          updatedItem.unit = currentItem.unit || 'قطعة';
        }

        console.log('بيانات النموذج الأصلية:', formData);
        console.log('الصنف الحالي:', currentItem);
        console.log('الصنف المحدث:', updatedItem);

        // لا نحتاج إلى التحقق من وجود تحديثات لأننا سنترك ذلك للخادم
        // الخادم سيستخدم القيم الحالية إذا كانت القيم المرسلة فارغة

        console.log('Updated item:', updatedItem);

        // تحديث الصنف باستخدام واجهة API المباشرة
        console.log('تحديث الصنف في Items.js:', updatedItem);

        // التأكد من وجود معرف الصنف
        if (!currentItem || (!currentItem.id && !currentItem._id)) {
          console.error('معرف الصنف غير موجود في currentItem:', currentItem);
          showAlert('danger', 'معرف الصنف غير موجود');
          return;
        }

        const itemId = currentItem.id || currentItem._id;
        console.log('استدعاء window.api.items.update مع المعرف:', itemId);
        console.log('البيانات المرسلة للتحديث:', updatedItem);

        // الحصول على دور المستخدم الحالي بشكل صريح
        const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
        console.log('دور المستخدم الحالي:', currentUserRole);

        let result;
        try {
          // استخدام window.api.invoke مباشرة لتمرير المعلمات بشكل صريح
          console.log('استدعاء update-item مباشرة...');

          // التأكد من أن البيانات المرسلة صالحة وليست فارغة
          const itemData = {
            name: String(updatedItem.name || '').trim() || currentItem.name || 'صنف بدون اسم',
            unit: String(updatedItem.unit || '').trim() || currentItem.unit || 'قطعة'
          };

          console.log('البيانات المرسلة للتحديث بعد التنسيق:', itemData);

          // التأكد من أن البيانات ليست فارغة
          if (!itemData.name || !itemData.unit) {
            throw new Error('بيانات الصنف غير مكتملة');
          }

          // استدعاء واجهة API مباشرة - استخدام window.api.items.update بدلاً من window.api.invoke
          result = await window.api.items.update(itemId, itemData);
          console.log('نتيجة update-item:', result);

          // التحقق من نجاح العملية
          if (!result || result.error) {
            console.error('خطأ في استدعاء update-item:', result?.error || 'سبب غير معروف');
            throw new Error(result?.error || 'فشل في تحديث الصنف');
          }
        } catch (updateError) {
          console.error('خطأ في تحديث الصنف:', updateError);

          // محاولة أخيرة باستخدام طريقة مختلفة
          try {
            console.log('محاولة أخيرة باستخدام طريقة بديلة...');

            // استخدام كائن بسيط جدًا للتحديث مع التأكد من عدم وجود قيم فارغة
            const simpleData = {
              name: String(updatedItem.name || '').trim() || currentItem.name || 'صنف بدون اسم',
              unit: String(updatedItem.unit || '').trim() || currentItem.unit || 'قطعة'
            };

            console.log('بيانات التحديث البسيطة:', simpleData);

            // التأكد من أن البيانات ليست فارغة
            if (!simpleData.name || !simpleData.unit) {
              throw new Error('بيانات الصنف غير مكتملة');
            }

            // استدعاء واجهة API مباشرة - استخدام window.api.items.update بدلاً من window.api.invoke
            result = await window.api.items.update(itemId, simpleData);
            console.log('نتيجة المحاولة البديلة:', result);

            // التحقق من نجاح العملية
            if (!result || result.error) {
              console.error('خطأ في المحاولة البديلة:', result?.error || 'سبب غير معروف');
              throw new Error(result?.error || 'فشل في تحديث الصنف');
            }
          } catch (finalError) {
            console.error('فشلت جميع محاولات تحديث الصنف:', finalError);
            throw finalError;
          }
        }

        // التحقق من وجود خطأ في الصلاحيات
        if (result && result.error && result.error.includes('ليس لديك صلاحية')) {
          console.error('خطأ في صلاحيات المستخدم:', result.error);
          showAlert('danger', result.error);
          return;
        }

        // تحديث قائمة الأصناف المعروضة
        setFilteredItems(prevItems =>
          prevItems.map(item =>
            (item.id === itemId || item._id === itemId)
              ? { ...item, name: updatedItem.name, unit: updatedItem.unit }
              : item
          )
        );

        // إعادة تحميل الأصناف من قاعدة البيانات
        try {
          console.log('إعادة تحميل الأصناف بعد التحديث...');

          // استخدام window.api.items.getAll
          const refreshedItems = await window.api.items.getAll();

          if (Array.isArray(refreshedItems)) {
            console.log('تم إعادة تحميل الأصناف بنجاح:', refreshedItems.length);
            setFilteredItems(refreshedItems);
          }
        } catch (reloadError) {
          console.error('خطأ في إعادة تحميل الأصناف بعد التحديث:', reloadError);
          // لا نريد إيقاف العملية إذا فشل إعادة التحميل
        }

        // إغلاق النافذة المنبثقة بعد تحديث الصنف بنجاح
        if (isMounted.current) {
          // إعادة تعيين النموذج بشكل كامل
          resetFormCompletely();

          // إعادة تعيين حالة النموذج
          resetForm();

          // تنظيف أي قيم مؤقتة
          resetFormData({
            name: '',
            unit: ''
          });

          // إعادة تعيين الصنف الحالي إلى null
          setCurrentItem(null);

          // إغلاق النافذة المنبثقة
          setShowModal(false);
          showAlert('success', 'تم تحديث الصنف بنجاح');
        }
      }
    } catch (err) {
      console.error('Error saving item:', err);
      showAlert('danger', 'فشل في حفظ الصنف: ' + (err.message || 'خطأ غير معروف'));
    }
  };

  const showAlert = (type, message, duration = 3000) => {
    if (isMounted.current) {
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء الإشعار بعد المدة المحددة وتخزين مرجع المؤقت
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;

          // إعادة تعيين حالة خطأ الصنف المكرر
          if (type === 'danger') {
            setDuplicateItemError(false);
          }
        }
      }, duration);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  // التحقق من صلاحيات المستخدم
  const currentUserRole = localStorage.getItem('currentUserRole');
  const isViewer = currentUserRole === 'viewer';
  const isAdmin = currentUserRole === 'admin';

  return (
    <div className="items-page">
      <div className="items-header">
        <h1>إدارة الأصناف</h1>
        <p>إضافة وتعديل وحذف الأصناف في المخزن</p>
      </div>

      <Card
        title="الأصناف"
        icon={<FaBoxes />}
        actions={
          <div className="items-actions">
            {/* تم إزالة زر المزامنة اليدوية */}
            {!isViewer && (
              <Button
                variant="secondary"
                icon={<FaFileExcel />}
                onClick={() => setShowImporter(!showImporter)}
              >
                استيراد/تصدير
              </Button>
            )}
            {!isViewer && (
              <Button
                variant="primary"
                icon={<FaPlus />}
                onClick={handleAddItem}
              >
                إضافة صنف جديد
              </Button>
            )}
          </div>
        }
      >
        {alert.show && (
          <div className={`alert alert-${alert.type}`}>
            {alert.message}
          </div>
        )}

        {error && (
          <div className="alert alert-danger">
            {error}
          </div>
        )}

        {showImporter && (
          <div className="importer-container">
            <h4 className="importer-title">استيراد وتصدير الأصناف</h4>
            <ItemImporter
              onImport={handleImportItems}
              existingItems={items}
            />
          </div>
        )}

        <div className="search-container">
          <FaSearch className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="بحث عن صنف..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="items-table-container">
          <DataTable
            columns={[
              {
                header: '#',
                accessor: 'index',
                cell: (_, index) => index + 1,
                style: { width: '50px' }
              },
              {
                header: 'الاسم',
                accessor: 'name',
                cell: (item) => (
                  <div className="item-name">{item.name}</div>
                )
              },
              {
                header: 'وحدة القياس',
                accessor: 'unit',
                cell: (item) => (
                  <div className="item-unit">{item.unit || '-'}</div>
                )
              },

              {
                header: 'الإجراءات',
                accessor: 'actions',
                cell: (item) => (
                  <div className="item-actions">
                    {!isViewer && (
                      <Button
                        variant="info"
                        size="sm"
                        icon={<FaEdit />}
                        title="تعديل"
                        onClick={() => handleEditItem(item)}
                      />
                    )}
                    {!isViewer && (
                      <Button
                        variant="danger"
                        size="sm"
                        icon={<FaTrash />}
                        title="حذف"
                        onClick={() => handleDeleteItem(item.id)}
                      />
                    )}
                  </div>
                )
              }
            ]}
            data={filteredItems}
            pagination={true}
            pageSize={10}
            searchable={false}
            emptyMessage={
              searchTerm
                ? `لا توجد نتائج للبحث عن "${searchTerm}"`
                : "لا توجد أصناف مسجلة. قم بإضافة صنف جديد."
            }
          />
        </div>
      </Card>

      {/* نافذة إضافة/تعديل الصنف */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          console.log('Modal closing...');

          // تنظيف النموذج بشكل كامل عند إغلاق النافذة المنبثقة
          resetFormCompletely();

          // إعادة تعيين حالة النموذج
          resetForm();

          // تنظيف أي قيم مؤقتة
          resetFormData({
            name: '',
            unit: ''
          });

          // إعادة تعيين الصنف الحالي إلى null
          setCurrentItem(null);

          // إعادة تعيين حالة الإكمال التلقائي
          setShowAutocomplete(true);
          setSelectedItem(null);
          setDuplicateItemError(false);

          // إغلاق النافذة المنبثقة
          setShowModal(false);
        }}
        title={modalMode === 'add' ? 'إضافة صنف جديد' : 'تعديل الصنف'}
        size="lg"
        footer={
          <>
            <Button
              variant="light"
              onClick={() => {
                console.log('Cancel button clicked...');

                // تنظيف النموذج بشكل كامل عند النقر على زر الإلغاء
                resetFormCompletely();

                // إعادة تعيين حالة النموذج
                resetForm();

                // تنظيف أي قيم مؤقتة
                resetFormData({
                  name: '',
                  unit: ''
                });

                // إعادة تعيين الصنف الحالي إلى null
                setCurrentItem(null);

                // إعادة تعيين حالة الإكمال التلقائي
                setShowAutocomplete(true);
                setSelectedItem(null);
                setDuplicateItemError(false);

                // إغلاق النافذة المنبثقة
                setShowModal(false);
              }}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
            >
              {modalMode === 'add' ? 'إضافة' : 'تحديث'}
            </Button>
          </>
        }
      >
        <form ref={formRef} className="item-form" onSubmit={(e) => e.preventDefault()}>
          {duplicateItemError && (
            <div className="duplicate-item-error">
              <div className="duplicate-item-error-icon">
                <FaExclamationTriangle />
              </div>
              <div className="duplicate-item-error-message">
                <strong>تنبيه!</strong> يوجد صنف بنفس الاسم بالفعل في قاعدة البيانات.
                <br />
                الرجاء استخدام اسم مختلف للصنف الجديد.
              </div>
            </div>
          )}

          <div className="form-group full-width">
            <label className="form-label">اسم الصنف *</label>
            {showAutocomplete ? (
              <div className={`autocomplete-container ${duplicateItemError ? 'error-container' : ''}`}>
                <ItemAutocomplete
                  onSelect={handleItemAutocompleteSelect}
                  onChange={(newValue) => {
                    // إعادة تعيين حالة خطأ الصنف المكرر عند تغيير النص
                    if (duplicateItemError && newValue) {
                      setDuplicateItemError(false);
                    }
                    // تحديث اسم الصنف في النموذج
                    updateFormData({ name: newValue });

                    // إعادة تمكين حقول النموذج في حالة كانت معطلة
                    setTimeout(() => {
                      enableFormFields();
                    }, 100);
                  }}
                  placeholder="اكتب اسم الصنف للبحث..."
                  className="item-form-autocomplete"
                  hideIcon={true}
                />
                <button
                  type="button"
                  className="switch-input-button"
                  onClick={() => {
                    setShowAutocomplete(false);
                    // إعادة تعيين حالة خطأ الصنف المكرر
                    if (duplicateItemError) {
                      setDuplicateItemError(false);
                    }
                  }}
                  title="التبديل إلى الإدخال اليدوي"
                >
                  <FaEdit />
                </button>
              </div>
            ) : (
              <div className="manual-input-container">
                <input
                  type="text"
                  name="name"
                  className={`form-control ${duplicateItemError ? 'error-input' : ''}`}
                  value={formData.name}
                  onChange={(e) => {
                    handleInputChange(e);
                    // إعادة تعيين حالة خطأ الصنف المكرر عند تغيير الاسم
                    if (duplicateItemError) {
                      setDuplicateItemError(false);
                    }

                    // إعادة تمكين حقول النموذج في حالة كانت معطلة
                    setTimeout(() => {
                      enableFormFields();
                    }, 100);
                  }}
                  onFocus={() => {
                    // إعادة تمكين حقول النموذج عند التركيز
                    enableFormFields();
                  }}
                  onClick={() => {
                    // إعادة تمكين حقول النموذج عند النقر
                    enableFormFields();
                  }}
                  required
                  placeholder="أدخل اسم الصنف"
                />
                <button
                  type="button"
                  className="switch-input-button"
                  onClick={() => setShowAutocomplete(true)}
                  title="التبديل إلى البحث التلقائي"
                >
                  <FaSearch />
                </button>
              </div>
            )}
          </div>

          <div className="form-group">
            <label className="form-label">وحدة القياس *</label>
            <div className="unit-buttons-container">
              <button
                type="button"
                className={`unit-button ${formData.unit === 'قطعة' ? 'active' : ''}`}
                onClick={() => handleUnitSelect('قطعة')}
              >
                قطعة
              </button>
              <button
                type="button"
                className={`unit-button ${formData.unit === 'باكو' ? 'active' : ''}`}
                onClick={() => handleUnitSelect('باكو')}
              >
                باكو
              </button>
              <button
                type="button"
                className={`unit-button ${formData.unit === 'كيلو' ? 'active' : ''}`}
                onClick={() => handleUnitSelect('كيلو')}
              >
                كيلو
              </button>
              <button
                type="button"
                className={`unit-button ${formData.unit === 'متر طولي' ? 'active' : ''}`}
                onClick={() => handleUnitSelect('متر طولي')}
              >
                متر طولي
              </button>
            </div>
          </div>


        </form>
      </Modal>
    </div>
  );
};

export default Items;