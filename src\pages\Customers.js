import React, { useState, useEffect, Fragment, useRef } from 'react';
import {
  FaPlus, FaEdit, FaTrash, FaSearch, FaPhone,
  FaUsers, FaUserFriends, FaUserAlt, FaLink,
  FaWallet, FaHistory, FaUser, FaShoppingCart,
  FaReceipt, FaTimes, FaMoneyBillWave, FaClipboardList,
  FaFileInvoice, FaUndo, FaChevronDown, FaInfoCircle
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import EnhancedCustomerSalesHistory from '../components/EnhancedCustomerSalesHistory';
import EnhancedReturnForm from '../components/EnhancedReturnForm';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
// import DataTable from '../components/DataTable';
import FormattedCurrency from '../components/FormattedCurrency';
import ModernItemAutocomplete from '../components/ModernItemAutocomplete';
import useFormReset from '../hooks/useFormReset';
import './Customers.css';
import './CustomerReturnSimple.css';

const Customers = () => {
  const {
    customers,
    items,
    inventory,
    transactions,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    addTransaction,
    loading,
    setInventory
  } = useApp();

  // Variable no utilizada - considere eliminarla o usarla
  const [searchTerm, setSearchTerm] = useState('');
  // Variable no utilizada - considere eliminarla o usarla
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [showModal, setShowModal] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [currentCustomer, setCurrentCustomer] = useState(null);
  // Variable no utilizada - considere eliminarla o usarla
  const [selectedType, setSelectedType] = useState('all'); // 'all', 'regular', 'sub', 'normal'
  // Variable no utilizada - considere eliminarla o usarla
  const [selectedParent, setSelectedParent] = useState(null);
  // Variable no utilizada - considere eliminarla o usarla
  const [showSalesHistory, setShowSalesHistory] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [selectedCustomerId, setSelectedCustomerId] = useState(null);
  // Variable no utilizada - considere eliminarla o usarla
  const [expandedCustomers, setExpandedCustomers] = useState({});
  // بيانات نموذج العميل
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    customer_type: 'normal', // normal, regular, sub
    parent_id: null,
    contact_person: ''
  });
  // Variable no utilizada - considere eliminarla o usarla
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  // متغير حالة للتحكم في حالة الحذف
  const [isDeleting, setIsDeleting] = useState(false);
  // متغير حالة للتحكم في حالة التحميل
  const [isLoading, setIsLoading] = useState(false);

  // متغيرات حالة البيع للعملاء
  const [showSalesModal, setShowSalesModal] = useState(false);
  const [saleItems, setSaleItems] = useState([]);
  const [currentItem, setCurrentItem] = useState({
    item_id: '',
    quantity: 1,
    price: 0
  });
  const [itemSearchTerm, setItemSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);
  const [invoiceData, setInvoiceData] = useState({
    invoice_number: '',
    transaction_date: new Date().toISOString().split('T')[0]
  });
  const [isCreatingSubInvoice, setIsCreatingSubInvoice] = useState(false);

  // مرجع للنموذج
  const formRef = useRef(null);

  // مرجع لمؤقت التنبيهات
  const alertTimeoutRef = useRef(null);

  // تصفية العملاء بناءً على مصطلح البحث ونوع العميل
  useEffect(() => {
    const filterCustomers = async () => {
      try {
        // الحصول على جميع العملاء من قاعدة البيانات
        const allCustomers = await window.api.customers.getAll();

        if (Array.isArray(allCustomers) && allCustomers.length > 0) {
          console.log('تم تحديث قائمة العملاء من قاعدة البيانات:', allCustomers.length);

          // تحديث قائمة العملاء الكاملة في سياق التطبيق
          if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
            window.api.customers.updateCustomersContext(allCustomers);
          }

          let filtered = allCustomers;

          // إذا كان العرض لجميع العملاء، نستبعد العملاء الفرعيين
          if (selectedType === 'all') {
            filtered = filtered.filter(customer => customer.customer_type !== 'sub');
          } else {
            // تصفية حسب النوع
            filtered = filtered.filter(customer => customer.customer_type === selectedType);
          }

          // تصفية العملاء الفرعيين حسب العميل الدائم
          if (selectedType === 'sub' && selectedParent) {
            filtered = filtered.filter(customer => {
              // تحويل parent_id إلى نص ورقم للمقارنة
              const customerParentId = customer.parent_id;
              const customerParentIdStr = String(customerParentId);
              const customerParentIdNum = Number(customerParentId);

              const selectedParentStr = String(selectedParent);
              const selectedParentNum = Number(selectedParent);

              return (
                customerParentId === selectedParent ||
                customerParentId === selectedParentNum ||
                customerParentIdStr === selectedParentStr ||
                customerParentIdNum === selectedParentNum
              );
            });
          }

          // تصفية حسب مصطلح البحث
          if (searchTerm) {
            filtered = filtered.filter(customer =>
              customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              (customer.contact_person && customer.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||
              (customer.phone && customer.phone.includes(searchTerm))
            );
          }

          // استبعاد العملاء الفرعيين من القائمة الرئيسية
          if (selectedType !== 'sub') {
            // إنشاء مجموعة لتخزين معرفات العملاء الفرعيين
            const subCustomerIds = new Set();

            // جمع معرفات جميع العملاء الفرعيين
            allCustomers.forEach(customer => {
              if (customer.customer_type === 'sub') {
                subCustomerIds.add(customer.id);
                subCustomerIds.add(String(customer.id));
                if (customer._id) {
                  subCustomerIds.add(customer._id);
                  subCustomerIds.add(String(customer._id));
                }
              }
            });

            // استبعاد العملاء الفرعيين من القائمة
            filtered = filtered.filter(customer => {
              return !subCustomerIds.has(customer.id) &&
                     !subCustomerIds.has(String(customer.id)) &&
                     (!customer._id || (!subCustomerIds.has(customer._id) && !subCustomerIds.has(String(customer._id))));
            });
          }

          console.log('عدد العملاء بعد التصفية:', filtered.length);
          setFilteredCustomers(filtered);
        } else if (customers && customers.length > 0) {
          // استخدام القائمة الحالية إذا فشل الحصول على العملاء من قاعدة البيانات
          console.log('استخدام قائمة العملاء الحالية:', customers.length);

          let filtered = customers;

          // إذا كان العرض لجميع العملاء، نستبعد العملاء الفرعيين
          if (selectedType === 'all') {
            filtered = filtered.filter(customer => customer.customer_type !== 'sub');
          } else {
            // تصفية حسب النوع
            filtered = filtered.filter(customer => customer.customer_type === selectedType);
          }

          // تصفية العملاء الفرعيين حسب العميل الدائم
          if (selectedType === 'sub' && selectedParent) {
            filtered = filtered.filter(customer => {
              // تحويل parent_id إلى نص ورقم للمقارنة
              const customerParentId = customer.parent_id;
              const customerParentIdStr = String(customerParentId);
              const customerParentIdNum = Number(customerParentId);

              const selectedParentStr = String(selectedParent);
              const selectedParentNum = Number(selectedParent);

              return (
                customerParentId === selectedParent ||
                customerParentId === selectedParentNum ||
                customerParentIdStr === selectedParentStr ||
                customerParentIdNum === selectedParentNum
              );
            });
          }

          // تصفية حسب مصطلح البحث
          if (searchTerm) {
            filtered = filtered.filter(customer =>
              customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              (customer.contact_person && customer.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||
              (customer.phone && customer.phone.includes(searchTerm))
            );
          }

          console.log('عدد العملاء بعد التصفية (من القائمة الحالية):', filtered.length);
          setFilteredCustomers(filtered);
        } else {
          setFilteredCustomers([]);
        }
      } catch (error) {
        console.error('خطأ في تحديث قائمة العملاء:', error);

        // في حالة الخطأ، نستخدم القائمة الحالية
        if (customers && customers.length > 0) {
          let filtered = customers;

          // تطبيق نفس منطق التصفية على القائمة الحالية
          if (selectedType === 'all') {
            filtered = filtered.filter(customer => customer.customer_type !== 'sub');
          } else {
            filtered = filtered.filter(customer => customer.customer_type === selectedType);
          }

          if (selectedType === 'sub' && selectedParent) {
            filtered = filtered.filter(customer => {
              const customerParentId = customer.parent_id;
              return String(customerParentId) === String(selectedParent) ||
                     Number(customerParentId) === Number(selectedParent);
            });
          }

          if (searchTerm) {
            filtered = filtered.filter(customer =>
              customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              (customer.phone && customer.phone.includes(searchTerm))
            );
          }

          setFilteredCustomers(filtered);
        } else {
          setFilteredCustomers([]);
        }
      }
    };

    // استدعاء وظيفة تصفية العملاء
    filterCustomers();
  }, [customers, searchTerm, selectedType, selectedParent]);

  // تصفية الأصناف بناءً على مصطلح البحث
  useEffect(() => {
    if (items && items.length > 0) {
      if (itemSearchTerm.trim() === '') {
        setFilteredItems(items);
      } else {
        const filtered = items.filter(item =>
          item.name.toLowerCase().includes(itemSearchTerm.toLowerCase()) ||
          (item.id && item.id.toString().includes(itemSearchTerm))
        );
        setFilteredItems(filtered);
      }
    } else {
      setFilteredItems([]);
    }
  }, [items, itemSearchTerm]);

  // استخدام هوك إعادة تعيين النموذج
  const { isMounted, enableFormFields, enableFormFieldsAfterDelete } = useFormReset(formRef);

  // وظيفة إعادة تعيين النموذج - تستخدم فقط عند الضغط على زر الإنهاء
  const resetForm = () => {
    if (isMounted.current) {
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        customer_type: 'normal',
        parent_id: null,
        contact_person: ''
      });
      setCurrentCustomer(null);
    }
  };

  // تنظيف المؤقتات عند إلغاء تحميل المكون
  useEffect(() => {
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);

  // تحديث بيانات العملاء عند تحميل الصفحة
  useEffect(() => {
    // تم نقل وظيفة تحديث بيانات العملاء إلى useEffect الخاص بتصفية العملاء
    // لتجنب التداخل وتحسين الأداء
    console.log('تم تحميل صفحة العملاء');
  }, []);

  // فتح النموذج لإضافة عميل جديد
  const handleAddCustomer = () => {
    // أولاً نقوم بتعيين العميل الحالي إلى null
    setCurrentCustomer(null);

    // ثم نقوم بإعادة تعيين البيانات يدويًا
    setFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      customer_type: 'normal',
      parent_id: null,
      contact_person: ''
    });

    console.log('إضافة عميل جديد');

    // أخيرًا نقوم بفتح النموذج
    setShowModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // فتح النموذج لتعديل عميل موجود
  const handleEditCustomer = (customer) => {
    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isViewer = currentUserRole === 'viewer';

    // المشاهد فقط لا يمكنه تعديل العملاء (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لتعديل العملاء. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    // التحقق من وجود العميل
    if (!customer) {
      console.error('لا يوجد عميل للتعديل');
      showAlert('danger', 'لا يوجد عميل للتعديل');
      return;
    }

    // التحقق من وجود معرف العميل
    if (!customer.id && !customer._id) {
      console.error('معرف العميل غير موجود:', customer);
      showAlert('danger', 'معرف العميل غير موجود');
      return;
    }

    console.log('تعديل العميل:', customer);

    // أولاً نقوم بتعيين العميل الحالي
    setCurrentCustomer(customer);

    // ثم نقوم بتعيين البيانات
    setFormData({
      name: customer.name,
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.address || '',
      customer_type: customer.customer_type || 'normal',
      parent_id: customer.parent_id || null,
      contact_person: customer.contact_person || ''
    });

    // أخيرًا نقوم بفتح النموذج
    setShowModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // معالجة تغيير قيم النموذج
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;

    // التعامل مع الحقول الرقمية
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    } else if (name === 'parent_id') {
      // التعامل مع حقل العميل الدائم
      setFormData(prev => ({ ...prev, [name]: value === '' ? null : value }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // معالجة تغيير نوع العميل
  const handleCustomerTypeChange = (type) => {
    console.log('تغيير نوع العميل إلى:', type);
    setFormData(prev => {
      // إذا تم تغيير النوع من فرعي إلى نوع آخر، نقوم بإزالة العميل الدائم
      const parent_id = type === 'sub' ? prev.parent_id : null;

      // إذا تم تغيير النوع إلى عميل دائم، نتأكد من عدم وجود parent_id
      if (type === 'regular' && prev.parent_id) {
        console.log('تغيير النوع إلى عميل دائم وإزالة parent_id');
        return {
          ...prev,
          customer_type: 'regular',
          parent_id: null
        };
      }

      // إذا تم تغيير النوع إلى عميل فرعي، نتأكد من وجود parent_id
      if (type === 'sub' && !prev.parent_id) {
        console.log('تغيير النوع إلى عميل فرعي ولكن لم يتم تحديد العميل الدائم');
        // لا نقوم بتغيير النوع إلى فرعي إلا بعد تحديد العميل الدائم
        return {
          ...prev,
          customer_type: type
        };
      }

      const newFormData = {
        ...prev,
        customer_type: type,
        parent_id
      };
      console.log('بيانات النموذج الجديدة:', newFormData);
      console.log('نوع parent_id:', typeof newFormData.parent_id);
      console.log('قيمة parent_id:', newFormData.parent_id);
      return newFormData;
    });
  };

  // معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      console.log('بدء معالجة إرسال نموذج العميل:', formData);

      // التحقق من دور المستخدم الحالي
      const currentUserRole = localStorage.getItem('currentUserRole');
      const isViewer = currentUserRole === 'viewer';

      // المشاهد فقط لا يمكنه إضافة أو تعديل العملاء
      if (isViewer) {
        showAlert('danger', 'ليس لديك صلاحية لإضافة أو تعديل العملاء. المشاهد يمكنه فقط عرض البيانات.');
        return;
      }

      // التحقق من البيانات المدخلة
      if (!formData.name.trim()) {
        showAlert('danger', 'يرجى إدخال اسم العميل');
        return;
      }

      // التحقق من نوع العميل
      if (!['regular', 'sub', 'normal'].includes(formData.customer_type)) {
        showAlert('danger', 'نوع العميل غير صالح');
        return;
      }

      // التحقق من وجود العميل الدائم إذا كان العميل فرعي
      if (formData.customer_type === 'sub') {
        if (!formData.parent_id) {
          showAlert('danger', 'يجب تحديد العميل الدائم للعميل الفرعي');
          return;
        }

        console.log('تم تحديد العميل الدائم للعميل الفرعي:', formData.parent_id);

        // تحويل parent_id إلى رقم
        const parentIdNum = Number(formData.parent_id);
        console.log('معرف العميل الدائم كرقم:', parentIdNum);

        // تحديث parent_id في formData
        formData.parent_id = parentIdNum;

        // لا نقوم بالتحقق من وجود العميل الدائم هنا لتجنب المشكلة
      }

      // تحضير بيانات العميل
      const customerData = {
        ...formData,
        // التأكد من أن parent_id هو رقم أو null
        parent_id: formData.parent_id ? formData.parent_id : null,
        // التأكد من أن customer_type هو أحد القيم المسموح بها
        customer_type: ['normal', 'regular', 'sub'].includes(formData.customer_type)
          ? formData.customer_type
          : 'normal'
      };

      // تسجيل بيانات العميل قبل الإرسال
      console.log('بيانات العميل قبل الإرسال:', customerData);
      console.log('نوع parent_id:', typeof customerData.parent_id);
      console.log('قيمة parent_id:', customerData.parent_id);

      if (currentCustomer) {
        // تحديث عميل موجود
        console.log('تحديث عميل موجود:', currentCustomer);

        // التأكد من استخدام المعرف الصحيح
        const customerId = currentCustomer._id || currentCustomer.id;

        if (!customerId) {
          console.error('معرف العميل غير موجود:', currentCustomer);
          showAlert('danger', 'معرف العميل غير موجود');
          return;
        }

        // إظهار مؤشر التحميل
        setIsLoading(true);

        try {
          // تحضير بيانات العميل للتحديث
          const customerUpdateData = {
            ...customerData,
            id: customerId,
            _id: customerId
          };

          console.log('بيانات تحديث العميل:', customerUpdateData);
          console.log('معرف العميل للتحديث:', customerId);

          // استخدام window.api.customers.update مباشرة
          const updatedCustomer = await window.api.customers.update(customerId, customerUpdateData);

          console.log('استجابة تحديث العميل:', updatedCustomer);

          // إخفاء مؤشر التحميل
          setIsLoading(false);

          // التحقق من وجود خطأ في الصلاحيات
          if (updatedCustomer && updatedCustomer.error && updatedCustomer.error.includes('ليس لديك صلاحية')) {
            console.error('خطأ في صلاحيات المستخدم:', updatedCustomer.error);
            showAlert('danger', updatedCustomer.error);
            return;
          }

          // التحقق من نجاح العملية
          if (updatedCustomer && updatedCustomer.success) {
            console.log('تم تحديث العميل بنجاح:', updatedCustomer.customer);

            const updatedCustomerData = updatedCustomer.customer;

            // إظهار رسالة نجاح
            if (updatedCustomer.message && updatedCustomer.message.includes('لم يتم إجراء أي تغييرات')) {
              showAlert('info', updatedCustomer.message || 'لم يتم إجراء أي تغييرات على بيانات العميل');
            } else {
              showAlert('success', 'تم تحديث بيانات العميل بنجاح');
            }

            // إغلاق النموذج
            setShowModal(false);

            // إعادة تحميل العملاء من قاعدة البيانات فوراً
            try {
              console.log('إعادة تحميل العملاء بعد التحديث...');
              const refreshedCustomers = await window.api.customers.getAll();
              console.log('تم إعادة تحميل العملاء بنجاح:', refreshedCustomers.length);

              if (Array.isArray(refreshedCustomers)) {
                // تطبيق التصفية على العملاء المحدثة
                let filtered = refreshedCustomers;

                // إذا كان العرض لجميع العملاء، نستبعد العملاء الفرعيين
                if (selectedType === 'all') {
                  filtered = filtered.filter(customer => customer.customer_type !== 'sub');
                } else {
                  // تصفية حسب النوع
                  filtered = filtered.filter(customer => customer.customer_type === selectedType);
                }

                // تصفية العملاء الفرعيين حسب العميل الدائم
                if (selectedType === 'sub' && selectedParent) {
                  filtered = filtered.filter(customer => {
                    const customerParentId = customer.parent_id;
                    const customerParentIdStr = String(customerParentId);
                    const customerParentIdNum = Number(customerParentId);

                    const selectedParentStr = String(selectedParent);
                    const selectedParentNum = Number(selectedParent);

                    return (
                      customerParentId === selectedParent ||
                      customerParentId === selectedParentNum ||
                      customerParentIdStr === selectedParentStr ||
                      customerParentIdNum === selectedParentNum
                    );
                  });
                }

                // تحديث قائمة العملاء المفلترة
                setFilteredCustomers(filtered);

                // تحديث سياق العملاء في الذاكرة
                if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
                  window.api.customers.updateCustomersContext(refreshedCustomers);
                  console.log('تم تحديث سياق العملاء في الذاكرة بعد تعديل العميل');
                }

                // تحديث العميل المحدد في القائمة
                const updatedCustomerInList = refreshedCustomers.find(c =>
                  c.id === customerId || c._id === customerId
                );

                if (updatedCustomerInList) {
                  console.log('تم العثور على العميل المحدث في القائمة:', updatedCustomerInList);
                } else {
                  console.warn('لم يتم العثور على العميل المحدث في القائمة المحدثة');
                }
              }
            } catch (reloadError) {
              console.error('خطأ في إعادة تحميل العملاء بعد التحديث:', reloadError);

              // في حالة فشل إعادة التحميل، نقوم بتحديث العميل في القائمة الحالية
              setFilteredCustomers(prevCustomers =>
                prevCustomers.map(c =>
                  (c.id === customerId || c._id === customerId) ? updatedCustomerData : c
                )
              );
            }

            // إعادة تعيين النموذج
            resetForm();
          } else {
            console.error('فشل في تحديث العميل:', updatedCustomer);
            showAlert('danger', updatedCustomer && updatedCustomer.error ? updatedCustomer.error : 'فشل في تحديث بيانات العميل');
          }
        } catch (updateError) {
          // إخفاء مؤشر التحميل في حالة الخطأ
          setIsLoading(false);

          console.error('خطأ أثناء تحديث العميل:', updateError);
          showAlert('danger', updateError.message || 'فشل في تحديث بيانات العميل');
        }
      } else {
        // إضافة عميل جديد
        console.log('إضافة عميل جديد');
        console.log('بيانات العميل الجديد:', customerData);
        console.log('نوع العميل:', customerData.customer_type);

        try {
          const newCustomer = await addCustomer(customerData);
          console.log('تم إضافة العميل بنجاح:', newCustomer);

          // إعادة تحميل العملاء من قاعدة البيانات فوراً
          try {
            console.log('إعادة تحميل العملاء بعد الإضافة...');
            const refreshedCustomers = await window.api.customers.getAll();
            console.log('تم إعادة تحميل العملاء بنجاح:', refreshedCustomers.length);

            if (Array.isArray(refreshedCustomers)) {
              // تطبيق التصفية على العملاء المحدثة
              let filtered = refreshedCustomers;

              // إذا كان العرض لجميع العملاء، نستبعد العملاء الفرعيين
              if (selectedType === 'all') {
                filtered = filtered.filter(customer => customer.customer_type !== 'sub');
              } else {
                // تصفية حسب النوع
                filtered = filtered.filter(customer => customer.customer_type === selectedType);
              }

              // تصفية العملاء الفرعيين حسب العميل الدائم
              if (selectedType === 'sub' && selectedParent) {
                filtered = filtered.filter(customer => {
                  const customerParentId = customer.parent_id;
                  const customerParentIdStr = String(customerParentId);
                  const customerParentIdNum = Number(customerParentId);

                  const selectedParentStr = String(selectedParent);
                  const selectedParentNum = Number(selectedParent);

                  return (
                    customerParentId === selectedParent ||
                    customerParentId === selectedParentNum ||
                    customerParentIdStr === selectedParentStr ||
                    customerParentIdNum === selectedParentNum
                  );
                });
              }

              // تحديث قائمة العملاء المفلترة
              setFilteredCustomers(filtered);

              // تحديث سياق العملاء في الذاكرة
              if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
                window.api.customers.updateCustomersContext(refreshedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة بعد إضافة عميل جديد');
              }
            }
          } catch (reloadError) {
            console.error('خطأ في إعادة تحميل العملاء بعد الإضافة:', reloadError);
          }

          showAlert('success', 'تم إضافة العميل بنجاح');
        } catch (addError) {
          console.error('خطأ في إضافة العميل:', addError);
          showAlert('danger', addError.message || 'فشل في إضافة العميل');
        }
      }

      // إغلاق النموذج وإعادة تعيين البيانات
      if (isMounted.current) {
        // إعادة تعيين النموذج بعد الانتهاء من العملية
        resetForm();

        // إغلاق النموذج
        setShowModal(false);
      }
    } catch (err) {
      console.error('خطأ في حفظ بيانات العميل:', err);
      showAlert('danger', err.message || 'فشل في حفظ بيانات العميل');
    }
  };

  // معالجة حذف عميل
  const handleDeleteCustomer = async (id) => {
    if (!id) {
      console.error('معرف العميل غير صالح:', id);
      showAlert('danger', 'معرف العميل غير صالح');
      return;
    }

    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isViewer = currentUserRole === 'viewer';
    const isAdmin = currentUserRole === 'admin' || currentUserRole === 'manager';

    // المشاهد فقط لا يمكنه حذف العملاء (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لحذف العملاء. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        console.log('محاولة حذف العميل بالمعرف:', id);
        console.log('دور المستخدم الحالي:', currentUserRole, 'هل هو مدير:', isAdmin);

        // إظهار مؤشر التحميل
        setIsDeleting(true);

        // تحويل المعرف إلى نص
        const customerId = String(id);

        // استدعاء وظيفة الحذف مباشرة من واجهة API
        console.log('استدعاء window.api.customers.delete مع المعرف:', customerId);
        const result = await window.api.customers.delete(customerId);

        // إخفاء مؤشر التحميل
        setIsDeleting(false);

        console.log('نتيجة حذف العميل:', result);

        // التحقق من نجاح العملية
        if (!result || !result.success) {
          // التحقق مما إذا كان العميل له عملاء فرعيين
          if (result && result.hasSubCustomers) {
            console.error('لا يمكن حذف العميل لأنه يحتوي على عملاء فرعيين:', result.error);
            showAlert('danger', result.error || `لا يمكن حذف العميل لأنه يحتوي على ${result.subCustomersCount} عميل فرعي. يجب حذف العملاء الفرعيين أولاً.`);
            return;
          }

          // التحقق مما إذا كان العميل له معاملات
          if (result && result.hasTransactions) {
            console.error('لا يمكن حذف العميل لأنه مرتبط بمعاملات:', result.error);
            showAlert('danger', result.error || `لا يمكن حذف العميل لأنه مرتبط بـ ${result.transactionsCount} معاملة.`);
            return;
          }

          // التحقق من صلاحيات المستخدم
          if (result && result.error && result.error.includes('ليس لديك صلاحية')) {
            console.error('خطأ في صلاحيات المستخدم:', result.error);
            showAlert('danger', result.error);
            return;
          }

          // خطأ آخر غير متعلق بالعملاء الفرعيين أو المعاملات أو الصلاحيات
          console.error('فشل في حذف العميل:', result ? result.error : 'سبب غير معروف');
          showAlert('danger', result && result.error ? result.error : 'فشل في حذف العميل');
          return;
        }

        // تحديث قائمة العملاء المفلترة بعد الحذف بشكل فوري
        setFilteredCustomers(prevCustomers => {
          const updatedCustomers = prevCustomers.filter(c => c.id !== id && c._id !== id);
          console.log('تم تحديث قائمة العملاء المفلترة بعد الحذف، عدد العملاء المتبقي:', updatedCustomers.length);
          return updatedCustomers;
        });

        // إعادة تعيين حالة التوسيع للعميل المحذوف
        setExpandedCustomers(prev => {
          const newState = { ...prev };
          delete newState[id];
          return newState;
        });

        // إعادة تحميل العملاء من قاعدة البيانات بشكل متزامن
        try {
          console.log('إعادة تحميل العملاء بعد الحذف...');

          // استخدام Promise.all لضمان اكتمال جميع العمليات
          await Promise.all([
            // تأخير أطول لضمان اكتمال عملية الحذف في قاعدة البيانات
            new Promise(resolve => setTimeout(resolve, 1000)),

            // استدعاء وظيفة الحصول على العملاء
            window.api.customers.getAll().then(refreshedCustomers => {
              console.log('تم إعادة تحميل العملاء بنجاح:', refreshedCustomers.length);
              if (Array.isArray(refreshedCustomers)) {
                // تحديث قائمة العملاء المفلترة
                setFilteredCustomers(refreshedCustomers);

                // تحديث سياق العملاء في الذاكرة
                if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
                  window.api.customers.updateCustomersContext(refreshedCustomers);
                  console.log('تم تحديث سياق العملاء في الذاكرة بعد حذف العميل');
                }
              }
            })
          ]);

          // إعادة تحميل الصفحة بعد فترة قصيرة لضمان تحديث واجهة المستخدم
          setTimeout(() => {
            console.log('إعادة تحميل العملاء مرة أخرى للتأكد من التحديث...');
            window.api.customers.getAll().then(refreshedCustomers => {
              if (Array.isArray(refreshedCustomers)) {
                // تحديث قائمة العملاء المفلترة
                setFilteredCustomers(refreshedCustomers);
                console.log('تم تحديث قائمة العملاء مرة أخرى بعد الحذف، عدد العملاء:', refreshedCustomers.length);

                // إعادة تحديث حالة التوسيع لجميع العملاء
                const regularCustomerIds = refreshedCustomers
                  .filter(c => c.customer_type === 'regular')
                  .map(c => c.id || c._id);

                // إزالة أي عملاء محذوفين من حالة التوسيع
                setExpandedCustomers(prev => {
                  const newState = { ...prev };
                  Object.keys(newState).forEach(customerId => {
                    if (!regularCustomerIds.includes(Number(customerId)) && !regularCustomerIds.includes(customerId)) {
                      delete newState[customerId];
                    }
                  });
                  return newState;
                });
              }
            }).catch(error => {
              console.error('خطأ في إعادة تحميل العملاء مرة أخرى:', error);
            });
          }, 2000);
        } catch (reloadError) {
          console.error('خطأ في إعادة تحميل العملاء بعد الحذف:', reloadError);
          // لا نريد إيقاف العملية إذا فشل إعادة التحميل
        }

        console.log('تم حذف العميل بنجاح');
        showAlert('success', 'تم حذف العميل بنجاح');

        // استخدام الوظيفة المحسنة لإعادة تمكين حقول الإدخال بعد الحذف
        console.log('استدعاء enableFormFieldsAfterDelete بعد عملية حذف العميل...');
        enableFormFieldsAfterDelete();
      } catch (err) {
        // إخفاء مؤشر التحميل في حالة الخطأ
        setIsDeleting(false);
        console.error('خطأ في handleDeleteCustomer:', err);
        showAlert('danger', err.message || 'فشل في حذف العميل');
      }
    }
  };

  // الحصول على العملاء الدائمين
  const getRegularCustomers = () => {
    console.log('جميع العملاء:', filteredCustomers);
    const regularCustomers = filteredCustomers.filter(c => c && c.customer_type === 'regular');
    console.log('العملاء الدائمين:', regularCustomers);
    return regularCustomers;
  };

  // الحصول على العملاء الفرعيين لعميل دائم
  const getSubCustomersForParent = (parentId) => {
    if (!parentId) return [];

    // تحويل parentId إلى نص ورقم للمقارنة المتسقة
    const parentIdStr = String(parentId);
    const parentIdNum = Number(parentId);

    console.log('البحث عن العملاء الفرعيين للعميل الدائم:', parentId);

    // البحث في جميع العملاء المتاحين (وليس فقط المفلترة)
    // هذا يضمن أننا نجد جميع العملاء الفرعيين حتى لو لم يكونوا في القائمة المفلترة الحالية
    const allAvailableCustomers = [...customers];
    console.log('عدد العملاء المتاحين للبحث:', allAvailableCustomers.length);

    // إنشاء مجموعة لتخزين معرفات العملاء الفرعيين المضافة بالفعل
    const addedSubCustomerIds = new Set();

    // تصفية العملاء الفرعيين
    const subCustomers = [];

    allAvailableCustomers.forEach(c => {
      if (!c || c.customer_type !== 'sub') return;

      // تحويل parent_id إلى نص ورقم للمقارنة
      const cParentIdStr = c.parent_id ? String(c.parent_id) : null;
      const cParentIdNum = c.parent_id ? Number(c.parent_id) : null;

      const isMatch = (
        c.parent_id === parentId ||
        c.parent_id === parentIdNum ||
        cParentIdStr === parentIdStr ||
        cParentIdNum === parentIdNum
      );

      if (isMatch) {
        // التحقق من عدم وجود العميل الفرعي بالفعل في القائمة
        const customerId = c.id || c._id;
        if (!addedSubCustomerIds.has(customerId)) {
          addedSubCustomerIds.add(customerId);
          subCustomers.push(c);
        }
      }
    });

    console.log('تم العثور على', subCustomers.length, 'عميل فرعي للعميل الدائم', parentId);

    // إذا لم يتم العثور على أي عملاء فرعيين، نحاول البحث في filteredCustomers كاحتياطي
    if (subCustomers.length === 0) {
      console.log('محاولة البحث في القائمة المفلترة كاحتياطي');

      filteredCustomers.forEach(c => {
        if (!c || c.customer_type !== 'sub') return;

        // تحويل parent_id إلى نص ورقم للمقارنة
        const cParentIdStr = c.parent_id ? String(c.parent_id) : null;
        const cParentIdNum = c.parent_id ? Number(c.parent_id) : null;

        const isMatch = (
          c.parent_id === parentId ||
          c.parent_id === parentIdNum ||
          cParentIdStr === parentIdStr ||
          cParentIdNum === parentIdNum
        );

        if (isMatch) {
          // التحقق من عدم وجود العميل الفرعي بالفعل في القائمة
          const customerId = c.id || c._id;
          if (!addedSubCustomerIds.has(customerId)) {
            addedSubCustomerIds.add(customerId);
            subCustomers.push(c);
          }
        }
      });

      console.log('تم العثور على', subCustomers.length, 'عميل فرعي في القائمة المفلترة');
    }

    return subCustomers;
  };

  // توسيع/طي العملاء الفرعيين للعميل الدائم
  const toggleCustomerExpansion = async (customerId) => {
    try {
      // تحديث حالة التوسيع
      const newExpandedState = !expandedCustomers[customerId];

      // تحديث حالة التوسيع فوراً
      setExpandedCustomers(prev => ({
        ...prev,
        [customerId]: newExpandedState
      }));

      // إذا كان العميل يتم توسيعه، نقوم بتحديث قائمة العملاء الفرعيين
      if (newExpandedState) {
        console.log('توسيع العميل الدائم:', customerId);

        // البحث عن العملاء الفرعيين في قائمة العملاء الحالية أولاً
        const existingSubCustomers = getSubCustomersForParent(customerId);
        console.log('العملاء الفرعيين الموجودين في القائمة الحالية:', existingSubCustomers.length);

        // إذا كان هناك عملاء فرعيين في القائمة الحالية، نكتفي بهم
        if (existingSubCustomers.length > 0) {
          console.log('تم العثور على عملاء فرعيين في القائمة الحالية، لا حاجة للبحث في قاعدة البيانات');
          return;
        }

        try {
          // محاولة الحصول على العملاء الفرعيين من قاعدة البيانات مباشرة
          console.log('الحصول على العملاء الفرعيين من قاعدة البيانات...');

          // استخدام window.api.invoke بشكل صحيح
          const result = await window.api.invoke('get-sub-customers', customerId);
          console.log('نتيجة استدعاء get-sub-customers:', result);

          // التحقق من أن النتيجة تحتوي على مصفوفة العملاء الفرعيين
          const subCustomers = Array.isArray(result) ? result :
                              (result && result.subCustomers && Array.isArray(result.subCustomers)) ?
                              result.subCustomers : [];

          if (subCustomers && subCustomers.length > 0) {
            console.log('تم العثور على', subCustomers.length, 'عميل فرعي من قاعدة البيانات');

            // إضافة العملاء الفرعيين إلى قائمة العملاء الكاملة
            const allCustomers = [...customers];
            const currentIds = new Set(allCustomers.map(c => c.id || c._id));
            const newSubCustomers = subCustomers.filter(c => !currentIds.has(c.id) && !currentIds.has(c._id));

            if (newSubCustomers.length > 0) {
              console.log('إضافة', newSubCustomers.length, 'عميل فرعي جديد إلى القائمة');

              // تحديث قائمة العملاء الكاملة
              const updatedCustomers = [...allCustomers, ...newSubCustomers];

              // تحديث سياق العملاء في الذاكرة
              if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
                window.api.customers.updateCustomersContext(updatedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة');
              }
            }
          } else {
            console.log('لم يتم العثور على عملاء فرعيين في قاعدة البيانات');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء get-sub-customers:', apiError);

          // محاولة استخدام window.api.customers.getAll كبديل
          console.log('محاولة استخدام window.api.customers.getAll كبديل...');
          try {
            const allCustomers = await window.api.customers.getAll();
            if (Array.isArray(allCustomers)) {
              // تصفية العملاء الفرعيين للعميل الدائم المحدد
              const subCustomers = allCustomers.filter(c =>
                c.customer_type === 'sub' &&
                (c.parent_id === customerId || c.parent_id === Number(customerId) || String(c.parent_id) === String(customerId))
              );

              if (subCustomers.length > 0) {
                console.log('تم العثور على', subCustomers.length, 'عميل فرعي من قائمة العملاء الكاملة');

                // تحديث سياق العملاء في الذاكرة
                if (window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
                  window.api.customers.updateCustomersContext(allCustomers);
                  console.log('تم تحديث سياق العملاء في الذاكرة');
                }
              } else {
                console.log('لم يتم العثور على عملاء فرعيين في قائمة العملاء الكاملة');
              }
            }
          } catch (fallbackError) {
            console.error('خطأ في استخدام window.api.customers.getAll كبديل:', fallbackError);
          }
        }
      } else {
        console.log('طي العميل الدائم:', customerId);
      }
    } catch (error) {
      console.error('خطأ في توسيع/طي العميل الدائم:', error);
    }
  };

  // عرض سجل مبيعات العميل
  const handleShowSalesHistory = (customerId) => {
    // التأكد من أن معرف العميل هو رقم
    const numericCustomerId = parseInt(customerId);

    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      console.error('معرف العميل غير صالح لعرض سجل المبيعات:', customerId);
      showAlert('danger', 'معرف العميل غير صالح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
      return;
    }

    console.log('عرض سجل مبيعات العميل بالمعرف:', numericCustomerId);
    setSelectedCustomerId(numericCustomerId);
    setShowSalesHistory(true);
  };

  // إنشاء فاتورة فرعية
  const handleCreateSubInvoice = async () => {
    try {
      if (saleItems.length === 0) {
        showAlert('danger', 'يرجى إضافة صنف واحد على الأقل للفاتورة الفرعية');
        return;
      }

      setIsCreatingSubInvoice(true);

      // الحصول على رقم الفاتورة الرئيسية
      const parentInvoiceNumber = invoiceData.invoice_number;

      if (!parentInvoiceNumber) {
        showAlert('danger', 'رقم الفاتورة الرئيسية غير متوفر');
        setIsCreatingSubInvoice(false);
        return;
      }

      // تحضير الأصناف المختارة للفاتورة الفرعية
      const selectedItems = saleItems.map(item => ({
        item_id: item.item_id,
        quantity: item.quantity,
        price: item.price,
        total_price: item.quantity * item.price,
        profit: 0 // سيتم حسابه في الخادم
      }));

      // استدعاء وظيفة إنشاء فاتورة فرعية
      const result = await window.api.customers.createSubInvoice(
        parentInvoiceNumber,
        currentCustomer.id,
        selectedItems
      );

      if (result.success) {
        showAlert('success', `تم إنشاء الفاتورة الفرعية بنجاح: ${result.subInvoice.invoice_number}`);

        // إغلاق النافذة وإعادة تعيين النموذج
        setShowSalesModal(false);
        resetSaleForm();
      } else {
        showAlert('danger', result.error || 'فشل في إنشاء الفاتورة الفرعية');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة الفرعية:', error);
      showAlert('danger', error.message || 'حدث خطأ أثناء إنشاء الفاتورة الفرعية');
    } finally {
      setIsCreatingSubInvoice(false);
    }
  };

  // الحصول على رقم الفاتورة الرئيسية للعميل
  const getCustomerMainInvoiceId = (customer) => {
    if (!customer) {
      console.error('لا يوجد عميل لاستخراج رقم الفاتورة الرئيسية');
      return 'INV-CL-000'; // رقم فاتورة افتراضي
    }

    // استخدام رقم الفاتورة الرئيسية إذا كان موجودًا
    if (customer.main_invoice_id) {
      console.log(`تم استخدام رقم الفاتورة الرئيسية للعميل: ${customer.main_invoice_id}`);
      return customer.main_invoice_id;
    }

    // التأكد من أن معرف العميل هو رقم
    let customerId;

    if (typeof customer.id === 'number') {
      customerId = customer.id;
    } else if (typeof customer.id === 'string' && !isNaN(parseInt(customer.id))) {
      customerId = parseInt(customer.id);
    } else if (typeof customer._id === 'number') {
      customerId = customer._id;
    } else if (typeof customer._id === 'string' && !isNaN(parseInt(customer._id))) {
      customerId = parseInt(customer._id);
    } else {
      console.error('لا يمكن استخراج معرف رقمي للعميل:', customer);
      return 'INV-CL-000'; // رقم فاتورة افتراضي
    }

    // إذا لم يكن موجودًا، نستخدم الصيغة الجديدة كاحتياطي
    const fallbackInvoiceId = `H${customerId.toString().padStart(5, '0')}`;
    console.log(`لم يتم العثور على رقم فاتورة رئيسية للعميل، تم استخدام الصيغة الاحتياطية: ${fallbackInvoiceId}`);
    return fallbackInvoiceId;
  };

  // إعادة تعيين نموذج البيع
  const resetSaleForm = () => {
    setSaleItems([]);
    setCurrentItem({
      item_id: '',
      quantity: 1,
      price: 0
    });
    setItemSearchTerm('');
    setInvoiceData({
      invoice_number: '',
      transaction_date: new Date().toISOString().split('T')[0]
    });
  };

  // فتح نموذج البيع لعميل
  const handleSellToCustomer = (customer) => {
    // التأكد من أن العميل يحتوي على معرف صحيح
    if (!customer || (!customer.id && !customer._id)) {
      console.error('معرف العميل غير صالح:', customer);
      showAlert('danger', 'معرف العميل غير صالح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
      return;
    }

    // التأكد من أن العميل يحتوي على معرف رقمي
    const customerId = parseInt(customer.id || customer._id);
    if (isNaN(customerId) || customerId <= 0) {
      console.error('معرف العميل ليس رقمًا صالحًا:', customer);
      showAlert('danger', 'معرف العميل ليس رقمًا صالحًا. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
      return;
    }

    // تعيين العميل مع التأكد من وجود معرف رقمي
    const customerWithNumericId = {
      ...customer,
      id: customerId,
      _id: customerId.toString()
    };

    console.log('تم تعيين العميل للبيع:', customerWithNumericId);

    setCurrentCustomer(customerWithNumericId);
    setSelectedCustomerId(customerId);
    resetSaleForm();

    // الحصول على رقم الفاتورة الرئيسية للعميل
    const customerInvoiceNumber = getCustomerMainInvoiceId(customerWithNumericId);
    console.log(`رقم الفاتورة الرئيسية للعميل ${customerWithNumericId.name}:`, customerInvoiceNumber);

    setInvoiceData(prev => ({
      ...prev,
      invoice_number: customerInvoiceNumber
    }));

    setShowSalesModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // ملاحظة: تم إزالة وظيفة handleItemChange لأنها لم تعد تستخدم مع شريط البحث
  // وتم استبدالها بوظيفة handleItemAutocompleteSelect

  // معالجة اختيار صنف من الإكمال التلقائي
  const handleItemAutocompleteSelect = async (item) => {
    console.log('تم اختيار الصنف من الإكمال التلقائي:', item);

    if (!item) {
      console.warn('لم يتم اختيار أي صنف');
      return;
    }

    try {
      // البحث عن الصنف في المخزون أولاً للحصول على أحدث سعر بيع
      const inventoryItem = inventory.find(invItem =>
        invItem.item_id === item.id ||
        invItem.id === item.id ||
        invItem.id === parseInt(item.id) ||
        invItem._id === item.id
      );

      // استخدام سعر البيع من المخزون إذا كان متاحًا، وإلا استخدام سعر البيع من الصنف
      const sellingPrice = inventoryItem?.selling_price || item.selling_price || 0;

      // تعيين الصنف الحالي
      setCurrentItem({
        item_id: item.id,
        item_name: item.name,
        quantity: 1,
        price: sellingPrice
      });

      // تعيين مصطلح البحث ليعكس الصنف المختار
      setItemSearchTerm(item.name);

      console.log(`تم تعيين الصنف الحالي:`, {
        item_id: item.id,
        item_name: item.name,
        quantity: 1,
        price: sellingPrice
      });
    } catch (error) {
      console.error('خطأ في معالجة اختيار الصنف:', error);
    }
  };

  // معالجة تغيير الكمية أو السعر
  const handleItemInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentItem(prev => ({
      ...prev,
      [name]: name === 'quantity' ? parseInt(value) || 1 : parseFloat(value) || 0
    }));
  };

  // معالجة تغيير بيانات الفاتورة
  const handleInvoiceInputChange = (e) => {
    const { name, value } = e.target;
    setInvoiceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إضافة صنف إلى قائمة البيع
  const handleAddItemToSale = () => {
    if (!currentItem.item_id) {
      showAlert('danger', 'يرجى اختيار صنف');
      return;
    }

    if (currentItem.quantity <= 0) {
      showAlert('danger', 'يجب أن تكون الكمية أكبر من صفر');
      return;
    }

    // تعديل التحقق من السعر للسماح بأي قيمة
    // إذا كان السعر صفر أو سالب، سنستخدم قيمة افتراضية 0.01
    let itemPrice = currentItem.price;
    if (itemPrice <= 0) {
      itemPrice = 0.01;
    }

    // البحث عن الصنف في المخزون أولاً للحصول على أحدث معلومات
    const inventoryItem = inventory.find(item =>
      item.item_id === currentItem.item_id ||
      item.id === currentItem.item_id ||
      item.id === parseInt(currentItem.item_id) ||
      item._id === currentItem.item_id
    );

    // البحث عن الصنف في قائمة الأصناف كاحتياطي
    const selectedItem = items.find(item =>
      item.id === currentItem.item_id ||
      item.id === parseInt(currentItem.item_id) ||
      item._id === currentItem.item_id
    );

    // استخدام معلومات الصنف من المخزون إذا كانت متاحة، وإلا استخدام معلومات الصنف من قائمة الأصناف
    const itemToUse = inventoryItem || selectedItem;

    // الحصول على اسم الصنف
    let itemName = '';
    if (itemToUse) {
      itemName = itemToUse.name || itemToUse.item_name;
    } else if (currentItem.item_name) {
      itemName = currentItem.item_name;
    } else {
      itemName = 'صنف غير معروف';
    }

    // التحقق من وجود الصنف في القائمة
    const existingItemIndex = saleItems.findIndex(item => item.item_id === currentItem.item_id);

    if (existingItemIndex !== -1) {
      // تحديث الصنف الموجود
      const updatedItems = [...saleItems];
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: updatedItems[existingItemIndex].quantity + currentItem.quantity,
        price: itemPrice // استخدام السعر المعدل
      };

      setSaleItems(updatedItems);
    } else {
      // إضافة صنف جديد
      setSaleItems([...saleItems, {
        ...currentItem,
        price: itemPrice, // استخدام السعر المعدل
        item_name: itemName,
        total: currentItem.quantity * itemPrice // استخدام السعر المعدل
      }]);
    }

    // إعادة تعيين نموذج الصنف
    setCurrentItem({
      item_id: '',
      quantity: 1,
      price: 0
    });

    // إعادة تعيين البحث
    setItemSearchTerm('');
  };

  // حذف صنف من قائمة البيع
  const handleRemoveItemFromSale = (index) => {
    const updatedItems = [...saleItems];
    updatedItems.splice(index, 1);
    setSaleItems(updatedItems);
  };

  // حساب إجمالي الفاتورة
  const calculateTotal = () => {
    // تحويل القيم إلى أرقام للتأكد من صحة الحساب
    return saleItems.reduce((total, item) => {
      const quantity = Number(item.quantity) || 0;
      const price = Number(item.price) || 0;
      return total + (quantity * price);
    }, 0);
  };

  // فتح نموذج الإرجاع من عميل
  const handleReturnFromCustomer = (customer) => {
    // التحقق من دور المستخدم الحالي
    const currentUserRole = localStorage.getItem('currentUserRole');
    const isViewer = currentUserRole === 'viewer';

    // المشاهد فقط لا يمكنه إضافة إرجاع (الموظف والمدير يمكنهم ذلك)
    if (isViewer) {
      showAlert('danger', 'ليس لديك صلاحية لإضافة إرجاع. المشاهد يمكنه فقط عرض البيانات.');
      return;
    }

    if (!customer || (!customer.id && !customer._id)) {
      console.error('معرف العميل غير صالح:', customer);
      showAlert('danger', 'معرف العميل غير صالح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
      return;
    }
    const customerId = parseInt(customer.id || customer._id);
    if (isNaN(customerId) || customerId <= 0) {
      console.error('معرف العميل ليس رقمًا صالحًا:', customer);
      showAlert('danger', 'معرف العميل ليس رقمًا صالحًا. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
      return;
    }
    const customerWithNumericId = {
      ...customer,
      id: customerId,
      _id: customerId.toString()
    };

    console.log('فتح نافذة الإرجاع للعميل:', customerWithNumericId);

    setCurrentCustomer(customerWithNumericId);
    setSelectedCustomerId(customerId);

    // التحقق من توفر نظام الإرجاع الجديد
    checkNewReturnSystemAvailability();

    resetReturnForm();
    setShowReturnModal(true);
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // التحقق من توفر نظام الإرجاع الجديد
  const [useNewReturnSystem, setUseNewReturnSystem] = useState(false);

  const checkNewReturnSystemAvailability = async () => {
    try {
      // محاولة استدعاء واجهة برمجة التطبيقات للتحقق من توفر نظام الإرجاع الجديد
      const result = await window.api.invoke('check-return-system-availability');

      if (result && result.success && result.useNewSystem) {
        console.log('نظام الإرجاع الجديد متوفر، سيتم استخدامه');
        setUseNewReturnSystem(true);
      } else {
        console.log('نظام الإرجاع الجديد غير متوفر، سيتم استخدام النظام القديم');
        setUseNewReturnSystem(false);
      }
    } catch (error) {
      console.error('خطأ في التحقق من توفر نظام الإرجاع الجديد:', error);
      console.log('سيتم استخدام النظام القديم كاحتياطي');
      setUseNewReturnSystem(false);
    }
  };

  // إعادة تعيين نموذج الإرجاع
  const resetReturnForm = () => {
    setReturnItems([]);
    setReturnCurrentItem({
      item_id: '',
      quantity: 1,
      price: 0
    });
    setReturnItemSearchTerm('');
    setReturnInvoiceData({
      invoice_number: '',
      transaction_date: new Date().toISOString().split('T')[0]
    });
  };

  // حالة الإرجاع
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [returnItems, setReturnItems] = useState([]);
  const [returnCurrentItem, setReturnCurrentItem] = useState({ item_id: '', quantity: 1, price: 0 });
  const [returnItemSearchTerm, setReturnItemSearchTerm] = useState('');
  const [returnInvoiceData, setReturnInvoiceData] = useState({ invoice_number: '', transaction_date: new Date().toISOString().split('T')[0] });

  // اختيار صنف من الإكمال التلقائي للإرجاع
  const handleReturnItemAutocompleteSelect = async (item) => {
    if (!item) return;
    try {
      // البحث عن الصنف في المخزون
      const inventoryItem = inventory.find(invItem =>
        invItem.item_id === item.id ||
        invItem.id === item.id ||
        invItem.id === parseInt(item.id) ||
        invItem._id === item.id
      );
      const sellingPrice = inventoryItem?.selling_price || item.selling_price || 0;

      // الحصول على معرف العميل
      const customerId = parseInt(currentCustomer.id || currentCustomer._id || 0);
      if (!customerId) {
        showAlert('danger', 'معرف العميل غير صالح');
        return;
      }

      // الحصول على معرف الصنف
      const itemId = parseInt(item.id);

      // الحصول على إجمالي الكمية المباعة للعميل لهذا الصنف
      const soldQuantityResult = await window.api.invoke('get-customer-item-sales', {
        customerId: customerId,
        itemId: itemId
      });

      const totalSold = soldQuantityResult.totalSold || 0;

      // الحصول على إجمالي الكمية المسترجعة سابقاً للعميل لهذا الصنف
      const returnedQuantityResult = await window.api.invoke('get-customer-item-returns', {
        customerId: customerId,
        itemId: itemId
      });

      const totalReturned = returnedQuantityResult.totalReturned || 0;

      // حساب الكمية المتاحة للاسترجاع
      const availableForReturn = totalSold - totalReturned;

      console.log(`الصنف: ${item.name}, إجمالي المبيعات: ${totalSold}, إجمالي المسترجعات: ${totalReturned}, المتاح للاسترجاع: ${availableForReturn}`);

      // إذا لم تكن هناك كمية متاحة للاسترجاع، نعرض تنبيهاً
      if (availableForReturn <= 0) {
        showAlert('warning', `لا توجد كمية متاحة للاسترجاع من الصنف "${item.name}". لم يتم بيع هذا الصنف للعميل أو تم استرجاع كل الكمية المباعة.`);
        return;
      }

      // تعيين الصنف المحدد مع الكمية المتاحة للاسترجاع
      setReturnCurrentItem({
        item_id: item.id,
        item_name: item.name,
        quantity: Math.min(1, availableForReturn), // تحديد الكمية بحيث لا تتجاوز المتاح
        price: sellingPrice,
        availableForReturn: availableForReturn, // تخزين الكمية المتاحة للاسترجاع
        totalSold: totalSold, // تخزين إجمالي الكمية المباعة
        totalReturned: totalReturned // تخزين إجمالي الكمية المسترجعة سابقاً
      });

      setReturnItemSearchTerm(item.name);
    } catch (error) {
      console.error('خطأ في معالجة اختيار الصنف للإرجاع:', error);
      showAlert('danger', 'حدث خطأ في الحصول على معلومات الاسترجاع');
    }
  };

  // تغيير الكمية أو السعر في الإرجاع
  const handleReturnItemInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'quantity') {
      // تحويل القيمة إلى رقم
      const newQuantity = parseInt(value) || 1;

      // التحقق من الكمية المتاحة للاسترجاع
      if (returnCurrentItem.availableForReturn !== undefined) {
        // إذا كانت الكمية الجديدة أكبر من الكمية المتاحة للاسترجاع
        if (newQuantity > returnCurrentItem.availableForReturn) {
          // عرض تنبيه
          showAlert('warning', `الكمية المطلوبة (${newQuantity}) تتجاوز الكمية المتاحة للاسترجاع (${returnCurrentItem.availableForReturn})`);

          // تعيين الكمية إلى الحد الأقصى المتاح
          setReturnCurrentItem(prev => ({
            ...prev,
            quantity: returnCurrentItem.availableForReturn
          }));
          return;
        }
      }

      // تعيين الكمية الجديدة
      setReturnCurrentItem(prev => ({
        ...prev,
        quantity: newQuantity
      }));
    } else {
      // تعيين قيمة أخرى (مثل السعر)
      setReturnCurrentItem(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    }
  };

  // تغيير بيانات الفاتورة للإرجاع
  const handleReturnInvoiceInputChange = (e) => {
    const { name, value } = e.target;
    setReturnInvoiceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إضافة صنف إلى قائمة الإرجاع
  const handleAddItemToReturn = async () => {
    if (!returnCurrentItem.item_id) {
      showAlert('danger', 'يرجى اختيار صنف للإرجاع');
      return;
    }
    if (returnCurrentItem.quantity <= 0) {
      showAlert('danger', 'يجب أن تكون الكمية أكبر من صفر');
      return;
    }

    try {
      // الحصول على معرف العميل
      const customerId = parseInt(currentCustomer.id || currentCustomer._id || 0);
      if (!customerId) {
        showAlert('danger', 'معرف العميل غير صالح');
        return;
      }

      // الحصول على معرف الصنف
      const itemId = parseInt(returnCurrentItem.item_id);

      // الحصول على إجمالي الكمية المباعة للعميل لهذا الصنف
      const soldQuantityResult = await window.api.invoke('get-customer-item-sales', {
        customerId: customerId,
        itemId: itemId
      });

      const totalSold = soldQuantityResult.totalSold || 0;

      // الحصول على إجمالي الكمية المسترجعة سابقاً للعميل لهذا الصنف
      const returnedQuantityResult = await window.api.invoke('get-customer-item-returns', {
        customerId: customerId,
        itemId: itemId
      });

      const totalReturned = returnedQuantityResult.totalReturned || 0;

      // حساب الكمية المتاحة للاسترجاع
      const availableForReturn = totalSold - totalReturned;

      // حساب الكمية المضافة بالفعل في قائمة الإرجاع
      const existingItemIndex = returnItems.findIndex(item => item.item_id === returnCurrentItem.item_id);
      const existingQuantity = existingItemIndex !== -1 ? returnItems[existingItemIndex].quantity : 0;

      // حساب إجمالي الكمية المطلوبة (الكمية الحالية + الكمية المضافة بالفعل)
      const totalRequestedQuantity = returnCurrentItem.quantity + existingQuantity;

      // التحقق من أن الكمية المطلوبة لا تتجاوز الكمية المتاحة للاسترجاع
      if (totalRequestedQuantity > availableForReturn) {
        showAlert('warning', `الكمية المطلوبة (${totalRequestedQuantity}) تتجاوز الكمية المتاحة للاسترجاع (${availableForReturn})`);
        return;
      }

      // إذا كانت الكمية المطلوبة صالحة، نتابع إضافة الصنف
      let itemPrice = returnCurrentItem.price;
      if (itemPrice <= 0) {
        itemPrice = 0.01;
      }

      const inventoryItem = inventory.find(item =>
        item.item_id === returnCurrentItem.item_id ||
        item.id === returnCurrentItem.item_id ||
        item.id === parseInt(returnCurrentItem.item_id) ||
        item._id === returnCurrentItem.item_id
      );

      const selectedItem = items.find(item =>
        item.id === returnCurrentItem.item_id ||
        item.id === parseInt(returnCurrentItem.item_id) ||
        item._id === returnCurrentItem.item_id
      );

      const itemToUse = inventoryItem || selectedItem;
      let itemName = '';

      if (itemToUse) {
        itemName = itemToUse.name || itemToUse.item_name;
      } else if (returnCurrentItem.item_name) {
        itemName = returnCurrentItem.item_name;
      } else {
        itemName = 'صنف غير معروف';
      }

      if (existingItemIndex !== -1) {
        const updatedItems = [...returnItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + returnCurrentItem.quantity,
          price: itemPrice,
          total: (updatedItems[existingItemIndex].quantity + returnCurrentItem.quantity) * itemPrice
        };
        setReturnItems(updatedItems);
      } else {
        setReturnItems([...returnItems, {
          ...returnCurrentItem,
          price: itemPrice,
          item_name: itemName,
          total: returnCurrentItem.quantity * itemPrice,
          availableForReturn: availableForReturn, // تخزين الكمية المتاحة للاسترجاع
          totalSold: returnCurrentItem.totalSold || 0, // تخزين إجمالي الكمية المباعة
          totalReturned: returnCurrentItem.totalReturned || 0 // تخزين إجمالي الكمية المسترجعة سابقاً
        }]);
      }

      setReturnCurrentItem({ item_id: '', quantity: 1, price: 0 });
      setReturnItemSearchTerm('');

      showAlert('success', `تمت إضافة الصنف "${itemName}" إلى قائمة الإرجاع`);
    } catch (error) {
      console.error('خطأ في إضافة الصنف إلى قائمة الإرجاع:', error);
      showAlert('danger', 'حدث خطأ في إضافة الصنف إلى قائمة الإرجاع');
    }
  };

  // حذف صنف من قائمة الإرجاع
  const handleRemoveItemFromReturn = (index) => {
    const updatedItems = [...returnItems];
    updatedItems.splice(index, 1);
    setReturnItems(updatedItems);
  };

  // حساب إجمالي الإرجاع
  const calculateReturnTotal = () => {
    return returnItems.reduce((total, item) => {
      const quantity = Number(item.quantity) || 0;
      const price = Number(item.price) || 0;
      return total + (quantity * price);
    }, 0);
  };

  // معالجة إرسال نموذج الإرجاع
  const handleSubmitReturn = async (e) => {
    e.preventDefault();
    try {
      if (!currentCustomer || !currentCustomer.id) {
        showAlert('danger', 'الرجاء اختيار عميل صالح للإرجاع');
        return;
      }
      if (returnItems.length === 0) {
        showAlert('danger', 'يرجى إضافة صنف واحد على الأقل للإرجاع');
        return;
      }

      // الحصول على معرف العميل
      const customerId = parseInt(currentCustomer.id || currentCustomer._id || 0);
      if (!customerId) {
        throw new Error('معرف العميل غير صالح. يرجى المحاولة مرة أخرى.');
      }

      // التحقق من الكميات المسترجعة لكل صنف
      for (let i = 0; i < returnItems.length; i++) {
        const item = returnItems[i];
        const itemId = parseInt(item.item_id);

        // الحصول على إجمالي الكمية المباعة للعميل لهذا الصنف
        const soldQuantityResult = await window.api.invoke('get-customer-item-sales', {
          customerId: customerId,
          itemId: itemId
        });

        const totalSold = soldQuantityResult.totalSold || 0;

        // الحصول على إجمالي الكمية المسترجعة سابقاً للعميل لهذا الصنف
        const returnedQuantityResult = await window.api.invoke('get-customer-item-returns', {
          customerId: customerId,
          itemId: itemId
        });

        const totalReturned = returnedQuantityResult.totalReturned || 0;

        // حساب الكمية المتاحة للاسترجاع
        const availableForReturn = totalSold - totalReturned;

        console.log(`الصنف: ${item.item_name}, إجمالي المبيعات: ${totalSold}, إجمالي المسترجعات: ${totalReturned}, المتاح للاسترجاع: ${availableForReturn}`);

        // التحقق من أن الكمية المطلوب استرجاعها لا تتجاوز الكمية المتاحة
        if (item.quantity > availableForReturn) {
          throw new Error(`الكمية المطلوب استرجاعها (${item.quantity}) للصنف "${item.item_name}" تتجاوز الكمية المتاحة للاسترجاع (${availableForReturn})`);
        }
      }

      // إذا تم التحقق من جميع الكميات، نقوم بإنشاء معاملات الاسترجاع
      const completedReturns = [];
      for (let i = 0; i < returnItems.length; i++) {
        const item = returnItems[i];
        // البحث عن الصنف في المخزون أو الأصناف
        const inventoryItem = inventory.find(inv =>
          inv.item_id === item.item_id ||
          inv.id === item.item_id ||
          inv.id === parseInt(item.item_id) ||
          inv._id === item.item_id
        );
        const selectedItem = items.find(itm =>
          itm.id === item.item_id ||
          itm.id === parseInt(item.item_id) ||
          itm._id === item.item_id
        );
        const itemToUse = inventoryItem || selectedItem;
        const itemName = item.item_name || (itemToUse ? itemToUse.name || itemToUse.item_name : 'صنف غير معروف');
        const safePrice = item.price <= 0 ? 0.01 : item.price;

        try {
          console.log(`إضافة معاملة إرجاع للصنف ${itemName} بكمية ${item.quantity} وسعر ${safePrice}`);

          // إنشاء معاملة الإرجاع
          const transaction = await addTransaction({
            item_id: parseInt(item.item_id),
            transaction_type: 'return',
            quantity: item.quantity,
            price: safePrice,
            total_price: item.quantity * safePrice,
            customer_id: customerId,
            customer: currentCustomer.name,
            item_name: itemName,
            invoice_number: returnInvoiceData.invoice_number,
            transaction_date: returnInvoiceData.transaction_date ? new Date(returnInvoiceData.transaction_date).toISOString() : new Date().toISOString(),
            // نحن نقوم بتحديث المخزون بشكل منفصل بعد إنشاء المعاملة
            skip_inventory_update: false
          });

          console.log(`تم إضافة معاملة الإرجاع بنجاح:`, transaction);

          // التحقق من نجاح المعاملة
          if (!transaction || !transaction.success) {
            console.error(`فشل في إضافة معاملة الإرجاع للصنف ${itemName}:`, transaction);
            throw new Error(`فشل في إضافة معاملة الإرجاع للصنف ${itemName}`);
          }

          // التأكد من تحديث المخزون بعد عملية الإرجاع
          try {
            // استدعاء معالج تحديث المخزون بعد الإرجاع
            console.log(`[RETURN-FIX] استدعاء update-inventory-after-return للصنف ${itemName} بكمية ${item.quantity}`);

            // الحصول على معلومات المخزون الحالية قبل التحديث
            const inventoryBefore = await window.api.invoke('get-inventory-item', {
              itemId: parseInt(item.item_id)
            });

            if (inventoryBefore && inventoryBefore.item) {
              console.log(`[RETURN-FIX] الكمية الحالية في المخزون قبل الإرجاع: ${inventoryBefore.item.current_quantity}`);
            }

            // تحديث المخزون
            const updateResult = await window.api.invoke('update-inventory-after-return', {
              itemId: parseInt(item.item_id),
              quantity: item.quantity
            });

            console.log(`[RETURN-FIX] نتيجة تحديث المخزون بعد الإرجاع:`, updateResult);

            if (updateResult && updateResult.success) {
              console.log(`[RETURN-FIX] تم تحديث المخزون بنجاح بعد الإرجاع. الكمية الجديدة: ${updateResult.newQuantity}`);
            } else {
              console.warn(`[RETURN-FIX] فشل في تحديث المخزون بعد الإرجاع للصنف ${itemName}:`, updateResult ? updateResult.error : 'سبب غير معروف');

              // محاولة تحديث المخزون بالطريقة القديمة
              console.log(`[RETURN-FIX] محاولة تحديث المخزون بالطريقة القديمة...`);

              // الحصول على معلومات المخزون الحالية
              const inventoryInfo = await window.api.invoke('get-inventory-item', {
                itemId: parseInt(item.item_id)
              });

              if (inventoryInfo && inventoryInfo.item) {
                // تحديث المخزون يدوياً
                const currentQuantity = inventoryInfo.item.current_quantity || 0;
                const newQuantity = currentQuantity + item.quantity;

                console.log(`[RETURN-FIX] تحديث المخزون يدوياً: الكمية الحالية=${currentQuantity}, الكمية الجديدة=${newQuantity}`);

                const manualUpdateResult = await window.api.invoke('update-inventory', {
                  itemId: parseInt(item.item_id),
                  updates: {
                    current_quantity: newQuantity
                  }
                });

                console.log(`[RETURN-FIX] نتيجة تحديث المخزون يدوياً:`, manualUpdateResult);

                if (!manualUpdateResult || !manualUpdateResult.success) {
                  console.error(`[RETURN-FIX] فشل في تحديث المخزون يدوياً:`, manualUpdateResult ? manualUpdateResult.error : 'سبب غير معروف');
                  showAlert('warning', `تحذير: فشل في تحديث المخزون للصنف ${itemName}`);
                }
              } else {
                console.error(`[RETURN-FIX] فشل في الحصول على معلومات المخزون للصنف ${itemName}`);
                showAlert('warning', `تحذير: فشل في الحصول على معلومات المخزون للصنف ${itemName}`);
              }
            }

            // التحقق من نجاح تحديث المخزون
            const inventoryAfter = await window.api.invoke('get-inventory-item', {
              itemId: parseInt(item.item_id)
            });

            if (inventoryAfter && inventoryAfter.item) {
              console.log(`[RETURN-FIX] الكمية الجديدة في المخزون بعد الإرجاع: ${inventoryAfter.item.current_quantity}`);

              if (inventoryBefore && inventoryBefore.item) {
                const beforeQuantity = inventoryBefore.item.current_quantity || 0;
                const afterQuantity = inventoryAfter.item.current_quantity || 0;

                if (afterQuantity <= beforeQuantity) {
                  console.error(`[RETURN-FIX] تحذير: لم يتم تحديث المخزون بشكل صحيح. الكمية قبل: ${beforeQuantity}, الكمية بعد: ${afterQuantity}`);

                  // محاولة أخيرة لتحديث المخزون
                  const finalUpdateResult = await window.api.invoke('update-inventory', {
                    itemId: parseInt(item.item_id),
                    updates: {
                      current_quantity: beforeQuantity + item.quantity
                    }
                  });

                  console.log(`[RETURN-FIX] نتيجة المحاولة الأخيرة لتحديث المخزون:`, finalUpdateResult);
                }
              }
            }
          } catch (inventoryError) {
            console.error(`[RETURN-FIX] خطأ في تحديث المخزون بعد الإرجاع:`, inventoryError);
            // لا نريد إيقاف العملية إذا فشل تحديث المخزون
          }

          completedReturns.push(transaction);
        } catch (itemError) {
          console.error(`خطأ في إضافة معاملة الإرجاع للصنف ${itemName}:`, itemError);
          throw itemError;
        }
      }

      if (completedReturns.length > 0) {
        showAlert('success', 'تم إتمام عملية الإرجاع بنجاح. يمكنك طباعة الإيصال من سجل المبيعات.');
      }

      // إغلاق النافذة وإعادة تعيين النموذج
      if (isMounted.current) {
        setShowReturnModal(false);
        resetReturnForm();
      }
    } catch (err) {
      console.error('Error processing return:', err);
      showAlert('danger', err.message || 'فشل في إتمام عملية الإرجاع');
    }
  };

  // معالجة إرسال نموذج البيع
  const handleSubmitSale = async (e) => {
    e.preventDefault();

    try {
      if (isCreatingSubInvoice) {
        showAlert('warning', 'جاري إنشاء فاتورة فرعية، يرجى الانتظار...');
        return;
      }

      if (saleItems.length === 0) {
        showAlert('danger', 'يرجى إضافة صنف واحد على الأقل');
        return;
      }

      // إنشاء معاملات البيع
      const completedTransactions = [];

      // معالجة كل صنف في قائمة البيع
      for (let i = 0; i < saleItems.length; i++) {
        const item = saleItems[i];

        // البحث عن الصنف في المخزون أولاً للحصول على أحدث معلومات
        const inventoryItem = inventory.find(inv =>
          inv.item_id === item.item_id ||
          inv.id === item.item_id ||
          inv.id === parseInt(item.item_id) ||
          inv._id === item.item_id
        );

        // البحث عن الصنف في قائمة الأصناف كاحتياطي
        const selectedItem = items.find(itm =>
          itm.id === item.item_id ||
          itm.id === parseInt(item.item_id) ||
          itm._id === item.item_id
        );

        // استخدام معلومات الصنف من المخزون إذا كانت متاحة، وإلا استخدام معلومات الصنف من قائمة الأصناف
        const itemToUse = inventoryItem || selectedItem;

        // استخدام اسم الصنف من البيانات المخزنة في saleItems
        const itemName = item.item_name || (itemToUse ? itemToUse.name || itemToUse.item_name : 'صنف غير معروف');

        // التأكد من أن السعر لا يقل عن 0.01
        const safePrice = item.price <= 0 ? 0.01 : item.price;

        try {
          // التأكد من أن معرف العميل هو رقم
          const customerId = parseInt(currentCustomer.id || currentCustomer._id || 0);

          // التحقق من صحة معرف العميل
          if (!customerId) {
            console.error('معرف العميل غير صالح:', currentCustomer);
            throw new Error('معرف العميل غير صالح. يرجى المحاولة مرة أخرى.');
          }

          console.log('معرف العميل المستخدم في المعاملة:', customerId, 'نوع البيانات:', typeof customerId);

          // إنشاء معاملة بيع
          const transaction = await addTransaction({
            item_id: parseInt(item.item_id),
            transaction_type: 'sale',
            quantity: item.quantity,
            price: safePrice,
            total_price: item.quantity * safePrice,
            customer_id: customerId, // استخدام معرف العميل كرقم
            customer: currentCustomer.name, // استخدام اسم العميل في حقل customer
            item_name: itemName,
            invoice_number: invoiceData.invoice_number,
            transaction_date: invoiceData.transaction_date ? new Date(invoiceData.transaction_date).toISOString() : new Date().toISOString()
          });

          completedTransactions.push(transaction);
        } catch (itemError) {
          // إذا حدث خطأ، نعيد رميه
          throw itemError;
        }
      }

      // عرض رسالة نجاح تتضمن إرشادات لطباعة الفاتورة
      if (completedTransactions.length > 0) {
        showAlert('success', 'تم إتمام عملية البيع بنجاح. يمكنك طباعة الفاتورة من سجل المبيعات.');
      }

      // إغلاق النافذة وإعادة تعيين النموذج
      if (isMounted.current) {
        setShowSalesModal(false);
        resetSaleForm();
      }
    } catch (err) {
      console.error('Error processing sale:', err);
      showAlert('danger', err.message || 'فشل في إتمام عملية البيع');
    }
  };

  // الحصول على اسم العميل الدائم
  const getParentCustomerName = (parentId) => {
    if (!parentId) return '';

    // تحويل parentId إلى نص ورقم للمقارنة
    const parentIdStr = String(parentId);
    const parentIdNum = Number(parentId);

    console.log('البحث عن اسم العميل الدائم بالمعرف:', parentId);
    console.log('معرف العميل الدائم كنص:', parentIdStr);
    console.log('معرف العميل الدائم كرقم:', parentIdNum);

    const parent = customers.find(c =>
      c.id === parentId ||
      c._id === parentId ||
      c.id === parentIdNum ||
      c._id === parentIdNum ||
      String(c.id) === parentIdStr ||
      String(c._id) === parentIdStr
    );

    if (parent) {
      console.log('تم العثور على العميل الدائم:', parent.name, 'بالمعرف:', parent.id);
      // إرجاع اسم العميل الدائم فقط بدون معرفه
      return parent.name;
    } else {
      console.log('لم يتم العثور على العميل الدائم بالمعرف:', parentId);
      return '';
    }
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    if (isMounted.current) {
      console.log(`عرض تنبيه: ${type} - ${message}`);

      // تعيين التنبيه
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء التنبيه بعد 3 ثوان
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;
        }
      }, 3000);
    }
  };

  if (loading || isDeleting) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  // التحقق من صلاحيات المستخدم
  const currentUserRole = localStorage.getItem('currentUserRole');
  const isViewer = currentUserRole === 'viewer';

  return (
    <div className="customers-page">
      <div className="customers-header">
        <h1>العميل</h1>
        <p>إدارة العملاء وعمليات البيع</p>
      </div>

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      <Card
        title="قائمة العملاء"
        icon={<FaUserFriends />}
        actions={
          <Button
            variant="primary"
            icon={<FaPlus />}
            onClick={handleAddCustomer}
          >
            إضافة عميل جديد
          </Button>
        }
      >
        {/* Card content here */}
        <div className="customers-filters">
          <div className="filter-tabs">
            <div
              className={`filter-tab ${selectedType === 'all' ? 'active' : ''}`}
              onClick={() => {
                setSelectedType('all');
                setSelectedParent(null);
              }}
            >
              <FaUsers />
              جميع العملاء
            </div>
            <div
              className={`filter-tab ${selectedType === 'regular' ? 'active' : ''}`}
              onClick={() => {
                setSelectedType('regular');
                setSelectedParent(null);
              }}
            >
              <FaUserFriends />
              العميل الدائم
            </div>
            <div
              className={`filter-tab ${selectedType === 'sub' ? 'active' : ''}`}
              onClick={() => setSelectedType('sub')}
            >
              <FaLink />
              العميل الفرعي
            </div>
            <div
              className={`filter-tab ${selectedType === 'normal' ? 'active' : ''}`}
              onClick={() => {
                setSelectedType('normal');
                setSelectedParent(null);
              }}
            >
              <FaUserAlt />
              العميل العادي
            </div>
          </div>

          <div className="search-container">
            <FaSearch className="search-icon" />
            {selectedType === 'sub' ? (
              <select
                className="parent-filter-select"
                value={selectedParent || ''}
                onChange={(e) => setSelectedParent(e.target.value === '' ? null : e.target.value)}
              >
                <option value="">جميع العملاء الفرعيين</option>
                {getRegularCustomers().map(customer => (
                  <option key={customer.id || customer._id} value={customer.id || customer._id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                className="search-input"
                placeholder="بحث عن عميل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            )}
          </div>
        </div>

        <div className="customers-table-container">
          <table className="customers-table">
            <thead>
              <tr>
                <th className="th-small">#</th>
                <th><FaUser className="th-icon" /> اسم العميل</th>
                <th><FaUserFriends className="th-icon" /> نوع العميل</th>
                <th><FaLink className="th-icon" /> العميل الدائم</th>
                <th><FaPhone className="th-icon" /> رقم الهاتف</th>
                <th><FaWallet className="th-icon" /> الإجمالي</th>
                <th className="th-actions"><FaShoppingCart className="th-icon" /> الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredCustomers.length > 0 ? (
                <>
                  {filteredCustomers.map((customer, index) => {
                    // تحديد ما إذا كان العميل دائم ولديه عملاء فرعيين
                    const isRegularCustomer = customer.customer_type === 'regular';
                    const subCustomers = isRegularCustomer ? getSubCustomersForParent(customer.id) : [];
                    const hasSubCustomers = subCustomers.length > 0;
                    const isExpanded = expandedCustomers[customer.id];

                    return (
                      <React.Fragment key={`customer-${customer.id}`}>
                        <tr className={`fade-in ${isRegularCustomer && hasSubCustomers ? 'has-sub-customers' : ''}`}>
                          <td>{index + 1}</td>
                          <td>
                            {isRegularCustomer ? (
                              <div
                                className="customer-name-with-toggle"
                                onClick={() => {
                                  // عرض رسالة تنبيه عند النقر على العميل الدائم
                                  showAlert('info', `جاري تحميل العملاء الفرعيين للعميل: ${customer.name}`);
                                  // استدعاء وظيفة توسيع/طي العملاء الفرعيين
                                  toggleCustomerExpansion(customer.id);
                                }}
                              >
                                <span className={`toggle-icon ${isExpanded ? 'expanded' : ''}`}>
                                  <FaChevronDown />
                                </span>
                                <strong>{customer.name}</strong>
                                <span className="sub-customers-count">
                                  ({subCustomers.length})
                                </span>
                              </div>
                            ) : (
                              <strong>{customer.name}</strong>
                            )}
                          </td>
                          <td>
                            {customer.customer_type === 'regular' && (
                              <div className="customer-type-badge regular">
                                <FaUserFriends />
                                عميل دائم
                              </div>
                            )}
                            {customer.customer_type === 'sub' && (
                              <div className="customer-type-badge sub">
                                <FaLink />
                                عميل فرعي
                              </div>
                            )}
                            {customer.customer_type === 'normal' && (
                              <div className="customer-type-badge normal">
                                <FaUserAlt />
                                عميل عادي
                              </div>
                            )}
                          </td>
                          <td>
                            {customer.customer_type === 'sub' && customer.parent_id ? (
                              <div className="parent-customer-name">
                                {getParentCustomerName(customer.parent_id)}
                              </div>
                            ) : (
                              '-'
                            )}
                          </td>
                          <td>
                            {customer.phone ? (
                              <div className="contact-info">
                                <FaPhone />
                                {customer.phone}
                              </div>
                            ) : (
                              '-'
                            )}
                          </td>
                          <td>
                            <div className={`customer-balance ${customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : ''}`}>
                              <FaWallet />
                              <FormattedCurrency amount={customer.balance || 0} />
                            </div>
                          </td>
                          <td>
                            <div className="customer-actions">
                              <Button
                                variant="info"
                                size="sm"
                                icon={<FaHistory />}
                                title="سجل المبيعات"
                                onClick={() => handleShowSalesHistory(customer.id)}
                              />
                              <Button
                                variant="success"
                                size="sm"
                                icon={<FaShoppingCart />}
                                title="بيع للعميل"
                                onClick={() => handleSellToCustomer(customer)}
                              />
                              <Button
                                variant="warning"
                                size="sm"
                                icon={<FaUndo />}
                                title="إرجاع من العميل"
                                onClick={() => handleReturnFromCustomer(customer)}
                              />
                              {!isViewer && (
                                <>
                                  <Button
                                    variant="info"
                                    size="sm"
                                    icon={<FaEdit />}
                                    title="تعديل"
                                    onClick={() => handleEditCustomer(customer)}
                                  />
                                  <Button
                                    variant="danger"
                                    size="sm"
                                    icon={<FaTrash />}
                                    title="حذف"
                                    onClick={() => handleDeleteCustomer(customer.id)}
                                  />
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                        {isExpanded && (
                          <>
                            {/* إعادة البحث عن العملاء الفرعيين في كل مرة يتم فيها توسيع العميل الدائم */}
                            {(() => {
                              // الحصول على العملاء الفرعيين المحدثين
                              const updatedSubCustomers = getSubCustomersForParent(customer.id);
                              const updatedHasSubCustomers = updatedSubCustomers.length > 0;

                              // إذا كان هناك عملاء فرعيين، نعرضهم
                              if (updatedHasSubCustomers) {
                                return updatedSubCustomers.map((subCustomer, subIndex) => (
                                  <tr key={`sub-customer-${subCustomer.id || subCustomer._id}-${subIndex}`} className="sub-customer-row fade-in">
                                    <td>{`${index + 1}.${subIndex + 1}`}</td>
                                    <td>
                                      <div className="sub-customer-name">
                                        <span className="sub-customer-indicator">└─</span>
                                        <strong>{subCustomer.name}</strong>
                                      </div>
                                    </td>
                                    <td>
                                      <div className="customer-type-badge sub">
                                        <FaLink />
                                        عميل فرعي
                                      </div>
                                    </td>
                                    <td>
                                      <div className="parent-customer-name">
                                        {getParentCustomerName(subCustomer.parent_id)}
                                      </div>
                                    </td>
                                    <td>
                                      {subCustomer.phone ? (
                                        <div className="contact-info">
                                          <FaPhone />
                                          {subCustomer.phone}
                                        </div>
                                      ) : (
                                        '-'
                                      )}
                                    </td>
                                    <td>
                                      <div className={`customer-balance ${subCustomer.balance > 0 ? 'positive' : subCustomer.balance < 0 ? 'negative' : ''}`}>
                                        <FaWallet />
                                        <FormattedCurrency amount={subCustomer.balance || 0} />
                                      </div>
                                    </td>
                                    <td>
                                      <div className="customer-actions">
                                        <Button
                                          variant="info"
                                          size="sm"
                                          icon={<FaHistory />}
                                          title="سجل المبيعات"
                                          onClick={() => handleShowSalesHistory(subCustomer.id || subCustomer._id)}
                                        />
                                        <Button
                                          variant="success"
                                          size="sm"
                                          icon={<FaShoppingCart />}
                                          title="بيع للعميل"
                                          onClick={() => handleSellToCustomer(subCustomer)}
                                        />
                                        <Button
                                          variant="warning"
                                          size="sm"
                                          icon={<FaUndo />}
                                          title="إرجاع من العميل"
                                          onClick={() => handleReturnFromCustomer(subCustomer)}
                                        />
                                        {!isViewer && (
                                          <>
                                            <Button
                                              variant="info"
                                              size="sm"
                                              icon={<FaEdit />}
                                              title="تعديل"
                                              onClick={() => handleEditCustomer(subCustomer)}
                                            />
                                            <Button
                                              variant="danger"
                                              size="sm"
                                              icon={<FaTrash />}
                                              title="حذف"
                                              onClick={() => handleDeleteCustomer(subCustomer.id || subCustomer._id)}
                                            />
                                          </>
                                        )}
                                      </div>
                                    </td>
                                  </tr>
                                ));
                              } else {
                                // إذا لم يكن هناك عملاء فرعيين، نعرض رسالة
                                return (
                                  <tr className="no-sub-customers-row">
                                    <td colSpan="7" className="text-center">
                                      <div className="no-sub-customers-message">
                                        <FaInfoCircle />
                                        <span>لا يوجد عملاء فرعيين لهذا العميل الدائم</span>
                                      </div>
                                    </td>
                                  </tr>
                                );
                              }
                            })()}
                          </>
                        )}
                      </React.Fragment>
                    );
                  })}
                </>
              ) : (
                <tr>
                  <td colSpan="7" className="empty-table">
                    {searchTerm ? (
                      <div key="no-search-results">
                        <div style={{fontSize: '1.1rem', marginBottom: '0.5rem', fontWeight: 600}}>لا توجد نتائج للبحث</div>
                        <div style={{color: 'var(--text-light)', marginBottom: '1rem'}}>لم يتم العثور على نتائج للبحث</div>
                      </div>
                    ) : (
                      <div key="no-customers">
                        <div style={{fontSize: '1.1rem', marginBottom: '0.5rem', fontWeight: 600}}>لا يوجد عملاء مسجلين</div>
                        <div style={{color: 'var(--text-light)', marginBottom: '1rem'}}>قم بإضافة عملاء جدد للبدء</div>
                        <Button
                          variant="primary"
                          icon={<FaPlus />}
                          onClick={handleAddCustomer}
                        >
                          إضافة عميل جديد
                        </Button>
                      </div>
                    )}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </Card>

      {/* نموذج إضافة/تعديل عميل */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={currentCustomer ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}
        size="lg"
        footer={
          <div key="modal-footer">
            <Button
              variant="light"
              onClick={() => setShowModal(false)}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? 'جاري التحديث...' : (currentCustomer ? 'تحديث' : 'إضافة')}
            </Button>
          </div>
        }
      >
        <div className="modal-subtitle">
          {currentCustomer
            ? 'قم بتعديل بيانات العميل في النموذج التالي'
            : 'قم بإدخال بيانات العميل الجديد في النموذج التالي'}
        </div>

        <div className="modal-info">
          <div className="modal-info-item">
            <FaUserFriends />
            <span>يمكنك إضافة عملاء من مختلف الأنواع</span>
          </div>
          <div className="modal-info-item">
            <FaPhone />
            <span>تأكد من إدخال رقم هاتف صحيح للتواصل</span>
          </div>

        </div>

        <form ref={formRef} className="customer-form" onSubmit={(e) => e.preventDefault()}>
          <div className="form-group full-width">
            <label className="form-label">اسم العميل <span className="required">*</span></label>
            <input
              type="text"
              name="name"
              className="form-control"
              value={formData.name}
              onChange={handleInputChange}
              required
              placeholder="أدخل اسم العميل"
            />
          </div>

          <div className="form-group">
            <label className="form-label">جهة الاتصال</label>
            <input
              type="text"
              name="contact_person"
              className="form-control"
              value={formData.contact_person}
              onChange={handleInputChange}
              placeholder="أدخل اسم جهة الاتصال"
            />
          </div>

          <div className="form-group">
            <label className="form-label">نوع العميل <span className="required">*</span></label>
            <div className="customer-type-buttons">
              <button
                type="button"
                className={`customer-type-button ${formData.customer_type === 'normal' ? 'active' : ''}`}
                onClick={() => handleCustomerTypeChange('normal')}
              >
                <FaUserAlt className="button-icon" />
                عميل عادي
              </button>
              <button
                type="button"
                className={`customer-type-button ${formData.customer_type === 'regular' ? 'active' : ''}`}
                onClick={() => handleCustomerTypeChange('regular')}
              >
                <FaUserFriends className="button-icon" />
                عميل دائم
              </button>
              <button
                type="button"
                className={`customer-type-button ${formData.customer_type === 'sub' ? 'active' : ''}`}
                onClick={() => handleCustomerTypeChange('sub')}
              >
                <FaLink className="button-icon" />
                عميل فرعي
              </button>
            </div>
          </div>

          {formData.customer_type === 'sub' && (
            <div className="form-group">
              <label className="form-label">العميل الدائم <span className="required">*</span></label>
              <select
                name="parent_id"
                className="form-control"
                value={formData.parent_id || ''}
                onChange={(e) => {
                  // تحويل القيمة إلى رقم مباشرة
                  const value = e.target.value === '' ? null : Number(e.target.value);
                  console.log('تم اختيار العميل الدائم:', value);
                  setFormData(prev => ({ ...prev, parent_id: value }));
                }}
                required
              >
                <option value="">اختر العميل الدائم</option>
                {getRegularCustomers().map(customer => {
                  // تحويل معرف العميل إلى رقم
                  const customerId = Number(customer.id || customer._id);
                  return (
                    <option key={customerId} value={customerId}>
                      {customer.name}
                    </option>
                  );
                })}
              </select>
              {formData.parent_id && (
                <div className="selected-parent-info">
                  <p>تم اختيار العميل الدائم: {getParentCustomerName(formData.parent_id)}</p>
                </div>
              )}
            </div>
          )}



          <div className="form-group">
            <label className="form-label">رقم الهاتف</label>
            <input
              type="text"
              name="phone"
              className="form-control"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="أدخل رقم الهاتف"
            />
          </div>

          <div className="form-group">
            <label className="form-label">البريد الإلكتروني</label>
            <input
              type="email"
              name="email"
              className="form-control"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="أدخل البريد الإلكتروني"
            />
          </div>

          <div className="form-group">
            <label className="form-label">العنوان</label>
            <textarea
              name="address"
              className="form-control"
              value={formData.address}
              onChange={handleInputChange}
              rows="3"
              placeholder="أدخل العنوان"
            ></textarea>
          </div>


        </form>
      </Modal>

      {/* نافذة عرض سجل عمليات العميل */}
      {showSalesHistory && (
        <EnhancedCustomerSalesHistory
          customerId={selectedCustomerId}
          onClose={() => setShowSalesHistory(false)}
        />
      )}

      {/* نموذج البيع للعميل */}
      {showSalesModal && currentCustomer && (
        <Modal
          isOpen={showSalesModal}
          onClose={() => setShowSalesModal(false)}
          title={`بيع للعميل: ${currentCustomer.name}`}
          size="xl"
          className="customer-sales-modal"
        >
          {/* Add your sales modal content here, e.g. form, items, summary, etc. */}
        </Modal>
      )}

      {/* نموذج الإرجاع من العميل */}
      {showReturnModal && currentCustomer && (
        <Modal
          isOpen={showReturnModal}
          onClose={() => setShowReturnModal(false)}
          title={`إرجاع من العميل: ${currentCustomer.name}`}
          size="lg"
          className="customer-return-modal"
        >
          {useNewReturnSystem ? (
            // استخدام نموذج الإرجاع المحسن مع النظام الجديد
            <EnhancedReturnForm
              customer={currentCustomer}
              onClose={() => setShowReturnModal(false)}
              onSuccess={(results) => {
                showAlert('success', 'تم إتمام عملية الإرجاع بنجاح');
                setShowReturnModal(false);
              }}
            />
          ) : (
            // استخدام النموذج القديم كاحتياطي
            <div className="return-simple-container">
              <div className="return-header">
                <div className="form-group">
                  <label className="form-label">التاريخ</label>
                  <input
                    type="date"
                    name="transaction_date"
                    className="form-control"
                    value={returnInvoiceData.transaction_date}
                    onChange={handleReturnInvoiceInputChange}
                  />
                </div>
              </div>

              <div className="return-item-selection">
                <h4 className="section-title">اختر الصنف للإرجاع</h4>
                <div className="item-select-container">
                  <ModernItemAutocomplete
                    onSelect={handleReturnItemAutocompleteSelect}
                    placeholder="اكتب اسم الصنف أو الرقم للبحث..."
                    className="return-item-autocomplete"
                  />
                </div>
              </div>

              {returnCurrentItem.item_id && (
                <div className="return-item-details">
                  <h4 className="item-name">{returnCurrentItem.item_name}</h4>

                  <div className="item-quantities">
                    <div className="quantity-box">
                      <div className="quantity-label">الكمية المباعة</div>
                      <div className="quantity-value sold">{returnCurrentItem.availableForReturn + (returnCurrentItem.totalReturned || 0)}</div>
                    </div>

                    <div className="quantity-box">
                      <div className="quantity-label">الكمية المسترجعة سابقاً</div>
                      <div className="quantity-value returned">{returnCurrentItem.totalReturned || 0}</div>
                    </div>

                    <div className="quantity-box">
                      <div className="quantity-label">الكمية المتاحة للإرجاع</div>
                      <div className="quantity-value available">{returnCurrentItem.availableForReturn}</div>
                    </div>
                  </div>

                  <div className="return-form">
                    <div className="form-group">
                      <label className="form-label">الكمية المراد إرجاعها</label>
                      <input
                        type="number"
                        name="quantity"
                        className="form-control"
                        value={returnCurrentItem.quantity}
                        onChange={handleReturnItemInputChange}
                        min="1"
                        max={returnCurrentItem.availableForReturn}
                      />
                      <small className="form-text">الحد الأقصى: {returnCurrentItem.availableForReturn}</small>
                    </div>

                    <div className="form-group">
                      <label className="form-label">سعر الوحدة</label>
                      <input
                        type="number"
                        name="price"
                        className="form-control"
                        value={returnCurrentItem.price}
                        onChange={handleReturnItemInputChange}
                        min="0"
                        step="0.01"
                        readOnly
                      />
                    </div>

                    <div className="form-group">
                      <label className="form-label">الإجمالي</label>
                      <div className="total-amount">
                        <FormattedCurrency
                          amount={returnCurrentItem.quantity * returnCurrentItem.price}
                          decimalPlaces={2}
                          showSymbol={true}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="return-actions">
                    <Button
                      variant="primary"
                      icon={<FaPlus />}
                      onClick={handleAddItemToReturn}
                    >
                      إضافة للإرجاع
                    </Button>
                  </div>
                </div>
              )}

              {returnItems.length > 0 && (
                <div className="return-items-list">
                  <h4 className="section-title">الأصناف المضافة للإرجاع</h4>
                  <div className="return-items-table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>الصنف</th>
                          <th>الكمية</th>
                          <th>الإجمالي</th>
                          <th>حذف</th>
                        </tr>
                      </thead>
                      <tbody>
                        {returnItems.map((item, idx) => (
                          <tr key={`return-item-${item.item_id}-${idx}`}>
                            <td>{item.item_name}</td>
                            <td>{item.quantity}</td>
                            <td><FormattedCurrency amount={item.quantity * item.price} decimalPlaces={2} showSymbol={true} /></td>
                            <td>
                              <Button
                                variant="danger"
                                size="sm"
                                icon={<FaTrash />}
                                onClick={() => handleRemoveItemFromReturn(idx)}
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan="2" className="text-left">الإجمالي:</td>
                          <td colSpan="2" className="total-cell">
                            <FormattedCurrency
                              amount={calculateReturnTotal()}
                              decimalPlaces={2}
                              showSymbol={true}
                            />
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              )}

              <form ref={formRef} onSubmit={handleSubmitReturn} className="return-submit-form">
                <div className="form-actions">
                  <Button
                    variant="secondary"
                    onClick={() => setShowReturnModal(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    variant="warning"
                    type="submit"
                    disabled={returnItems.length === 0}
                  >
                    تنفيذ الإرجاع
                  </Button>
                </div>
              </form>
            </div>
          )}
        </Modal>
      )}

      {/* نموذج البيع للعميل */}
      {showSalesModal && currentCustomer && (
        <Modal
          isOpen={showSalesModal}
          onClose={() => setShowSalesModal(false)}
          title={`بيع للعميل: ${currentCustomer.name}`}
          size="xl"
          className="customer-sales-modal"
        >
          <div className="modal-sales-container">
            <div className="modal-sales-grid">
              {/* القسم الأيمن - معلومات الفاتورة */}
              <div className="modal-sales-sidebar">
                <div className="form-group">
                  <label className="form-label">رقم الفاتورة</label>
                  <input
                    type="text"
                    name="invoice_number"
                    className="form-control"
                    value={invoiceData.invoice_number}
                    onChange={handleInvoiceInputChange}
                    placeholder="أدخل رقم الفاتورة"
                    readOnly
                  />
                  <small className="form-text">يتم توليد رقم الفاتورة تلقائياً</small>
                </div>

                <div className="form-group">
                  <label className="form-label">التاريخ</label>
                  <input
                    type="date"
                    name="transaction_date"
                    className="form-control"
                    value={invoiceData.transaction_date}
                    onChange={handleInvoiceInputChange}
                  />
                </div>

                <div className="invoice-summary">
                  <h4>ملخص الفاتورة</h4>
                  <div className="summary-item">
                    <span>عدد الأصناف:</span>
                    <span>{saleItems.length}</span>
                  </div>
                  <div className="summary-item">
                    <span>إجمالي الكمية:</span>
                    <span>{saleItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  <div className="summary-item total">
                    <span>الإجمالي:</span>
                    <span>
                      <FormattedCurrency
                        amount={calculateTotal()}
                        decimalPlaces={2}
                        showSymbol={true}
                      />
                    </span>
                  </div>
                </div>
              </div>

              {/* القسم الأيسر - الأصناف */}
              <div className="modal-sales-main">
                <form ref={formRef} onSubmit={handleSubmitSale}>
                  <div className="form-section">
                    <h4 className="section-title">إضافة الأصناف</h4>
                    <div className="item-add-form">
                      <div className="item-select">
                        <label className="form-label">الصنف</label>
                        <ModernItemAutocomplete
                          onSelect={handleItemAutocompleteSelect}
                          placeholder="اكتب اسم الصنف أو الرقم للبحث..."
                          className="customers-sales-autocomplete"
                        />
                      </div>
                      <div className="item-quantity">
                        <label className="form-label">الكمية</label>
                        <input
                          type="number"
                          name="quantity"
                          className="form-control"
                          value={currentItem.quantity}
                          onChange={handleItemInputChange}
                          min="1"
                        />
                      </div>
                      <div className="item-price">
                        <label className="form-label">السعر</label>
                        <input
                          type="number"
                          name="price"
                          className="form-control"
                          value={currentItem.price}
                          onChange={handleItemInputChange}
                          min="0"
                          step="0.01"
                        />
                      </div>
                      <div className="item-add-button">
                        <Button
                          variant="primary"
                          type="button"
                          icon={<FaPlus />}
                          onClick={handleAddItemToSale}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="form-section">
                    <h4 className="section-title">
                      <FaClipboardList className="section-icon" />
                      قائمة الأصناف
                    </h4>
                    <div className="sale-items-table">
                      <table className="table">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                            <th>حذف</th>
                          </tr>
                        </thead>
                        <tbody>
                          {saleItems.length > 0 ? (
                            saleItems.map((item, index) => (
                              <tr key={`sale-item-${item.item_id}-${index}`}>
                                <td>{index + 1}</td>
                                <td>{item.item_name}</td>
                                <td>{item.quantity}</td>
                                <td>{item.price.toFixed(2)}</td>
                                <td>{(item.quantity * item.price).toFixed(2)}</td>
                                <td>
                                  <Button
                                    variant="danger"
                                    size="sm"
                                    type="button"
                                    icon={<FaTrash />}
                                    onClick={() => handleRemoveItemFromSale(index)}
                                  />
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="6" className="text-center">
                                لم تتم إضافة أصناف بعد
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>



                  {/* أزرار التحكم */}
                  <div className="form-actions" style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      variant="info"
                      type="button"
                      icon={<FaFileInvoice />}
                      onClick={handleCreateSubInvoice}
                      disabled={isCreatingSubInvoice || saleItems.length === 0}
                    >
                      إنشاء فاتورة فرعية
                    </Button>

                    <div style={{ display: 'flex', gap: '10px' }}>
                      <Button
                        variant="secondary"
                        type="button"
                        onClick={() => setShowSalesModal(false)}
                      >
                        إلغاء
                      </Button>
                      <Button
                        variant="primary"
                        type="submit"
                        icon={<FaReceipt />}
                        disabled={isCreatingSubInvoice}
                      >
                        إتمام عملية البيع
                      </Button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Customers;
