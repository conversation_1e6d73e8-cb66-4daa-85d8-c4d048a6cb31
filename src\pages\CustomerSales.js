import React, { useState, useEffect, useRef } from 'react';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaUser, FaUsers, FaUserFriends, FaUserAlt, FaLink, FaShoppingCart, FaMoneyBillWave, FaReceipt, FaPhone, FaWallet, FaExclamationTriangle, FaHistory, FaTimes, FaPrint, FaFilePdf, FaFileInvoice } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import EnhancedCustomerSalesHistory from '../components/EnhancedCustomerSalesHistory';
import EnhancedItemSearch from '../components/EnhancedItemSearch';
import ItemAutocomplete from '../components/ItemAutocomplete';
import * as database from '../utils/database';
import '../../assets/css/customer-sales.css';
import '../assets/css/customer-sales-history.css';
import '../../assets/css/popup-modals.css';
import '../../assets/css/customer-expansion.css';
import '../../assets/css/item-search-popup.css';

const CustomerSales = () => {
  const {
    customers,
    items,
    inventory,
    transactions,
    addTransaction,
    getCustomersByType,
    getSubCustomers,
    loading
  } = useApp();

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [selectedType, setSelectedType] = useState('all'); // 'all', 'regular', 'sub', 'normal'
  const [selectedParent, setSelectedParent] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showSalesModal, setShowSalesModal] = useState(false);
  const [showSalesHistory, setShowSalesHistory] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState(null);
  const [expandedCustomers, setExpandedCustomers] = useState({});
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  const [isCreatingSubInvoice, setIsCreatingSubInvoice] = useState(false);

  // مرجع للنموذج
  const formRef = useRef(null);

  // مرجع لمؤقت التنبيهات
  const alertTimeoutRef = useRef(null);

  // مرجع لتتبع ما إذا كان المكون لا يزال مثبتًا
  const isMounted = useRef(true);

  // تنظيف المؤقتات عند إلغاء تحميل المكون
  useEffect(() => {
    // تعيين isMounted إلى true عند تحميل المكون
    isMounted.current = true;

    return () => {
      isMounted.current = false;
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);
  const [saleItems, setSaleItems] = useState([]);
  const [currentItem, setCurrentItem] = useState({
    item_id: '',
    quantity: 1,
    price: 0
  });
  const [itemSearchTerm, setItemSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);
  const [showItemSearchPopup, setShowItemSearchPopup] = useState(false);
  const [invoiceData, setInvoiceData] = useState({
    invoice_number: '',
    notes: '',
    transaction_date: new Date().toISOString().split('T')[0]
  });

  // تصفية العملاء بناءً على مصطلح البحث ونوع العميل
  useEffect(() => {
    if (customers && customers.length > 0) {
      let filtered = customers;

      // إذا كان العرض لجميع العملاء، نستبعد العملاء الفرعيين
      if (selectedType === 'all') {
        filtered = filtered.filter(customer => customer.customer_type !== 'sub');
      } else {
        // تصفية حسب النوع
        filtered = filtered.filter(customer => customer.customer_type === selectedType);
      }

      // تصفية العملاء الفرعيين حسب العميل الدائم
      if (selectedType === 'sub' && selectedParent) {
        filtered = filtered.filter(customer => customer.parent_id === selectedParent);
      }

      // تصفية حسب مصطلح البحث
      if (searchTerm) {
        filtered = filtered.filter(customer =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (customer.contact_person && customer.contact_person.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (customer.phone && customer.phone.includes(searchTerm))
        );
      }

      setFilteredCustomers(filtered);
    } else {
      setFilteredCustomers([]);
    }
  }, [customers, searchTerm, selectedType, selectedParent]);

  // تصفية الأصناف بناءً على مصطلح البحث
  useEffect(() => {
    if (items && items.length > 0) {
      if (itemSearchTerm.trim() === '') {
        setFilteredItems(items);
      } else {
        const filtered = items.filter(item =>
          item.name.toLowerCase().includes(itemSearchTerm.toLowerCase()) ||
          (item.id && item.id.toString().includes(itemSearchTerm))
        );
        setFilteredItems(filtered);
      }
    } else {
      setFilteredItems([]);
    }
  }, [items, itemSearchTerm]);

  // إعادة تعيين نموذج البيع
  const resetSaleForm = () => {
    setSaleItems([]);
    setCurrentItem({
      item_id: '',
      quantity: 1,
      price: 0
    });
    setItemSearchTerm('');
    setInvoiceData({
      invoice_number: '',
      notes: '',
      transaction_date: new Date().toISOString().split('T')[0]
    });
  };

  // الحصول على رقم الفاتورة الرئيسية للعميل
  const getCustomerMainInvoiceId = (customer) => {
    if (!customer) {
      console.error('لا يوجد عميل لاستخراج رقم الفاتورة الرئيسية');
      return null;
    }

    // استخدام رقم الفاتورة الرئيسية إذا كان موجودًا
    if (customer.main_invoice_id) {
      console.log(`تم استخدام رقم الفاتورة الرئيسية للعميل: ${customer.main_invoice_id}`);
      return customer.main_invoice_id;
    }

    // إذا لم يكن موجودًا، نستخدم الصيغة الجديدة كاحتياطي
    const fallbackInvoiceId = `H${customer.id.toString().padStart(5, '0')}`;
    console.log(`لم يتم العثور على رقم فاتورة رئيسية للعميل، تم استخدام الصيغة الاحتياطية: ${fallbackInvoiceId}`);
    return fallbackInvoiceId;
  };

  // وظيفة لإعادة تمكين حقول النموذج
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      // الحصول على جميع حقول الإدخال في النموذج
      const inputs = formRef.current.querySelectorAll('input, select, textarea');

      // إعادة تمكين جميع الحقول
      inputs.forEach(input => {
        input.disabled = false;
      });
    }
  };

  // فتح نموذج البيع لعميل
  const handleSellToCustomer = (customer) => {
    setSelectedCustomer(customer);
    setSelectedCustomerId(customer.id);
    resetSaleForm();

    // الحصول على رقم الفاتورة الرئيسية للعميل
    const customerInvoiceNumber = getCustomerMainInvoiceId(customer);
    console.log(`رقم الفاتورة الرئيسية للعميل ${customer.name}:`, customerInvoiceNumber);

    setInvoiceData(prev => ({
      ...prev,
      invoice_number: customerInvoiceNumber
    }));

    setShowSalesModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // فتح نافذة البحث عن الأصناف
  const handleOpenItemSearch = () => {
    setShowItemSearchPopup(true);
  };

  // معالجة اختيار صنف من الإكمال التلقائي
  const handleItemAutocompleteSelect = async (item) => {
    console.log('تم اختيار الصنف من الإكمال التلقائي:', item);

    if (!item) {
      console.warn('لم يتم اختيار أي صنف');
      return;
    }

    try {
      // الحصول على معلومات الصنف للبيع
      const itemInfo = await window.api.customers.getItemInfoForSale(item.id);

      if (itemInfo) {
        console.log('تم استلام معلومات الصنف للبيع:');
        console.log('- المعرف:', itemInfo.id);
        console.log('- الاسم:', itemInfo.name);
        console.log('- الكمية المتوفرة:', itemInfo.current_quantity);
        console.log('- سعر البيع:', itemInfo.selling_price);
        console.log('- متاح للبيع:', itemInfo.available_for_sale);

        if (itemInfo.message) {
          console.log('- رسالة:', itemInfo.message);
        }

        // التحقق من توفر الصنف للبيع
        if (itemInfo.available_for_sale === false) {
          console.warn(`الصنف ${itemInfo.name} غير متاح للبيع: ${itemInfo.message}`);
          showAlert('warning', itemInfo.message || `الصنف ${itemInfo.name} غير متاح للبيع`);
          return;
        }

        // تعيين الصنف الحالي باستخدام البيانات المستلمة
        const newItem = {
          item_id: itemInfo.id,
          item_name: itemInfo.name,
          unit: itemInfo.unit || 'قطعة',
          quantity: 1,
          price: typeof itemInfo.selling_price === 'number' ? itemInfo.selling_price : 0,
          available_quantity: typeof itemInfo.current_quantity === 'number' ? itemInfo.current_quantity : 0,
          minimum_quantity: typeof itemInfo.minimum_quantity === 'number' ? itemInfo.minimum_quantity : 0
        };

        setCurrentItem(newItem);
        console.log(`تم تعيين الصنف الحالي:`, newItem);
      }
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الصنف للبيع:', error);

      // في حالة الخطأ، استخدم البيانات المتاحة
      setCurrentItem({
        item_id: item.id,
        item_name: item.name,
        unit: item.unit || 'قطعة',
        quantity: 1,
        price: typeof item.selling_price === 'number' ? item.selling_price : 0,
        available_quantity: typeof item.current_quantity === 'number' ? item.current_quantity : 0,
        minimum_quantity: typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0
      });
    }
  };

  // معالجة اختيار صنف من نافذة البحث
  const handleItemSelect = async (item) => {
    console.log('تم اختيار الصنف من نافذة البحث:', item);
    setShowItemSearchPopup(false);

    if (!item) {
      console.warn('لم يتم اختيار أي صنف');
      return;
    }

    // التحقق من وجود معرف الصنف
    if (!item.id) {
      console.error('الصنف المختار لا يحتوي على معرف:', item);
      showAlert('danger', 'خطأ في اختيار الصنف، يرجى المحاولة مرة أخرى');
      return;
    }

    // التحقق من توفر الصنف للبيع
    if (item.available_for_sale === false) {
      console.warn(`الصنف ${item.name} غير متاح للبيع: ${item.message}`);
      showAlert('warning', item.message || `الصنف ${item.name} غير متاح للبيع`);
      return;
    }

    // تعيين الصنف المختار
    const itemId = item.id;
    const itemName = item.name || 'صنف بدون اسم';
    const itemUnit = item.unit || 'قطعة';
    const sellingPrice = typeof item.selling_price === 'number' ? item.selling_price : 0;
    const availableQuantity = typeof item.current_quantity === 'number' ? item.current_quantity : 0;
    const minimumQuantity = typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0;

    console.log(`معلومات الصنف المستلمة من نافذة البحث المحسنة:`);
    console.log(`- معرف الصنف: ${itemId}`);
    console.log(`- اسم الصنف: ${itemName}`);
    console.log(`- الوحدة: ${itemUnit}`);
    console.log(`- سعر البيع: ${sellingPrice}`);
    console.log(`- الكمية المتوفرة: ${availableQuantity}`);
    console.log(`- الحد الأدنى: ${minimumQuantity}`);

    // تعيين الصنف الحالي باستخدام البيانات المستلمة
    const newItem = {
      item_id: itemId,
      item_name: itemName,
      unit: itemUnit,
      quantity: 1,
      price: sellingPrice,
      available_quantity: availableQuantity,
      minimum_quantity: minimumQuantity
    };

    setCurrentItem(newItem);
    console.log(`تم تعيين الصنف الحالي:`, newItem);

    // إنشاء كائن يحتوي على جميع بيانات الصنف المطلوبة
    const itemData = {
      id: itemId,
      name: itemName,
      unit: itemUnit,
      current_quantity: availableQuantity,
      selling_price: sellingPrice,
      minimum_quantity: minimumQuantity
    };

    // إضافة الصنف مباشرة إلى قائمة البيع
    processItemAddition(itemData, itemName, availableQuantity);
  };

  // تم حذف دالة handleItemChange لأنها لم تعد مستخدمة

  // معالجة تغيير الكمية أو السعر
  const handleItemInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentItem(prev => ({
      ...prev,
      [name]: name === 'quantity' ? parseInt(value) || 1 : parseFloat(value) || 0
    }));
  };

  // معالجة تغيير بيانات الفاتورة
  const handleInvoiceInputChange = (e) => {
    const { name, value } = e.target;
    setInvoiceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إضافة صنف إلى قائمة البيع
  const handleAddItemToSale = async () => {
    if (!currentItem.item_id) {
      showAlert('danger', 'يرجى اختيار صنف');
      return;
    }

    if (currentItem.quantity <= 0) {
      showAlert('danger', 'يجب أن تكون الكمية أكبر من صفر');
      return;
    }

    if (currentItem.price <= 0) {
      showAlert('danger', 'يجب أن يكون السعر أكبر من صفر');
      return;
    }

    // استخدام الوظيفة الجديدة للحصول على معلومات الصنف للبيع
    try {
      console.log('محاولة الحصول على معلومات الصنف للبيع:', currentItem.item_id);

      // استدعاء وظيفة getItemInfoForSale للحصول على أحدث معلومات الصنف
      const itemInfo = await window.api.customers.getItemInfoForSale(currentItem.item_id);

      if (itemInfo) {
        console.log('تم استلام معلومات الصنف للبيع:', itemInfo);

        // التحقق من توفر الصنف للبيع
        if (itemInfo.available_for_sale === false) {
          console.warn(`الصنف ${itemInfo.name} غير متاح للبيع: ${itemInfo.message}`);
          showAlert('warning', itemInfo.message || `الصنف ${itemInfo.name} غير متاح للبيع`);
          return;
        }

        // استخدام معلومات الصنف من قاعدة البيانات
        const itemName = itemInfo.name;
        const availableQuantity = typeof itemInfo.current_quantity === 'number' ? itemInfo.current_quantity : 0;

        console.log(`الكمية المتوفرة من قاعدة البيانات للصنف ${itemName}: ${availableQuantity}`);

        // التحقق من توفر الكمية المطلوبة
        if (availableQuantity < currentItem.quantity) {
          showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
          return;
        }

        console.log(`الكمية المتوفرة كافية للصنف ${itemName}: ${availableQuantity} >= ${currentItem.quantity}`);

        // استمر بالعملية مع البيانات المستلمة من قاعدة البيانات
        processItemAddition(itemInfo, itemName, availableQuantity);
        return;
      }
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الصنف للبيع:', error);
      // في حالة الخطأ، نستمر بالطريقة القديمة
    }

    // الطريقة القديمة كاحتياطي في حالة فشل الحصول على البيانات من قاعدة البيانات
    // البحث عن الصنف في المخزون أولاً للحصول على أحدث معلومات
    const inventoryItem = inventory.find(item =>
      item.item_id === currentItem.item_id ||
      item.id === currentItem.item_id ||
      item.id === parseInt(currentItem.item_id) ||
      item._id === currentItem.item_id
    );

    // البحث عن الصنف في قائمة الأصناف كاحتياطي
    const selectedItem = items.find(item =>
      item.id === currentItem.item_id ||
      item.id === parseInt(currentItem.item_id) ||
      item._id === currentItem.item_id
    );

    // استخدام معلومات الصنف من المخزون إذا كانت متاحة، وإلا استخدام معلومات الصنف من قائمة الأصناف
    const itemToUse = inventoryItem || selectedItem;

    if (!itemToUse) {
      showAlert('danger', 'الصنف غير موجود');
      return;
    }

    // الحصول على اسم الصنف واستخدام الكمية المتوفرة من المخزون
    const itemName = itemToUse.name || itemToUse.item_name;

    // الحصول على الكمية المتوفرة من المخزون أو من جدول items
    let availableQuantity = 0;

    if (inventoryItem && typeof inventoryItem.current_quantity === 'number') {
      availableQuantity = inventoryItem.current_quantity;
      console.log(`الكمية المتوفرة من المخزون للصنف ${itemName}: ${availableQuantity}`);
    } else if (selectedItem && typeof selectedItem.current_quantity === 'number') {
      availableQuantity = selectedItem.current_quantity;
      console.log(`الكمية المتوفرة من جدول items للصنف ${itemName}: ${availableQuantity}`);
    } else {
      console.warn(`لم يتم العثور على كمية متوفرة للصنف ${itemName}`);
    }

    // التحقق من توفر الكمية في المخزون
    if (availableQuantity < 1) {
      showAlert('danger', `الكمية المتوفرة من ${itemName} غير كافية للبيع`);
      return;
    }

    if (availableQuantity < currentItem.quantity) {
      showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
      return;
    }

    console.log(`الكمية المتوفرة كافية للصنف ${itemName}: ${availableQuantity} >= ${currentItem.quantity}`);

    // استمر بالعملية مع البيانات المستلمة من الذاكرة المؤقتة
    processItemAddition(itemToUse, itemName, availableQuantity);
  };

  // دالة معالجة المعاملة
  const processTransaction = async (item, _itemData, itemName, finalPrice) => {
    try {
      // إنشاء معاملة بيع
      const transaction = await addTransaction({
        item_id: parseInt(item.item_id),
        transaction_type: 'sale',
        quantity: item.quantity,
        price: finalPrice,
        total_price: item.quantity * finalPrice, // إضافة السعر الإجمالي بشكل صريح
        customer_id: selectedCustomer.id, // تم تغيير customer إلى customer_id
        customer: selectedCustomer.name, // استخدام اسم العميل في حقل customer
        item_name: itemName, // استخدام اسم الصنف المستخرج
        invoice_number: invoiceData.invoice_number,
        notes: invoiceData.notes,
        transaction_date: invoiceData.transaction_date ? new Date(invoiceData.transaction_date).toISOString() : new Date().toISOString()
      });

      return transaction;
    } catch (error) {
      console.error('خطأ في إنشاء معاملة البيع:', error);
      showAlert('danger', 'حدث خطأ أثناء إنشاء معاملة البيع');
      return null;
    }
  };

  // تحديث بيانات العميل بعد إتمام عملية البيع
  const refreshCustomerData = async (customerId) => {
    try {
      console.log('جاري تحديث بيانات العميل بعد إتمام عملية البيع، معرف العميل:', customerId);

      // الحصول على بيانات العميل المحدثة من قاعدة البيانات
      const customerData = await window.api.customers.getCustomerById(customerId);

      if (!customerData) {
        console.error('فشل في الحصول على بيانات العميل المحدثة: لم يتم العثور على العميل');
        return false;
      }

      console.log('تم الحصول على بيانات العميل المحدثة:', customerData);

      // تحديث العميل في قائمة العملاء
      setCustomers(prevCustomers => {
        // إنشاء نسخة جديدة من مصفوفة العملاء
        const updatedCustomers = prevCustomers.map(customer => {
          if (customer.id === customerId || customer._id === customerId) {
            console.log('تحديث بيانات العميل في القائمة:', customerData);
            // إنشاء كائن جديد للعميل مع البيانات المحدثة
            return {
              ...customer,
              balance: customerData.balance,
              credit_limit: customerData.credit_limit,
              // تحديث جميع الحقول الأخرى
              name: customerData.name,
              contact_person: customerData.contact_person,
              phone: customerData.phone,
              email: customerData.email,
              address: customerData.address,
              customer_type: customerData.customer_type,
              parent_id: customerData.parent_id,
              parent_name: customerData.parent_name,
              created_at: customerData.created_at
            };
          }
          return customer;
        });

        console.log('قائمة العملاء المحدثة:', updatedCustomers);
        return updatedCustomers;
      });

      // تحديث العميل المحدد أيضًا إذا كان هو نفس العميل الذي تم تحديثه
      if (selectedCustomer && (selectedCustomer.id === customerId || selectedCustomer._id === customerId)) {
        console.log('تحديث العميل المحدد أيضًا:', customerData);
        setSelectedCustomer(prevSelected => ({
          ...prevSelected,
          balance: customerData.balance,
          credit_limit: customerData.credit_limit,
          // تحديث جميع الحقول الأخرى
          name: customerData.name,
          contact_person: customerData.contact_person,
          phone: customerData.phone,
          email: customerData.email,
          address: customerData.address,
          customer_type: customerData.customer_type,
          parent_id: customerData.parent_id,
          parent_name: customerData.parent_name,
          created_at: customerData.created_at
        }));
      }

      // تحديث جميع العملاء في السياق العام للتطبيق
      if (window.updateCustomerData) {
        window.updateCustomerData(customerData);
        console.log('تم تحديث بيانات العميل في السياق العام للتطبيق');
      }

      console.log('تم تحديث بيانات العميل بنجاح');
      return true;
    } catch (error) {
      console.error('خطأ في تحديث بيانات العميل:', error);
      return false;
    }
  };

  // دالة معالجة إضافة الصنف إلى قائمة البيع
  const processItemAddition = (itemData, itemName, availableQuantity) => {
    console.log('معالجة إضافة الصنف إلى قائمة البيع:', {
      itemData,
      itemName,
      availableQuantity,
      currentItem
    });

    // التحقق من صحة البيانات
    if (!itemData || !itemData.id) {
      console.error('بيانات الصنف غير صالحة:', itemData);
      showAlert('danger', 'خطأ في بيانات الصنف، يرجى المحاولة مرة أخرى');
      return;
    }

    // التحقق من توفر الكمية
    if (typeof availableQuantity !== 'number' || availableQuantity <= 0) {
      console.warn(`الكمية المتوفرة من الصنف ${itemName} غير كافية للبيع (${availableQuantity})`);
      showAlert('warning', `الكمية المتوفرة من الصنف ${itemName} غير كافية للبيع`);
      return;
    }

    // التحقق من وجود الصنف في القائمة
    const itemId = itemData.id;
    const existingItemIndex = saleItems.findIndex(item =>
      item.item_id === itemId ||
      parseInt(item.item_id) === parseInt(itemId)
    );
    console.log(`هل الصنف موجود في القائمة؟ ${existingItemIndex !== -1 ? 'نعم' : 'لا'}`);

    // التأكد من استخدام سعر البيع الصحيح
    let finalPrice = currentItem.price;
    console.log(`السعر الأولي من النموذج الحالي: ${finalPrice}`);

    // إذا كان السعر صفر أو غير محدد، نحاول استخدام سعر البيع من البيانات المقدمة
    if (finalPrice <= 0) {
      if (itemData && typeof itemData.selling_price === 'number' && itemData.selling_price > 0) {
        finalPrice = itemData.selling_price;
        console.log(`تم استخدام سعر البيع من البيانات المقدمة: ${finalPrice}`);
      } else {
        // إذا لم نجد سعر بيع، نستخدم قيمة افتراضية
        finalPrice = 0.01;
        console.log(`لم يتم العثور على سعر بيع صالح، استخدام القيمة الافتراضية: ${finalPrice}`);
      }
    }

    console.log(`السعر النهائي المستخدم: ${finalPrice}`);

    // التحقق من الكمية المطلوبة
    const requestedQuantity = currentItem.quantity || 1;
    if (requestedQuantity <= 0) {
      console.warn('الكمية المطلوبة يجب أن تكون أكبر من صفر');
      showAlert('warning', 'الكمية المطلوبة يجب أن تكون أكبر من صفر');
      return;
    }

    // التحقق من توفر الكمية المطلوبة
    if (requestedQuantity > availableQuantity) {
      console.warn(`الكمية المطلوبة (${requestedQuantity}) أكبر من الكمية المتوفرة (${availableQuantity})`);
      showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
      return;
    }

    // الحصول على وحدة القياس
    const itemUnit = itemData.unit || 'قطعة';

    if (existingItemIndex !== -1) {
      // تحديث الصنف الموجود
      const updatedItems = [...saleItems];
      const newQuantity = updatedItems[existingItemIndex].quantity + requestedQuantity;

      console.log(`تحديث الصنف الموجود - الكمية الحالية: ${updatedItems[existingItemIndex].quantity}, الكمية المضافة: ${requestedQuantity}, الكمية الجديدة: ${newQuantity}`);

      // التحقق من توفر الكمية الإجمالية
      if (newQuantity > availableQuantity) {
        console.warn(`الكمية الإجمالية المطلوبة (${newQuantity}) أكبر من الكمية المتوفرة (${availableQuantity})`);
        showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
        return;
      }

      // حساب السعر الإجمالي
      const totalPrice = newQuantity * finalPrice;

      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: newQuantity,
        price: finalPrice,
        total: totalPrice,
        unit: itemUnit
      };

      console.log(`تم تحديث الصنف في القائمة:`, updatedItems[existingItemIndex]);
      setSaleItems(updatedItems);

      // عرض رسالة نجاح
      showAlert('success', `تم تحديث كمية ${itemName} في قائمة البيع بنجاح`);
    } else {
      // إضافة صنف جديد
      const totalPrice = requestedQuantity * finalPrice;

      const newItem = {
        item_id: itemId,
        item_name: itemName,
        quantity: requestedQuantity,
        price: finalPrice,
        total: totalPrice,
        unit: itemUnit
      };

      console.log(`إضافة صنف جديد إلى القائمة:`, newItem);
      setSaleItems([...saleItems, newItem]);

      // عرض رسالة نجاح
      showAlert('success', `تم إضافة ${itemName} إلى قائمة البيع بنجاح`);
    }

    // إعادة تعيين نموذج الصنف
    setCurrentItem({
      item_id: '',
      quantity: 1,
      price: 0
    });

    // إعادة تعيين مصطلح البحث
    setItemSearchTerm('');

    console.log('تم إضافة الصنف بنجاح وإعادة تعيين النموذج');
  };

  // حذف صنف من قائمة البيع
  const handleRemoveItemFromSale = (index) => {
    const updatedItems = [...saleItems];
    updatedItems.splice(index, 1);
    setSaleItems(updatedItems);
  };

  // حساب إجمالي الفاتورة
  const calculateTotal = () => {
    return saleItems.reduce((total, item) => total + (item.quantity * item.price), 0);
  };

  // معالجة إرسال نموذج البيع
  const handleSubmitSale = async (e) => {
    e.preventDefault();

    try {
      if (isCreatingSubInvoice) {
        showAlert('warning', 'جاري إنشاء فاتورة فرعية، يرجى الانتظار...');
        return;
      }

      if (saleItems.length === 0) {
        showAlert('danger', 'يرجى إضافة صنف واحد على الأقل');
        return;
      }

      // تم إلغاء الارتباط بين العميل الفرعي والرئيسي في عمليات البيع
      // كل عميل يحتفظ بسجل مبيعات خاص به

      // إنشاء معاملات البيع
      const completedTransactions = [];
      for (const item of saleItems) {
        try {
          // استخدام الوظيفة الجديدة للحصول على معلومات الصنف للبيع
          console.log('محاولة الحصول على معلومات الصنف للبيع:', item.item_id);

          // استدعاء وظيفة getItemInfoForSale للحصول على أحدث معلومات الصنف
          const itemInfo = await window.api.customers.getItemInfoForSale(item.item_id);

          if (itemInfo) {
            console.log('تم استلام معلومات الصنف للبيع:', itemInfo);

            // التحقق من توفر الصنف للبيع
            if (itemInfo.available_for_sale === false) {
              console.warn(`الصنف ${itemInfo.name} غير متاح للبيع: ${itemInfo.message}`);
              showAlert('warning', itemInfo.message || `الصنف ${itemInfo.name} غير متاح للبيع`);
              return;
            }

            // استخدام معلومات الصنف من قاعدة البيانات
            const itemName = itemInfo.name;
            const availableQuantity = typeof itemInfo.current_quantity === 'number' ? itemInfo.current_quantity : 0;

            console.log(`الكمية المتوفرة من قاعدة البيانات للصنف ${itemName}: ${availableQuantity}`);

            // التحقق من توفر الكمية المطلوبة
            if (availableQuantity < item.quantity) {
              showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
              return;
            }

            console.log(`الكمية المتوفرة كافية للصنف ${itemName}: ${availableQuantity} >= ${item.quantity}`);

            // التأكد من استخدام سعر البيع الصحيح
            let finalPrice = item.price;

            // إذا كان السعر صفر، نحاول استخدام سعر البيع من قاعدة البيانات
            if (finalPrice <= 0) {
              if (itemInfo && typeof itemInfo.selling_price === 'number' && itemInfo.selling_price > 0) {
                finalPrice = itemInfo.selling_price;
                console.log(`تم استخدام سعر البيع من قاعدة البيانات: ${finalPrice}`);
              } else {
                // إذا لم نجد سعر بيع، نستخدم قيمة افتراضية
                finalPrice = 0.01;
                console.log(`لم يتم العثور على سعر بيع صالح، استخدام القيمة الافتراضية: ${finalPrice}`);
              }
            }

            // استمر بالعملية مع البيانات المستلمة من قاعدة البيانات
            const transaction = await processTransaction(item, itemInfo, itemName, finalPrice);
            if (transaction) {
              completedTransactions.push(transaction);
            }

            // انتقل إلى العنصر التالي
            continue;
          }
        } catch (error) {
          console.error('خطأ في الحصول على معلومات الصنف من قاعدة البيانات للبيع:', error);
          // في حالة الخطأ، نستمر بالطريقة القديمة
        }

        // الطريقة القديمة كاحتياطي في حالة فشل الحصول على البيانات من قاعدة البيانات
        // البحث عن الصنف في المخزون أولاً للحصول على أحدث معلومات
        const inventoryItem = inventory.find(i =>
          i.item_id === item.item_id ||
          i.id === item.item_id ||
          i.id === parseInt(item.item_id) ||
          i._id === item.item_id
        );

        // البحث عن الصنف في قائمة الأصناف كاحتياطي
        const selectedItem = items.find(i =>
          i.id === item.item_id ||
          i.id === parseInt(item.item_id) ||
          i._id === item.item_id
        );

        // استخدام معلومات الصنف من المخزون إذا كانت متاحة، وإلا استخدام معلومات الصنف من قائمة الأصناف
        const itemToUse = inventoryItem || selectedItem;

        if (!itemToUse) {
          showAlert('danger', 'أحد الأصناف غير موجود');
          return;
        }

        // الحصول على اسم الصنف واستخدام الكمية المتوفرة من المخزون
        const itemName = itemToUse.name || itemToUse.item_name;

        // الحصول على الكمية المتوفرة من المخزون أو من جدول items
        let availableQuantity = 0;

        if (inventoryItem && typeof inventoryItem.current_quantity === 'number') {
          availableQuantity = inventoryItem.current_quantity;
          console.log(`الكمية المتوفرة من المخزون للصنف ${itemName}: ${availableQuantity}`);
        } else if (selectedItem && typeof selectedItem.current_quantity === 'number') {
          availableQuantity = selectedItem.current_quantity;
          console.log(`الكمية المتوفرة من جدول items للصنف ${itemName}: ${availableQuantity}`);
        } else {
          console.warn(`لم يتم العثور على كمية متوفرة للصنف ${itemName}`);
        }

        // التحقق من توفر الكمية في المخزون
        if (availableQuantity < 1) {
          showAlert('danger', `الكمية المتوفرة من ${itemName} غير كافية للبيع`);
          return;
        }

        if (availableQuantity < item.quantity) {
          showAlert('danger', `الكمية المتوفرة من ${itemName} هي ${availableQuantity} فقط`);
          return;
        }

        console.log(`الكمية المتوفرة كافية للصنف ${itemName}: ${availableQuantity} >= ${item.quantity}`);

        // التأكد من استخدام سعر البيع الصحيح
        let finalPrice = item.price;

        // إذا كان السعر صفر، نحاول استخدام سعر البيع من المخزون أو من الصنف
        if (finalPrice <= 0) {
          if (inventoryItem && typeof inventoryItem.selling_price === 'number' && inventoryItem.selling_price > 0) {
            finalPrice = inventoryItem.selling_price;
            console.log(`تم استخدام سعر البيع من المخزون: ${finalPrice}`);
          } else if (selectedItem && typeof selectedItem.selling_price === 'number' && selectedItem.selling_price > 0) {
            finalPrice = selectedItem.selling_price;
            console.log(`تم استخدام سعر البيع من جدول الأصناف: ${finalPrice}`);
          } else {
            // إذا لم نجد سعر بيع، نستخدم قيمة افتراضية
            finalPrice = 0.01;
            console.log(`لم يتم العثور على سعر بيع صالح، استخدام القيمة الافتراضية: ${finalPrice}`);
          }
        }

        // استخدام دالة processTransaction للطريقة القديمة
        const transaction = await processTransaction(item, itemToUse, itemName, finalPrice);
        if (transaction) {
          completedTransactions.push(transaction);
        }
      }

      // عرض رسالة نجاح تتضمن إرشادات لطباعة الفاتورة
      if (completedTransactions.length > 0) {
        // إظهار رسالة للمستخدم تشير إلى إمكانية طباعة الفاتورة من سجل المبيعات
        showAlert('success', 'تم إتمام عملية البيع بنجاح. يمكنك طباعة الفاتورة من سجل المبيعات.');

        // تحديث بيانات العميل المحدد بعد إتمام عملية البيع
        console.log('جاري تحديث بيانات العميل بعد إتمام عملية البيع...');
        try {
          // استخدام الدالة الجديدة لتحديث بيانات العميل
          const customerId = selectedCustomer.id;
          console.log('معرف العميل المراد تحديثه:', customerId);

          // تأخير أطول لضمان اكتمال عملية البيع في قاعدة البيانات
          setTimeout(async () => {
            console.log('محاولة تحديث بيانات العميل بعد التأخير...');
            const refreshResult = await refreshCustomerData(customerId);

            if (refreshResult) {
              console.log('تم تحديث بيانات العميل بنجاح بعد إتمام عملية البيع');

              // محاولة ثانية بعد تأخير إضافي للتأكد من تحديث واجهة المستخدم
              setTimeout(async () => {
                console.log('محاولة ثانية لتحديث بيانات العميل...');
                await refreshCustomerData(customerId);

                // تحديث جميع العملاء أيضًا للتأكد من تحديث الجدول
                try {
                  console.log('تحديث جميع العملاء للتأكد من تحديث الجدول...');
                  const updatedCustomers = await database.getCustomers();

                  if (Array.isArray(updatedCustomers)) {
                    console.log('تم الحصول على بيانات جميع العملاء المحدثة:', updatedCustomers.length);

                    // تحديث حالة العملاء في السياق
                    setCustomers(updatedCustomers);
                    console.log('تم تحديث حالة جميع العملاء بنجاح');

                    // إعادة تحميل الصفحة إذا كان ذلك ضروريًا (تعليق هذا السطر إذا لم تكن تريد إعادة تحميل الصفحة)
                    // window.location.reload();
                  }
                } catch (secondFallbackError) {
                  console.error('خطأ في التحديث الثاني لجميع العملاء:', secondFallbackError);
                }
              }, 1000);
            } else {
              console.warn('لم يتم تحديث بيانات العميل بنجاح، محاولة تحديث جميع العملاء...');

              // محاولة تحديث جميع العملاء كخطة بديلة
              try {
                const updatedCustomers = await database.getCustomers();

                if (Array.isArray(updatedCustomers)) {
                  console.log('تم الحصول على بيانات العملاء المحدثة:', updatedCustomers.length);

                  // تحديث حالة العملاء في السياق
                  setCustomers(updatedCustomers);
                  console.log('تم تحديث حالة العملاء بنجاح');
                }
              } catch (fallbackError) {
                console.error('خطأ في تحديث جميع العملاء:', fallbackError);
              }
            }
          }, 1000);
        } catch (updateError) {
          console.error('خطأ في تحديث بيانات العميل بعد إتمام عملية البيع:', updateError);
        }
      }

      // إغلاق النافذة وإعادة تعيين النموذج
      if (isMounted.current) {
        setShowSalesModal(false);
        resetSaleForm();
      }
    } catch (err) {
      console.error('Error processing sale:', err);
      showAlert('danger', err.message || 'فشل في إتمام عملية البيع');
    }
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    if (isMounted.current) {
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء التنبيه بعد 3 ثوان
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;
        }
      }, 3000);
    }
  };

  // الحصول على العملاء الدائمين
  const getRegularCustomers = () => {
    return customers.filter(c => c.customer_type === 'regular');
  };

  // الحصول على العملاء الفرعيين لعميل دائم
  const getSubCustomersForParent = (parentId) => {
    if (!parentId) return [];

    // تحويل parentId إلى نص للمقارنة المتسقة
    const parentIdStr = String(parentId);

    // البحث عن العملاء الفرعيين بمراعاة أن parent_id قد يكون رقمًا أو نصًا
    return customers.filter(c =>
      c.customer_type === 'sub' && (
        c.parent_id === parentId ||
        (c.parent_id && String(c.parent_id) === parentIdStr)
      )
    );
  };

  // توسيع/طي العملاء الفرعيين للعميل الدائم
  const toggleCustomerExpansion = (customerId) => {
    setExpandedCustomers(prev => ({
      ...prev,
      [customerId]: !prev[customerId]
    }));
  };

  // الحصول على اسم العميل الدائم
  const getParentCustomerName = (parentId) => {
    const parent = customers.find(c => c.id === parentId);
    return parent ? parent.name : '';
  };

  // عرض سجل مبيعات العميل
  const handleShowSalesHistory = (customerId) => {
    setSelectedCustomerId(customerId);
    setShowSalesHistory(true);
  };

  // إنشاء فاتورة فرعية
  const handleCreateSubInvoice = async () => {
    try {
      if (saleItems.length === 0) {
        showAlert('danger', 'يرجى إضافة صنف واحد على الأقل للفاتورة الفرعية');
        return;
      }

      setIsCreatingSubInvoice(true);

      // الحصول على رقم الفاتورة الرئيسية
      const parentInvoiceNumber = invoiceData.invoice_number;

      if (!parentInvoiceNumber) {
        showAlert('danger', 'رقم الفاتورة الرئيسية غير متوفر');
        setIsCreatingSubInvoice(false);
        return;
      }

      // تحضير الأصناف المختارة للفاتورة الفرعية
      const selectedItems = saleItems.map(item => ({
        item_id: item.item_id,
        quantity: item.quantity,
        price: item.price,
        total_price: item.quantity * item.price,
        profit: 0 // سيتم حسابه في الخادم
      }));

      // استدعاء وظيفة إنشاء فاتورة فرعية
      const result = await window.api.customers.createSubInvoice(
        parentInvoiceNumber,
        selectedCustomer.id,
        selectedItems
      );

      if (result.success) {
        showAlert('success', `تم إنشاء الفاتورة الفرعية بنجاح: ${result.subInvoice.invoice_number}`);

        // إغلاق النافذة وإعادة تعيين النموذج
        if (isMounted.current) {
          setShowSalesModal(false);
          resetSaleForm();
        }
      } else {
        showAlert('danger', result.error || 'فشل في إنشاء الفاتورة الفرعية');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة الفرعية:', error);
      showAlert('danger', error.message || 'حدث خطأ أثناء إنشاء الفاتورة الفرعية');
    } finally {
      setIsCreatingSubInvoice(false);
    }
  };

  return (
    <div className="container-fluid mt-4">
      {alert.show && (
        <div className={`alert alert-${alert.type} alert-dismissible fade show`} role="alert">
          {alert.message}
        </div>
      )}

      <div className="page-header hover-lift">
        <div className="header-content">
          <h2>
            <FaShoppingCart className="header-icon" />
            البيع للعملاء
          </h2>
          <p>اختر العميل المطلوب وقم بإضافة الأصناف للبيع</p>
        </div>
        <div className="header-actions">
          <button className="btn-add" onClick={() => window.location.href = '#/customers'}>
            <FaPlus className="icon-left" />
            إضافة عميل جديد
          </button>
        </div>
      </div>

      <div className="card hover-lift">
        <div className="card-header">
          <h3 className="card-title">
            <FaUsers className="header-icon" />
            قائمة العملاء
          </h3>
          <div className="card-actions">
            <span className="customer-count">
              {filteredCustomers.length} عميل
            </span>
          </div>
        </div>
        <div className="card-body">
          <div className="filter-container">
            <div className="search-container">
              <div className="search-input-wrapper">
                <FaSearch className="search-icon" />
                <input
                  type="text"
                  className="search-input"
                  placeholder="بحث عن عميل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="filter-buttons">
              <button
                className={`filter-btn ${selectedType === 'all' ? 'active' : ''}`}
                onClick={() => {
                  setSelectedType('all');
                  setSelectedParent(null);
                }}
              >
                <FaUsers className="icon-left" />
                جميع العملاء
              </button>
              <button
                className={`filter-btn ${selectedType === 'regular' ? 'active' : ''}`}
                onClick={() => {
                  setSelectedType('regular');
                  setSelectedParent(null);
                }}
              >
                <FaUserFriends className="icon-left" />
                العملاء الدائمين
              </button>
              <button
                className={`filter-btn ${selectedType === 'sub' ? 'active' : ''}`}
                onClick={() => setSelectedType('sub')}
              >
                <FaLink className="icon-left" />
                العملاء الفرعيين
              </button>
              <button
                className={`filter-btn ${selectedType === 'normal' ? 'active' : ''}`}
                onClick={() => {
                  setSelectedType('normal');
                  setSelectedParent(null);
                }}
              >
                <FaUserAlt className="icon-left" />
                العملاء العاديين
              </button>
            </div>
          </div>

          {selectedType === 'sub' && (
            <div className="filter-dropdown fade-in hover-lift">
              <label><FaUserFriends className="icon-left" style={{color: 'var(--primary-color)'}} /> اختر العميل الدائم:</label>
              <select
                className="form-select"
                value={selectedParent || ''}
                onChange={(e) => setSelectedParent(e.target.value === '' ? null : e.target.value)}
              >
                <option value="">جميع العملاء الفرعيين</option>
                {getRegularCustomers().map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th className="th-small">#</th>
                  <th><FaUser className="th-icon" /> اسم العميل</th>
                  <th><FaUserFriends className="th-icon" /> نوع العميل</th>
                  <th><FaLink className="th-icon" /> العميل الدائم</th>
                  <th><FaPhone className="th-icon" /> رقم الهاتف</th>
                  <th><FaWallet className="th-icon" /> الإجمالي (د ل)</th>
                  <th className="th-actions"><FaShoppingCart className="th-icon" /> الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredCustomers.length > 0 ? (
                  <>
                    {filteredCustomers.map((customer, index) => {
                      // تحديد ما إذا كان العميل دائم ولديه عملاء فرعيين
                      const isRegularCustomer = customer.customer_type === 'regular';
                      const subCustomers = isRegularCustomer ? getSubCustomersForParent(customer.id) : [];
                      const hasSubCustomers = subCustomers.length > 0;
                      const isExpanded = expandedCustomers[customer.id];

                      return (
                        <React.Fragment key={customer.id}>
                          <tr className={`fade-in ${isRegularCustomer && hasSubCustomers ? 'has-sub-customers' : ''}`} style={{animationDelay: `${index * 0.05}s`}}>
                            <td>{index + 1}</td>
                            <td>
                              {isRegularCustomer && hasSubCustomers ? (
                                <div
                                  className="customer-name-with-toggle"
                                  onClick={() => toggleCustomerExpansion(customer.id)}
                                  style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                                >
                                  {isExpanded ? (
                                    <span className="toggle-icon" style={{ marginLeft: '5px' }}>▼</span>
                                  ) : (
                                    <span className="toggle-icon" style={{ marginLeft: '5px' }}>▶</span>
                                  )}
                                  <strong>{customer.name}</strong>
                                </div>
                              ) : (
                                <strong>{customer.name}</strong>
                              )}
                            </td>
                            <td>
                              {customer.customer_type === 'regular' && (
                                <span className="badge primary">
                                  <FaUserFriends className="icon-right" />
                                  عميل دائم
                                </span>
                              )}
                              {customer.customer_type === 'sub' && (
                                <span className="badge info">
                                  <FaLink className="icon-right" />
                                  عميل فرعي
                                </span>
                              )}
                              {customer.customer_type === 'normal' && (
                                <span className="badge secondary">
                                  <FaUserAlt className="icon-right" />
                                  عميل عادي
                                </span>
                              )}
                            </td>
                            <td>
                              {customer.customer_type === 'sub' && customer.parent_id ? (
                                <span className="parent-name">
                                  <FaUserFriends className="icon-right" style={{color: '#3498db'}} />
                                  {getParentCustomerName(customer.parent_id)}
                                </span>
                              ) : (
                                '-'
                              )}
                            </td>
                            <td>
                              {customer.phone ? (
                                <span className="phone-number">
                                  <FaPhone className="icon-right" style={{color: '#27ae60'}} />
                                  {customer.phone}
                                </span>
                              ) : (
                                '-'
                              )}
                            </td>
                            <td>
                              <span className={`balance ${customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : ''}`}>
                                <FaWallet className="icon-right" />
                                {customer.balance ? customer.balance.toFixed(2) : '0.00'}
                              </span>
                            </td>
                            <td>
                              <div className="actions">
                                <button
                                  className="btn-info"
                                  onClick={() => handleShowSalesHistory(customer.id)}
                                  title="سجل المبيعات"
                                >
                                  <FaHistory />
                                </button>
                                <button
                                  className="btn-success"
                                  onClick={() => handleSellToCustomer(customer)}
                                  title="بيع للعميل"
                                >
                                  <FaShoppingCart />
                                  <span className="action-text">بيع</span>
                                </button>
                              </div>
                            </td>
                          </tr>

                          {/* عرض العملاء الفرعيين إذا كان العميل دائم وتم توسيعه */}
                          {isRegularCustomer && hasSubCustomers && isExpanded && (
                            subCustomers.map((subCustomer, subIndex) => (
                              <tr key={subCustomer.id} className="sub-customer-row fade-in" style={{ backgroundColor: 'rgba(0, 123, 255, 0.05)', animationDelay: `${(index + subIndex + 1) * 0.05}s` }}>
                                <td>{index + 1}.{subIndex + 1}</td>
                                <td style={{ paddingRight: '25px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <span style={{ marginLeft: '5px', color: '#3498db' }}>└</span>
                                    <span>{subCustomer.name}</span>
                                  </div>
                                </td>
                                <td>
                                  <span className="badge info">
                                    <FaLink className="icon-right" />
                                    عميل فرعي
                                  </span>
                                </td>
                                <td>
                                  <span className="parent-name">
                                    <FaUserFriends className="icon-right" style={{color: '#3498db'}} />
                                    {customer.name}
                                  </span>
                                </td>
                                <td>
                                  {subCustomer.phone ? (
                                    <span className="phone-number">
                                      <FaPhone className="icon-right" style={{color: '#27ae60'}} />
                                      {subCustomer.phone}
                                    </span>
                                  ) : (
                                    '-'
                                  )}
                                </td>
                                <td>
                                  <span className={`balance ${subCustomer.balance > 0 ? 'positive' : subCustomer.balance < 0 ? 'negative' : ''}`}>
                                    <FaWallet className="icon-right" />
                                    {subCustomer.balance ? subCustomer.balance.toFixed(2) : '0.00'}
                                  </span>
                                </td>
                                <td>
                                  <div className="actions">
                                    <button
                                      className="btn-info"
                                      onClick={() => handleShowSalesHistory(subCustomer.id)}
                                      title="سجل المبيعات"
                                    >
                                      <FaHistory />
                                    </button>
                                    <button
                                      className="btn-success"
                                      onClick={() => handleSellToCustomer(subCustomer)}
                                      title="بيع للعميل"
                                    >
                                      <FaShoppingCart />
                                      <span className="action-text">بيع</span>
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))
                          )}
                        </React.Fragment>
                      );
                    })}
                  </>
                ) : (
                  <tr>
                    <td colSpan="7" className="empty-table">
                      {searchTerm ? (
                        <>
                          <FaSearch style={{fontSize: '2.5rem', color: 'var(--text-light)', marginBottom: '1rem', opacity: 0.7}} />
                          <div style={{fontSize: '1.1rem', marginBottom: '0.5rem', fontWeight: 600, color: 'var(--text-dark)'}}>لا توجد نتائج للبحث</div>
                          <div style={{color: 'var(--text-light)', marginBottom: '1rem'}}>لم يتم العثور على نتائج للبحث عن "{searchTerm}"</div>
                        </>
                      ) : (
                        <>
                          <FaUsers style={{fontSize: '2.5rem', color: 'var(--primary-color)', marginBottom: '1rem', opacity: 0.7}} />
                          <div style={{fontSize: '1.1rem', marginBottom: '0.5rem', fontWeight: 600, color: 'var(--text-dark)'}}>لا يوجد عملاء مسجلين</div>
                          <div style={{color: 'var(--text-light)', marginBottom: '1rem'}}>قم بإضافة عملاء جدد للبدء في عمليات البيع</div>
                          <button
                            className="btn-add hover-lift"
                            style={{margin: '1rem auto 0', display: 'flex'}}
                            onClick={() => window.location.href = '#/customers'}
                          >
                            <FaPlus className="icon-left" />
                            إضافة عميل جديد
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* نموذج البيع للعميل */}
      {showSalesModal && selectedCustomer && (
        <div className="popup-backdrop">
          <div className="popup-container popup-horizontal" style={{ maxWidth: '1000px' }}>
            <button className="popup-close" onClick={() => setShowSalesModal(false)}>
              <FaTimes />
            </button>

            <div className="popup-horizontal-content">
              {/* القسم الأيمن - معلومات الفاتورة */}
              <div className="popup-sidebar">
                <h3 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>
                  <FaShoppingCart style={{ marginLeft: '8px' }} />
                  بيع للعميل: {selectedCustomer.name}
                </h3>

                <div style={{ marginBottom: '25px' }}>
                  <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>رقم الفاتورة</label>
                  <input
                    type="text"
                    name="invoice_number"
                    className="popup-form-control"
                    value={invoiceData.invoice_number}
                    onChange={handleInvoiceInputChange}
                    placeholder="أدخل رقم الفاتورة"
                    readOnly
                  />
                  <small style={{ color: 'var(--text-light)', fontSize: '12px', marginTop: '5px', display: 'block' }}>
                    يتم توليد رقم الفاتورة تلقائياً
                  </small>
                </div>

                <div style={{ marginBottom: '25px' }}>
                  <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>التاريخ</label>
                  <input
                    type="date"
                    name="transaction_date"
                    className="popup-form-control"
                    value={invoiceData.transaction_date}
                    onChange={handleInvoiceInputChange}
                  />
                </div>



                <div style={{ marginBottom: '25px' }}>
                  <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>ملاحظات</label>
                  <textarea
                    name="notes"
                    className="popup-form-control"
                    value={invoiceData.notes}
                    onChange={handleInvoiceInputChange}
                    rows="3"
                    placeholder="أدخل أي ملاحظات إضافية هنا"
                  ></textarea>
                </div>

                <div style={{ marginTop: '30px' }}>
                  <button
                    className="popup-btn popup-btn-primary"
                    style={{ width: '100%', marginBottom: '10px' }}
                    onClick={() => handleShowSalesHistory(selectedCustomer.id)}
                    title="عرض سجل المبيعات"
                  >
                    <FaHistory style={{ marginLeft: '5px' }} />
                    سجل المبيعات
                  </button>

                  <button
                    type="button"
                    style={{
                      width: '100%',
                      marginBottom: '10px',
                      backgroundColor: '#3498db',
                      color: 'white',
                      padding: '10px 20px',
                      borderRadius: '4px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      border: 'none',
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '15px'
                    }}
                    onClick={handleCreateSubInvoice}
                    disabled={isCreatingSubInvoice || saleItems.length === 0}
                  >
                    <FaFileInvoice style={{ marginLeft: '5px' }} />
                    إنشاء فاتورة فرعية
                  </button>
                </div>
              </div>

              {/* القسم الأيسر - إضافة الأصناف والجدول */}
              <div className="popup-main-content">
                <form ref={formRef} onSubmit={handleSubmitSale} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                  {/* قسم إضافة الصنف */}
                  <div className="item-add-section" style={{ marginBottom: '20px' }}>
                    <h4>
                      <FaPlus style={{ marginLeft: '8px' }} />
                      إضافة صنف
                    </h4>
                    <div style={{ marginBottom: '15px' }}>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginBottom: '15px' }}>
                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: '600' }}>البحث عن صنف</label>
                        <ItemAutocomplete
                          onSelect={handleItemAutocompleteSelect}
                          placeholder="اكتب اسم الصنف أو الرقم للبحث..."
                          className="sales-autocomplete"
                        />
                        <div style={{ display: 'flex', justifyContent: 'center', marginTop: '5px' }}>
                          <span style={{ fontSize: '12px', color: '#777' }}>أو</span>
                        </div>
                        <button
                          type="button"
                          className="popup-btn popup-btn-secondary"
                          onClick={handleOpenItemSearch}
                          style={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}
                        >
                          <FaSearch style={{ marginLeft: '8px' }} />
                          فتح نافذة البحث المتقدم
                        </button>
                      </div>
                    </div>
                    <div style={{ display: 'flex', gap: '15px', marginBottom: '15px' }}>
                      <div style={{ flex: 2 }}>
                        <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>الصنف المحدد</label>
                        <div style={{ display: 'flex', gap: '10px' }}>
                          <input
                            type="text"
                            className="popup-form-control"
                            value={currentItem.item_id ? currentItem.item_name || items.find(item => item.id === currentItem.item_id)?.name || '' : ''}
                            readOnly
                            placeholder="لم يتم اختيار صنف بعد..."
                            style={{ flex: 1 }}
                          />
                        </div>
                      </div>
                      <div style={{ flex: 1 }}>
                        <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>الكمية</label>
                        <input
                          type="number"
                          name="quantity"
                          className="popup-form-control"
                          value={currentItem.quantity}
                          onChange={handleItemInputChange}
                          min="1"
                        />
                      </div>
                      <div style={{ flex: 1 }}>
                        <label style={{ display: 'block', marginBottom: '10px', fontWeight: '600' }}>السعر (د ل)</label>
                        <input
                          type="number"
                          name="price"
                          className="popup-form-control"
                          value={currentItem.price}
                          onChange={handleItemInputChange}
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                    <div style={{ textAlign: 'left' }}>
                      <button
                        type="button"
                        className="popup-btn popup-btn-primary"
                        onClick={handleAddItemToSale}
                      >
                        <FaPlus style={{ marginLeft: '5px' }} />
                        إضافة الصنف
                      </button>
                    </div>
                  </div>

                  {/* جدول الأصناف */}
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <table className="popup-table">
                      <thead>
                        <tr>
                          <th style={{ width: '50px' }}>#</th>
                          <th>الصنف</th>
                          <th style={{ width: '100px' }}>الكمية</th>
                          <th style={{ width: '120px' }}>السعر (د ل)</th>
                          <th style={{ width: '120px' }}>الإجمالي (د ل)</th>
                          <th style={{ width: '80px' }}>الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {saleItems.length > 0 ? (
                          saleItems.map((item, index) => (
                            <tr key={index}>
                              <td>{index + 1}</td>
                              <td>{item.item_name}</td>
                              <td>{item.quantity}</td>
                              <td>{parseFloat(item.price).toFixed(2)}</td>
                              <td>{(item.quantity * item.price).toFixed(2)}</td>
                              <td>
                                <button
                                  type="button"
                                  className="popup-btn popup-btn-danger"
                                  style={{ padding: '8px', height: 'auto', minWidth: 'auto' }}
                                  onClick={() => handleRemoveItemFromSale(index)}
                                >
                                  <FaTrash />
                                </button>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan="6" style={{ textAlign: 'center', padding: '30px', color: '#777' }}>
                              لا توجد أصناف مضافة
                            </td>
                          </tr>
                        )}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan="4" style={{ textAlign: 'left', fontWeight: 'bold', padding: '15px 20px' }}>
                            الإجمالي:
                          </td>
                          <td colSpan="2" style={{ fontWeight: 'bold', color: 'var(--primary-color, #1e3243)', fontSize: '16px', padding: '15px 20px' }}>
                            {calculateTotal().toFixed(2)} د ل
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>

                  {/* أزرار التحكم */}
                  <div style={{
                    marginTop: '20px',
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: '10px',
                    borderTop: '1px solid var(--border-color, #eee)',
                    paddingTop: '20px'
                  }}>
                    <button type="button" className="popup-btn popup-btn-secondary" onClick={() => setShowSalesModal(false)}>
                      إلغاء
                    </button>
                    <button
                      type="submit"
                      className="popup-btn popup-btn-primary"
                      disabled={isCreatingSubInvoice}
                    >
                      <FaReceipt style={{ marginLeft: '8px', fontSize: '16px' }} />
                      إتمام عملية البيع
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة عرض سجل عمليات العميل */}
      {showSalesHistory && (
        <EnhancedCustomerSalesHistory
          customerId={selectedCustomerId}
          onClose={() => setShowSalesHistory(false)}
        />
      )}

      {/* نافذة البحث عن الأصناف */}
      {showItemSearchPopup && (
        <EnhancedItemSearch
          onSelect={handleItemSelect}
          onClose={() => setShowItemSearchPopup(false)}
        />
      )}
    </div>
  );
};

export default CustomerSales;
