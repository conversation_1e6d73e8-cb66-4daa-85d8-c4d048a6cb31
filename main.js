/**
 * نقطة دخول التطبيق الرئيسية
 * تقوم بتهيئة التطبيق وإدارة دورة حياته
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const bcrypt = require('bcryptjs');

// استيراد الوحدات المساعدة
const logger = require('./src/utils/logger');
const dbOptimizer = require('./src/utils/db-optimizer');
const memoryManager = require('./src/utils/memory-manager');
const { logError, logSystem } = require('./error-handler');

// استيراد مدير قاعدة البيانات
const DatabaseManager = require('./database-singleton');

// استيراد إصلاح جدول المعاملات
const { fixTransactionsTable } = require('./fix-transactions-table');

// استيراد إصلاح نظام الاسترجاع
const { fixRetrievalSystem } = require('./fix-retrieval-system');

// استيراد إصلاح حذف العملاء
const customersManagerFix = require('./customers-manager-fix');
const customersManagerPatch = require('./customers-manager-patch');

// استيراد وحدات إدارة النظام
const itemsManager = require('./items-manager');
const inventoryManager = require('./inventory-manager');
const customersManager = require('./customers-manager');
const transactionManager = require('./unified-transaction-manager');
const returnTransactionsManager = require('./return-transactions-manager');
const cashboxManager = require('./cashbox-manager');
const reportsManager = require('./reports-manager');
const UpdateManager = require('./src/services/UpdateManager');

// استيراد معالجات IPC
const ipcHandlers = require('./ipc-handlers');
const missingHandlers = require('./missing-handlers');

// تعريف المتغيرات العامة
let mainWindow = null;
let db = null;
let isAppReady = false;
let isDbInitialized = false;

/**
 * تهيئة قاعدة البيانات ووحدات النظام
 * @returns {Promise<boolean>} - نجاح العملية
 */
async function initializeDatabase() {
  try {
    console.log('جاري تهيئة قاعدة البيانات ووحدات النظام...');

    // تحديد مسار قاعدة البيانات
    const userDataPath = app.getPath('userData');
    const dbDir = path.join(userDataPath, 'wms-database');
    const dbPath = path.join(dbDir, 'warehouse.db');

    console.log(`مسار قاعدة البيانات: ${dbPath}`);

    // تهيئة مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = await dbManager.initialize(dbPath);

    if (!db) {
      console.error('فشل في تهيئة قاعدة البيانات');
      return false;
    }

    console.log('تم تهيئة قاعدة البيانات بنجاح');

    // إصلاح جدول المعاملات (إضافة عمود selling_price)
    try {
      console.log('جاري إصلاح جدول المعاملات...');
      const fixResult = fixTransactionsTable();
      console.log('نتيجة إصلاح جدول المعاملات:', fixResult);
    } catch (fixError) {
      console.error('خطأ في إصلاح جدول المعاملات:', fixError);
      logError(fixError, 'initializeDatabase - fixTransactionsTable');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إصلاح نظام الاسترجاع
    try {
      console.log('جاري إصلاح نظام الاسترجاع...');
      const fixRetrievalResult = fixRetrievalSystem();
      console.log('نتيجة إصلاح نظام الاسترجاع:', fixRetrievalResult);
    } catch (fixRetrievalError) {
      console.error('خطأ في إصلاح نظام الاسترجاع:', fixRetrievalError);
      logError(fixRetrievalError, 'initializeDatabase - fixRetrievalSystem');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إصلاح حذف العملاء
    try {
      console.log('جاري إصلاح حذف العملاء...');
      const customersFixResult = customersManagerFix.initialize();
      console.log('نتيجة إصلاح حذف العملاء:', customersFixResult);

      // تطبيق التصحيح على ملف customers-manager.js
      console.log('جاري تطبيق التصحيح على ملف customers-manager.js...');
      const patchResult = customersManagerPatch.applyPatch();
      console.log('نتيجة تطبيق التصحيح على ملف customers-manager.js:', patchResult);
    } catch (customersFixError) {
      console.error('خطأ في إصلاح حذف العملاء:', customersFixError);
      logError(customersFixError, 'initializeDatabase - customersManagerFix');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إنشاء مجلدات التحديثات والنسخ الاحتياطية
    try {
      const updatePaths = {
        appUpdatesDir: path.join(app.getPath('userData'), 'updates', 'app'),
        dbUpdatesDir: path.join(app.getPath('userData'), 'updates', 'database'),
        backupsDir: path.join(app.getPath('userData'), 'backups'),
        tempDir: path.join(app.getPath('temp'), 'wms-updates')
      };

      // إنشاء المجلدات إذا لم تكن موجودة
      for (const dir of Object.values(updatePaths)) {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`تم إنشاء مجلد: ${dir}`);
        }
      }

      console.log('تم التحقق من وجود مجلدات التحديثات والنسخ الاحتياطية');
    } catch (error) {
      console.error('خطأ في إنشاء مجلدات التحديثات والنسخ الاحتياطية:', error);
      // لا نريد إيقاف التهيئة إذا فشل إنشاء المجلدات
    }

    // تهيئة وحدات النظام
    try {
      // تهيئة وحدة إدارة الأصناف
      console.log('جاري تهيئة وحدة إدارة الأصناف...');
      const itemsInitResult = itemsManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة الأصناف:', itemsInitResult);

      // تهيئة وحدة إدارة المخزون
      console.log('جاري تهيئة وحدة إدارة المخزون...');
      const inventoryInitResult = inventoryManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة المخزون:', inventoryInitResult);

      // تهيئة وحدة إدارة العملاء
      console.log('جاري تهيئة وحدة إدارة العملاء...');
      const customersInitResult = customersManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة العملاء:', customersInitResult);

      // تهيئة وحدة إدارة المعاملات
      console.log('جاري تهيئة وحدة إدارة المعاملات...');
      const transactionInitResult = transactionManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة المعاملات:', transactionInitResult);

      // تهيئة وحدة إدارة عمليات الإرجاع
      console.log('جاري تهيئة وحدة إدارة عمليات الإرجاع...');
      const returnTransactionsInitResult = returnTransactionsManager.initialize(db);
      console.log('نتيجة تهيئة وحدة إدارة عمليات الإرجاع:', returnTransactionsInitResult);

      // تهيئة وحدة إدارة الخزينة
      console.log('جاري تهيئة وحدة إدارة الخزينة...');
      const cashboxInitResult = cashboxManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة الخزينة:', cashboxInitResult);

      // تهيئة وحدة إدارة التقارير
      console.log('جاري تهيئة وحدة إدارة التقارير...');
      const reportsInitResult = reportsManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة التقارير:', reportsInitResult);

      console.log('تم تهيئة وحدات النظام بنجاح');
      isDbInitialized = true;
      return true;
    } catch (error) {
      console.error('خطأ في تهيئة وحدات النظام:', error);
      logError(error, 'initializeDatabase - modules');
      return false;
    }
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات ووحدات النظام:', error);
    logError(error, 'initializeDatabase');
    return false;
  }
}

/**
 * إنشاء النافذة الرئيسية للتطبيق
 */
function createWindow() {
  console.log('جاري إنشاء النافذة الرئيسية...');

  // إنشاء النافذة الرئيسية
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      // إضافة خيارات إضافية لتصحيح المشكلة
      worldSafeExecuteJavaScript: true,
      enableRemoteModule: false,
      sandbox: false
    }
  });

  // إضافة النافذة الرئيسية إلى المتغيرات العالمية
  global.mainWindow = mainWindow;

  // تسجيل مسار ملف preload.js للتأكد من تحميله بشكل صحيح
  console.log('مسار ملف preload.js:', path.join(__dirname, 'preload.js'));

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // إظهار النافذة عند اكتمال تحميل الصفحة
  mainWindow.once('ready-to-show', () => {
    console.log('النافذة الرئيسية جاهزة للعرض');
    mainWindow.show();
    mainWindow.maximize();
  });

  // معالجة إغلاق النافذة
  mainWindow.on('closed', () => {
    console.log('تم إغلاق النافذة الرئيسية');
    mainWindow = null;
  });

  console.log('تم إنشاء النافذة الرئيسية بنجاح');
}

/**
 * تهيئة التطبيق
 */
app.on('ready', async () => {
  console.log('التطبيق جاهز للتشغيل');
  isAppReady = true;

  // تعريف متغير عالمي لتتبع حالة المزامنة
  global.isSyncInProgress = false;

  // تهيئة قاعدة البيانات ووحدات النظام
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('فشل في تهيئة قاعدة البيانات ووحدات النظام');
    app.quit();
    return;
  }

  // تسجيل معالجات IPC
  console.log('[info] جاري تسجيل معالجات IPC...');

  // التحقق من عدم تسجيل المعالجات من قبل
  if (!global.ipcHandlersRegistered) {
    try {
      // تسجيل المعالجات الأساسية
      const ipcHandlersRegistered = ipcHandlers.registerAllHandlers();
      console.log('[info] نتيجة تسجيل معالجات IPC الأساسية:', ipcHandlersRegistered);

      // تعيين علامة لتجنب تسجيل المعالجات مرة أخرى
      global.ipcHandlersRegistered = true;
    } catch (error) {
      console.error('[error] فشل في تسجيل معالجات IPC:', error.message);
    }
  } else {
    console.log('[info] تم تسجيل معالجات IPC مسبقًا، تم تجاهل التسجيل المكرر');
  }

  // إنشاء النافذة الرئيسية
  createWindow();
});

/**
 * معالجة إغلاق التطبيق
 */
app.on('window-all-closed', () => {
  console.log('تم إغلاق جميع النوافذ');
  if (process.platform !== 'darwin') {
    console.log('جاري إغلاق التطبيق...');
    app.quit();
  }
});

/**
 * معالجة إعادة تنشيط التطبيق
 */
app.on('activate', () => {
  console.log('تم تنشيط التطبيق');
  if (mainWindow === null && isAppReady) {
    createWindow();
  }
});

/**
 * معالجة إغلاق التطبيق
 */
app.on('before-quit', () => {
  console.log('جاري إغلاق التطبيق...');
  if (db) {
    console.log('جاري إغلاق اتصال قاعدة البيانات...');
    const dbManager = DatabaseManager.getInstance();
    dbManager.close();
  }
});

// ملاحظة: تم نقل جميع معالجات IPC إلى ملفات ipc-handlers.js و missing-handlers.js
// لتجنب تكرار تسجيل المعالجات وتحسين تنظيم الكود

// تسجيل معالج لتحديث الأصناف في جميع النوافذ
ipcMain.on('refresh-items-all-windows', (event) => {
  try {
    console.log('تم استلام طلب تحديث الأصناف في جميع النوافذ');

    // إرسال حدث تحديث الأصناف إلى جميع النوافذ
    BrowserWindow.getAllWindows().forEach(window => {
      if (window && !window.isDestroyed()) {
        console.log(`إرسال حدث تحديث الأصناف إلى النافذة ${window.id}`);
        window.webContents.send('item-added', {
          id: 'refresh-all',
          name: 'تحديث الأصناف',
          timestamp: new Date().toISOString()
        });
      }
    });

    console.log('تم إرسال حدث تحديث الأصناف إلى جميع النوافذ بنجاح');
  } catch (error) {
    console.error('خطأ في معالج refresh-items-all-windows:', error);
    logError(error, 'refresh-items-all-windows');
  }
});

// تم تعطيل تسجيل معالج get-all-inventory مباشرة هنا لتجنب التكرار
// يتم تسجيله في ملف ipc-handlers.js

// تم تعطيل تسجيل معالج get-customer-available-items-for-return مباشرة هنا لتجنب التكرار
// يتم تسجيله في ملف missing-handlers.js
