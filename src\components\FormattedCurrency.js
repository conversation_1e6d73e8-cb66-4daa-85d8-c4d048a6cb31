import React from 'react';
import { formatCurrency } from '../utils/formatCurrency';

/**
 * مكون لعرض المبالغ المالية بتنسيق العملة
 * @param {Object} props - خصائص المكون
 * @param {number|string} props.amount - المبلغ المراد عرضه
 * @param {boolean} props.showSymbol - ما إذا كان سيتم عرض رمز العملة
 * @param {number} props.decimalPlaces - عدد المنازل العشرية
 * @param {string} props.currencySymbol - رمز العملة
 * @param {boolean} props.useThousandSeparator - استخدام فاصل الآلاف
 * @param {string} props.className - فئة CSS إضافية
 * @param {boolean} props.isProfit - ما إذا كان المبلغ يمثل ربحًا (لإضافة تنسيق خاص)
 * @returns {JSX.Element} - مكون React
 */
const FormattedCurrency = ({
  amount,
  showSymbol = true,
  decimalPlaces = 2,
  currencySymbol = 'د.ل',
  useThousandSeparator = true, // تغيير القيمة الافتراضية إلى true
  className = '',
  isProfit = false,
  ...props
}) => {
  // دائماً نستخدم 0 منازل عشرية لجميع المبالغ
  const actualDecimalPlaces = 0;

  // تكوين خيارات التنسيق
  const formatOptions = {
    showSymbol,
    decimalPlaces: actualDecimalPlaces,
    currencySymbol,
    useThousandSeparator
  };

  // تنسيق المبلغ
  const formattedValue = formatCurrency(amount, formatOptions);

  // تحديد الفئة بناءً على ما إذا كان المبلغ يمثل ربحًا
  const combinedClassName = `formatted-currency ${isProfit ? 'profit-amount' : ''} ${className}`;

  // تحويل الأرقام العربية إلى أرقام إنجليزية للعرض
  const convertToEnglishDigits = (str) => {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str.replace(/[٠-٩]/g, match => {
      return englishDigits[arabicDigits.indexOf(match)];
    });
  };

  // تحويل الأرقام إلى أرقام إنجليزية
  const displayValue = convertToEnglishDigits(formattedValue);

  return (
    <span className={combinedClassName} {...props}>
      {displayValue}
    </span>
  );
};

export default FormattedCurrency;
