/**
 * ملف اختبار إصلاح الأرباح عبر معالج IPC
 */

const { ipcRenderer } = require('electron');

async function testProfitFix() {
  try {
    console.log('=== اختبار إصلاح الأرباح عبر IPC ===\n');

    // 1. الحصول على بيانات الخزينة الحالية
    console.log('1. الحصول على بيانات الخزينة الحالية...');
    const currentCashbox = await ipcRenderer.invoke('get-cashbox');
    
    if (currentCashbox.exists) {
      console.log('بيانات الخزينة الحالية:');
      console.log(`  الرصيد الافتتاحي: ${currentCashbox.initial_balance}`);
      console.log(`  الرصيد الحالي: ${currentCashbox.current_balance}`);
      console.log(`  إجمالي الأرباح: ${currentCashbox.profit_total}`);
      console.log(`  إجمالي المبيعات: ${currentCashbox.sales_total}`);
      console.log(`  إجمالي المشتريات: ${currentCashbox.purchases_total}`);
    } else {
      console.log('لا توجد بيانات خزينة');
      return;
    }

    // 2. الحصول على عينة من معاملات البيع
    console.log('\n2. فحص معاملات البيع...');
    try {
      const transactions = await ipcRenderer.invoke('get-transactions-with-filters', {});
      const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');
      
      console.log(`عدد معاملات البيع: ${salesTransactions.length}`);
      
      if (salesTransactions.length > 0) {
        console.log('عينة من معاملات البيع:');
        salesTransactions.slice(0, 3).forEach((transaction, index) => {
          console.log(`  معاملة ${index + 1}:`);
          console.log(`    الكمية: ${transaction.quantity}`);
          console.log(`    سعر البيع: ${transaction.selling_price}`);
          console.log(`    سعر التكلفة: ${transaction.price}`);
          console.log(`    المبلغ الإجمالي: ${transaction.total_price}`);
          console.log(`    الربح المسجل: ${transaction.profit || 0}`);
          
          // حساب الربح المتوقع
          const expectedProfit = Math.max(0, (transaction.selling_price - transaction.price) * transaction.quantity);
          console.log(`    الربح المتوقع: ${expectedProfit}`);
          
          if (Math.abs((transaction.profit || 0) - expectedProfit) > 0.01) {
            console.log(`    ⚠️  هناك اختلاف في حساب الربح!`);
          } else {
            console.log(`    ✅ الربح محسوب بشكل صحيح`);
          }
        });
      }
    } catch (error) {
      console.log('خطأ في الحصول على المعاملات:', error.message);
    }

    // 3. تشغيل إصلاح الأرباح
    console.log('\n3. تشغيل إصلاح الأرباح...');
    try {
      const fixResult = await ipcRenderer.invoke('update-profit-values');
      
      if (fixResult.success) {
        console.log('✅ تم إصلاح الأرباح بنجاح!');
        console.log(`عدد المعاملات المحدثة: ${fixResult.updatedCount}`);
        console.log(`إجمالي الأرباح الجديد: ${fixResult.totalProfit}`);
        
        if (fixResult.cashboxUpdated) {
          console.log('✅ تم تحديث بيانات الخزينة');
        }
      } else {
        console.log('❌ فشل في إصلاح الأرباح:', fixResult.error);
      }
    } catch (error) {
      console.log('❌ خطأ في تشغيل إصلاح الأرباح:', error.message);
    }

    // 4. التحقق من النتائج بعد الإصلاح
    console.log('\n4. التحقق من النتائج بعد الإصلاح...');
    try {
      const updatedCashbox = await ipcRenderer.invoke('get-cashbox');
      
      if (updatedCashbox.exists) {
        console.log('بيانات الخزينة بعد الإصلاح:');
        console.log(`  الرصيد الافتتاحي: ${updatedCashbox.initial_balance}`);
        console.log(`  الرصيد الحالي: ${updatedCashbox.current_balance}`);
        console.log(`  إجمالي الأرباح: ${updatedCashbox.profit_total}`);
        console.log(`  إجمالي المبيعات: ${updatedCashbox.sales_total}`);
        console.log(`  إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
        
        // مقارنة النتائج
        const profitDifference = updatedCashbox.profit_total - currentCashbox.profit_total;
        if (profitDifference !== 0) {
          console.log(`\n📈 تغيير في الأرباح: ${profitDifference > 0 ? '+' : ''}${profitDifference}`);
        } else {
          console.log('\n📊 لم يتغير إجمالي الأرباح');
        }
      }
    } catch (error) {
      console.log('خطأ في الحصول على بيانات الخزينة المحدثة:', error.message);
    }

    console.log('\n=== انتهاء اختبار إصلاح الأرباح ===');

  } catch (error) {
    console.error('خطأ عام في الاختبار:', error);
  }
}

// تشغيل الاختبار
testProfitFix();
