import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// تسجيل المكونات المطلوبة للرسم البياني
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

/**
 * مكون الرسم البياني للمبيعات الشهرية
 * @param {Object} props - خصائص المكون
 * @param {Object} props.data - بيانات الرسم البياني
 * @param {Array} props.data.labels - تسميات الأشهر
 * @param {Array} props.data.sales - قيم المبيعات
 * @param {Array} props.data.purchases - قيم المشتريات
 * @returns {JSX.Element} - مكون الرسم البياني
 */
const MonthlySalesChart = ({ data }) => {
  // التأكد من وجود البيانات
  if (!data || !data.labels || !data.sales || !data.purchases) {
    return (
      <div className="chart-placeholder">
        <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
      </div>
    );
  }

  // إعداد خيارات الرسم البياني
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: 'Cairo, sans-serif',
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'تطور المبيعات والمشتريات الشهرية',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('ar-EG', { 
                style: 'currency', 
                currency: 'EGP',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              }).format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            family: 'Cairo, sans-serif',
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            family: 'Cairo, sans-serif',
            size: 11
          },
          callback: function(value) {
            return new Intl.NumberFormat('ar-EG', { 
              style: 'currency', 
              currency: 'EGP',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value);
          }
        }
      }
    }
  };

  // إعداد بيانات الرسم البياني
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: 'المبيعات',
        data: data.sales,
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        borderColor: 'rgb(53, 162, 235)',
        borderWidth: 1
      },
      {
        label: 'المشتريات',
        data: data.purchases,
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        borderColor: 'rgb(255, 99, 132)',
        borderWidth: 1
      }
    ]
  };

  return (
    <div className="chart-container" style={{ height: '300px', width: '100%' }}>
      <Bar options={options} data={chartData} />
    </div>
  );
};

export default MonthlySalesChart;
