<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الأرباح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .button.success {
            background-color: #27ae60;
        }
        .button.danger {
            background-color: #e74c3c;
        }
        .log {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار إصلاح الأرباح</h1>
            <p>أداة لاختبار وإصلاح حساب الأرباح في النظام</p>
        </div>

        <div class="section">
            <h3>📊 بيانات الخزينة الحالية</h3>
            <button class="button" onclick="loadCashboxData()">تحميل بيانات الخزينة</button>
            <div id="cashboxData"></div>
        </div>

        <div class="section">
            <h3>🔍 فحص معاملات البيع</h3>
            <button class="button" onclick="checkSalesTransactions()">فحص معاملات البيع</button>
            <div id="salesData"></div>
        </div>

        <div class="section">
            <h3>🛠️ إصلاح الأرباح</h3>
            <button class="button success" onclick="fixProfits()">إصلاح الأرباح</button>
            <button class="button" onclick="checkAfterFix()">التحقق بعد الإصلاح</button>
            <div id="fixResults"></div>
        </div>

        <div class="section">
            <h3>📝 سجل العمليات</h3>
            <button class="button" onclick="clearLog()">مسح السجل</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let currentCashboxData = null;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function loadCashboxData() {
            try {
                log('🔄 تحميل بيانات الخزينة...');
                const cashbox = await window.api.getCashbox();
                
                if (cashbox.exists) {
                    currentCashboxData = cashbox;
                    const html = `
                        <div class="status success">
                            <strong>بيانات الخزينة:</strong><br>
                            الرصيد الافتتاحي: ${cashbox.initial_balance}<br>
                            الرصيد الحالي: ${cashbox.current_balance}<br>
                            إجمالي الأرباح: ${cashbox.profit_total}<br>
                            إجمالي المبيعات: ${cashbox.sales_total}<br>
                            إجمالي المشتريات: ${cashbox.purchases_total}
                        </div>
                    `;
                    document.getElementById('cashboxData').innerHTML = html;
                    log(`✅ تم تحميل بيانات الخزينة - الأرباح الحالية: ${cashbox.profit_total}`);
                } else {
                    showStatus('cashboxData', 'لا توجد بيانات خزينة', 'error');
                    log('❌ لا توجد بيانات خزينة');
                }
            } catch (error) {
                showStatus('cashboxData', `خطأ: ${error.message}`, 'error');
                log(`❌ خطأ في تحميل بيانات الخزينة: ${error.message}`);
            }
        }

        async function checkSalesTransactions() {
            try {
                log('🔍 فحص معاملات البيع...');
                const transactions = await window.api.getTransactionsWithFilters({});
                const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');
                
                log(`📊 عدد معاملات البيع: ${salesTransactions.length}`);
                
                if (salesTransactions.length > 0) {
                    let html = `<div class="status info"><strong>معاملات البيع (أول 5):</strong><br>`;
                    let totalExpectedProfit = 0;
                    let totalRecordedProfit = 0;
                    
                    salesTransactions.slice(0, 5).forEach((transaction, index) => {
                        const expectedProfit = Math.max(0, (transaction.selling_price - transaction.price) * transaction.quantity);
                        const recordedProfit = transaction.profit || 0;
                        
                        totalExpectedProfit += expectedProfit;
                        totalRecordedProfit += recordedProfit;
                        
                        const isCorrect = Math.abs(recordedProfit - expectedProfit) < 0.01;
                        const status = isCorrect ? '✅' : '⚠️';
                        
                        html += `${status} معاملة ${index + 1}: الربح المسجل ${recordedProfit} | المتوقع ${expectedProfit}<br>`;
                        log(`${status} معاملة ${transaction.id}: ربح مسجل ${recordedProfit}, متوقع ${expectedProfit}`);
                    });
                    
                    html += `<br><strong>الإجماليات:</strong><br>`;
                    html += `الربح المسجل: ${totalRecordedProfit}<br>`;
                    html += `الربح المتوقع: ${totalExpectedProfit}<br>`;
                    html += `الفرق: ${totalExpectedProfit - totalRecordedProfit}</div>`;
                    
                    document.getElementById('salesData').innerHTML = html;
                    log(`📈 إجمالي الربح المتوقع: ${totalExpectedProfit}, المسجل: ${totalRecordedProfit}`);
                } else {
                    showStatus('salesData', 'لا توجد معاملات بيع', 'info');
                    log('📭 لا توجد معاملات بيع');
                }
            } catch (error) {
                showStatus('salesData', `خطأ: ${error.message}`, 'error');
                log(`❌ خطأ في فحص معاملات البيع: ${error.message}`);
            }
        }

        async function fixProfits() {
            try {
                log('🛠️ بدء إصلاح الأرباح...');
                showStatus('fixResults', 'جاري إصلاح الأرباح...', 'info');
                
                const result = await window.api.updateProfitValues();
                
                if (result.success) {
                    const html = `
                        <div class="status success">
                            <strong>✅ تم إصلاح الأرباح بنجاح!</strong><br>
                            عدد المعاملات المحدثة: ${result.updatedCount}<br>
                            إجمالي الأرباح الجديد: ${result.totalProfit}<br>
                            ${result.cashboxUpdated ? '✅ تم تحديث بيانات الخزينة' : ''}
                        </div>
                    `;
                    document.getElementById('fixResults').innerHTML = html;
                    log(`✅ تم إصلاح ${result.updatedCount} معاملة - الأرباح الجديدة: ${result.totalProfit}`);
                } else {
                    showStatus('fixResults', `❌ فشل الإصلاح: ${result.error}`, 'error');
                    log(`❌ فشل في إصلاح الأرباح: ${result.error}`);
                }
            } catch (error) {
                showStatus('fixResults', `❌ خطأ: ${error.message}`, 'error');
                log(`❌ خطأ في إصلاح الأرباح: ${error.message}`);
            }
        }

        async function checkAfterFix() {
            try {
                log('🔍 التحقق من النتائج بعد الإصلاح...');
                const updatedCashbox = await window.api.getCashbox();
                
                if (updatedCashbox.exists && currentCashboxData) {
                    const profitDifference = updatedCashbox.profit_total - currentCashboxData.profit_total;
                    
                    const html = `
                        <div class="status ${profitDifference !== 0 ? 'success' : 'info'}">
                            <strong>النتائج بعد الإصلاح:</strong><br>
                            الأرباح قبل الإصلاح: ${currentCashboxData.profit_total}<br>
                            الأرباح بعد الإصلاح: ${updatedCashbox.profit_total}<br>
                            التغيير: ${profitDifference > 0 ? '+' : ''}${profitDifference}
                        </div>
                    `;
                    document.getElementById('fixResults').innerHTML += html;
                    log(`📊 مقارنة النتائج: قبل ${currentCashboxData.profit_total} | بعد ${updatedCashbox.profit_total} | التغيير ${profitDifference}`);
                    
                    // تحديث البيانات الحالية
                    currentCashboxData = updatedCashbox;
                } else {
                    log('❌ لا يمكن مقارنة النتائج - تأكد من تحميل بيانات الخزينة أولاً');
                }
            } catch (error) {
                log(`❌ خطأ في التحقق من النتائج: ${error.message}`);
            }
        }

        // تحميل البيانات عند فتح الصفحة
        window.addEventListener('load', () => {
            log('🚀 تم تحميل أداة اختبار إصلاح الأرباح');
            loadCashboxData();
        });
    </script>
</body>
</html>
