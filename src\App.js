import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';

// Components
import TopNavbar from './components/TopNavbar';
import ErrorBoundary from './components/ErrorBoundary';
// تم إزالة استيراد مكون الإشعارات
// import Notifications from './components/Notifications';

// Context
import { AppProvider } from './context/AppContext';

// Pages
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Items from './pages/Items';
import Inventory from './pages/Inventory';
import Purchases from './pages/Purchases';
import Customers from './pages/Customers';
import Reports from './pages/Reports';
import Users from './pages/Users';

import Backup from './pages/Backup';
import Settings from './pages/Settings';
import Zakat from './pages/Zakat';
import Cashbox from './pages/Cashbox';
import ProfitTestPage from './pages/ProfitTestPage';
import './App.css';
import './modern-theme.css';
import './responsive.css';
import './print-styles.css';
import './components/charts/charts.css';

const App = () => {
  // سنضيف حالة لتتبع ما إذا كان المستخدم مسجل الدخول
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  // التحقق من حالة تسجيل الدخول عند تحميل التطبيق
  useEffect(() => {
    const storedUser = localStorage.getItem('wms_user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setIsLoggedIn(true);
        setUser(userData);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('wms_user');
        localStorage.removeItem('currentUserId');
        localStorage.removeItem('currentUserRole');
        localStorage.removeItem('currentUserName');
      }
    }
  }, []);

  // تعريف مكون خاص للمسارات المحمية
  const ProtectedRoute = ({ children }) => {
    if (!isLoggedIn) {
      return <Navigate to="/login" />;
    }
    return children;
  };

  return (
    <div className="app-container">
      <AppProvider>
        <ErrorBoundary>
          {/* تم إزالة مكون الإشعارات */}
          <Routes>
            <Route path="/login" element={
              isLoggedIn ?
              <Navigate to="/" /> :
              <Login onLogin={(userData) => {
                setIsLoggedIn(true);
                setUser(userData);
              }} />
            } />

            <Route path="/*" element={
              <ProtectedRoute>
                <>
                  <TopNavbar user={user} onLogout={() => {
                    // إزالة بيانات المستخدم من التخزين المحلي
                    localStorage.removeItem('wms_user');
                    localStorage.removeItem('currentUserId');
                    localStorage.removeItem('currentUserRole');
                    localStorage.removeItem('currentUserName');

                    // إعادة تعيين حالة التطبيق
                    setIsLoggedIn(false);
                    setUser(null);

                    // تأخير قصير قبل التوجيه لضمان تنظيف الحالة
                    setTimeout(() => {
                      // التوجيه إلى صفحة تسجيل الدخول
                      navigate('/login', { replace: true });
                    }, 50);
                  }} />
                  <div className="main-content-full">
                    <ErrorBoundary>
                      <Routes>
                        <Route path="/" element={<Dashboard />} />
                        <Route path="/items" element={<Items />} />
                        <Route path="/inventory" element={<Inventory />} />
                        <Route path="/purchases" element={<Purchases />} />
                        <Route path="/customers" element={<Customers />} />
                        <Route path="/reports" element={<Reports />} />
                        <Route path="/zakat" element={<Zakat />} />
                        <Route path="/cashbox" element={<Cashbox />} />
                        <Route path="/profit-test" element={<ProfitTestPage />} />
                        <Route path="/users" element={<Users />} />
                        <Route path="/settings" element={<Settings />} />
                        <Route path="/sales" element={<Navigate to="/customers" />} />
                        <Route path="*" element={<Navigate to="/" />} />
                      </Routes>
                    </ErrorBoundary>
                  </div>
                </>
              </ProtectedRoute>
            } />
          </Routes>
        </ErrorBoundary>
      </AppProvider>
    </div>
  );
};

export default App;
