import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  FaUser,
  FaUserFriends,
  FaFileInvoice,
  FaChartBar,
  FaMoneyBillWave,
  FaShoppingCart,
  FaCalendarAlt,
  FaSearch,
  FaFilter,
  FaPrint,
  FaDownload,
  FaExclamationTriangle,
  FaHistory,
  FaUndo,
  FaExchangeAlt,
  FaChartPie,
  FaChartLine,
  FaInfoCircle,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaEye,
  FaTimes,
  FaPercentage,
  FaSync,
  FaDatabase
} from 'react-icons/fa';
import DataTable from './DataTable';
import FormattedCurrency from './FormattedCurrency';
import Button from './Button';
import Card from './Card';
import DateRangePicker from './DateRangePicker';
import { formatDate, formatNumber } from '../utils/formatters';
import { getCustomerNameById } from '../utils/customerUtils';
import { createCachedQuery, cacheManager } from '../utils/database-optimization';
import './UnifiedCustomerReports.css';

/**
 * مكون تقارير العملاء الموحد
 * يجمع كل تقارير العملاء في مكان واحد مع واجهة مستخدم محسنة
 */
const UnifiedCustomerReports = () => {
  // استخدام واجهة API النافذة بدلاً من سياق التطبيق
  const showAlert = (type, message) => {
    // إظهار تنبيه بسيط في حالة عدم توفر دالة showAlert من سياق التطبيق
    console.log(`[${type}] ${message}`);
    // يمكن إضافة منطق لعرض تنبيه في واجهة المستخدم هنا
  };

  // حالة التقارير
  const [activeView, setActiveView] = useState('dashboard');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    endDate: new Date()
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // بيانات التقارير
  const [customers, setCustomers] = useState([]);
  const [topCustomers, setTopCustomers] = useState([]);
  const [subCustomersSales, setSubCustomersSales] = useState([]);
  const [customerInvoices, setCustomerInvoices] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerDetails, setCustomerDetails] = useState(null);

  // إحصائيات عامة
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalSales: 0,
    totalProfit: 0,
    averageSalePerCustomer: 0,
    profitMargin: 0
  });

  // مرجع للطباعة
  const printRef = useRef(null);

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  // تحميل البيانات عند تغيير نطاق التاريخ
  useEffect(() => {
    if (activeView !== 'dashboard') {
      loadReportData(activeView);
    }
  }, [dateRange, activeView]);

  // إنشاء دوال التخزين المؤقت
  const cachedApiFunctions = useMemo(() => {
    // بدء التنظيف التلقائي للتخزين المؤقت
    cacheManager.startAutoCleanup();

    // دالة تحميل العملاء مع تخزين مؤقت
    const cachedGetAllCustomers = createCachedQuery(
      async () => {
        if (window.api && window.api.customers && typeof window.api.customers.getAll === 'function') {
          return await window.api.customers.getAll();
        }
        console.warn('واجهة API للعملاء غير متوفرة');
        return [];
      },
      {
        functionName: 'getAllCustomers',
        cacheTime: 10 * 60 * 1000, // 10 دقائق
        autoRefresh: true,
        backgroundRefresh: true,
        refreshThreshold: 0.8 // تحديث البيانات عندما يتبقى 20% من وقت الصلاحية
      }
    );

    // دالة تحميل العملاء الأكثر شراءً مع تخزين مؤقت
    const cachedGetTopCustomers = createCachedQuery(
      async (filters) => {
        if (window.api && window.api.reports && typeof window.api.reports.getTopCustomersReport === 'function') {
          return await window.api.reports.getTopCustomersReport(filters);
        }
        console.warn('واجهة API لتقارير العملاء غير متوفرة');
        return { customers: [], stats: {} };
      },
      {
        functionName: 'getTopCustomers',
        cacheTime: 5 * 60 * 1000, // 5 دقائق
        autoRefresh: true,
        backgroundRefresh: true
      }
    );

    // دالة تحميل مبيعات العملاء الفرعيين مع تخزين مؤقت
    const cachedGetSubCustomersSales = createCachedQuery(
      async (parentId, filters) => {
        if (window.api && window.api.reports && typeof window.api.reports.getSubCustomersSalesReport === 'function') {
          return await window.api.reports.getSubCustomersSalesReport(parentId, filters);
        }
        console.warn('واجهة API لتقارير العملاء الفرعيين غير متوفرة');
        return { subCustomers: [] };
      },
      {
        functionName: 'getSubCustomersSales',
        cacheTime: 5 * 60 * 1000, // 5 دقائق
        autoRefresh: true,
        backgroundRefresh: true
      }
    );

    // دالة تحميل فواتير العميل مع تخزين مؤقت
    const cachedGetCustomerInvoices = createCachedQuery(
      async (filters) => {
        if (window.api && window.api.customers && typeof window.api.customers.getInvoices === 'function') {
          return await window.api.customers.getInvoices(filters);
        } else if (window.api && typeof window.api.invoke === 'function') {
          return await window.api.invoke('get-customer-invoices', filters);
        }
        console.warn('واجهة API لفواتير العملاء غير متوفرة');
        return { invoices: [] };
      },
      {
        functionName: 'getCustomerInvoices',
        cacheTime: 5 * 60 * 1000, // 5 دقائق
        autoRefresh: true,
        backgroundRefresh: true
      }
    );

    // إضافة مستمع لإيقاف التنظيف التلقائي عند إزالة المكون
    return {
      cachedGetAllCustomers,
      cachedGetTopCustomers,
      cachedGetSubCustomersSales,
      cachedGetCustomerInvoices,
      cleanup: () => {
        cacheManager.stopAutoCleanup();
      }
    };
  }, []);

  // إيقاف التنظيف التلقائي عند إزالة المكون
  useEffect(() => {
    return () => {
      if (cachedApiFunctions.cleanup) {
        cachedApiFunctions.cleanup();
      }
    };
  }, [cachedApiFunctions]);

  // تحميل البيانات الأولية
  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // تحميل قائمة العملاء باستخدام الدالة المخزنة مؤقتًا
      const allCustomers = await cachedApiFunctions.cachedGetAllCustomers();
      setCustomers(allCustomers || []);

      // تحميل لوحة المعلومات
      await loadDashboardData();

      setIsLoading(false);
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأولية:', error);
      setError('حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);
    }
  };

  // تحميل بيانات لوحة المعلومات
  const loadDashboardData = async () => {
    try {
      setError(null);

      // تحميل العملاء الأكثر شراءً (محدود لأعلى 5) باستخدام الدالة المخزنة مؤقتًا
      const filters = {
        startDate: formatDate(dateRange.startDate),
        endDate: formatDate(dateRange.endDate),
        limit: 5
      };

      const topCustomersData = await cachedApiFunctions.cachedGetTopCustomers(filters);

      if (topCustomersData) {
        setTopCustomers(topCustomersData.customers || []);
        setStats(topCustomersData.stats || {});
      } else {
        setTopCustomers([]);
        setStats({
          totalCustomers: 0,
          totalSales: 0,
          totalProfit: 0,
          averageSalePerCustomer: 0,
          profitMargin: 0
        });
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة المعلومات:', error);
      setError('حدث خطأ أثناء تحميل بيانات لوحة المعلومات.');
    }
  };

  // تحديث التخزين المؤقت وإعادة تحميل البيانات
  const refreshData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // مسح التخزين المؤقت للتقارير
      cacheManager.invalidateCache('getTopCustomers');
      cacheManager.invalidateCache('getSubCustomersSales');
      cacheManager.invalidateCache('getCustomerInvoices');

      // إعادة تحميل البيانات
      if (activeView === 'dashboard') {
        await loadDashboardData();
      } else {
        await loadReportData(activeView);
      }

      setIsLoading(false);
      showAlert('success', 'تم تحديث البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
      setError('حدث خطأ أثناء تحديث البيانات.');
      setIsLoading(false);
    }
  };

  // تحميل بيانات التقرير المحدد
  const loadReportData = async (reportType) => {
    setIsLoading(true);
    setError(null);

    try {
      const filters = {
        startDate: formatDate(dateRange.startDate),
        endDate: formatDate(dateRange.endDate)
      };

      switch (reportType) {
        case 'topCustomers':
          // استخدام الدالة المخزنة مؤقتًا لتحميل العملاء الأكثر شراءً
          const topCustomersData = await cachedApiFunctions.cachedGetTopCustomers(filters);
          setTopCustomers(topCustomersData.customers || []);
          setStats(topCustomersData.stats || {});
          break;

        case 'subCustomers':
          // استخدام الدالة المخزنة مؤقتًا لتحميل مبيعات العملاء الفرعيين
          const subCustomersData = await cachedApiFunctions.cachedGetSubCustomersSales(null, filters);
          setSubCustomersSales(subCustomersData.subCustomers || []);
          break;

        case 'customerInvoices':
          if (selectedCustomer) {
            await loadCustomerInvoices(selectedCustomer.id || selectedCustomer._id || selectedCustomer.customer_id);
          }
          break;

        case 'customerDetails':
          if (selectedCustomer) {
            await loadCustomerDetails(selectedCustomer.id || selectedCustomer._id || selectedCustomer.customer_id);
          }
          break;

        default:
          console.warn(`نوع التقرير غير معروف: ${reportType}`);
          break;
      }
    } catch (error) {
      console.error(`خطأ في تحميل بيانات التقرير ${reportType}:`, error);
      setError(`حدث خطأ أثناء تحميل البيانات. ${error.message || 'خطأ غير معروف'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // تحميل فواتير العميل
  const loadCustomerInvoices = async (customerId) => {
    try {
      console.log('جاري تحميل فواتير العميل بالمعرف:', customerId);

      if (!customerId) {
        console.error('معرف العميل غير متوفر');
        setError('معرف العميل غير متوفر');
        setCustomerInvoices([]);
        return;
      }

      const filters = {
        startDate: formatDate(dateRange.startDate),
        endDate: formatDate(dateRange.endDate),
        customerId
      };

      console.log('فلاتر البحث:', filters);

      // استخدام الدالة المخزنة مؤقتًا لتحميل فواتير العميل
      const result = await cachedApiFunctions.cachedGetCustomerInvoices(filters);

      if (result && result.invoices) {
        console.log(`تم تحميل ${result.invoices.length} فاتورة للعميل`);

        // ترتيب الفواتير حسب التاريخ (الأحدث أولاً)
        const sortedInvoices = [...result.invoices].sort((a, b) => {
          return new Date(b.invoice_date || b.date || 0) - new Date(a.invoice_date || a.date || 0);
        });

        setCustomerInvoices(sortedInvoices);
      } else {
        console.warn('لم يتم العثور على فواتير للعميل في النتيجة:', result);
        setCustomerInvoices([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل فواتير العميل:', error);
      setError('حدث خطأ أثناء تحميل فواتير العميل');
      setCustomerInvoices([]);
    }
  };

  // تحميل تفاصيل العميل
  const loadCustomerDetails = async (customerId) => {
    try {
      console.log('جاري تحميل تفاصيل العميل بالمعرف:', customerId);

      // تحميل فواتير العميل أولاً
      await loadCustomerInvoices(customerId);

      // تحميل تفاصيل العميل وإحصائياته
      const customer = customers.find(c => c.id === customerId);

      if (!customer) {
        console.log('العميل غير موجود في القائمة المحلية، محاولة الحصول عليه من API');

        // محاولة الحصول على العميل من API إذا لم يكن موجوداً في القائمة المحلية
        if (window.api && window.api.customers && typeof window.api.customers.getById === 'function') {
          console.log('استخدام window.api.customers.getById للحصول على تفاصيل العميل');
          const customerData = await window.api.customers.getById(customerId);
          console.log('نتيجة استدعاء window.api.customers.getById:', customerData);

          if (customerData) {
            console.log('تم الحصول على بيانات العميل بنجاح');

            setCustomerDetails({
              ...customerData,
              invoices: customerInvoices
            });
            return;
          }
        } else if (window.api && typeof window.api.invoke === 'function') {
          // محاولة استخدام واجهة invoke العامة
          console.log('استخدام window.api.invoke للحصول على تفاصيل العميل');
          try {
            const customerData = await window.api.invoke('get-customer-by-id', customerId);
            console.log('نتيجة استدعاء get-customer-by-id:', customerData);

            if (customerData) {
              console.log('تم الحصول على بيانات العميل بنجاح');

              setCustomerDetails({
                ...customerData,
                invoices: customerInvoices
              });
              return;
            }
          } catch (invokeError) {
            console.error('خطأ في استدعاء get-customer-by-id:', invokeError);
          }
        }

        console.error('لم يتم العثور على العميل بالمعرف:', customerId);
        setError('لم يتم العثور على العميل');
        return;
      }

      console.log('تم العثور على العميل في القائمة المحلية:', customer);

      setCustomerDetails({
        ...customer,
        invoices: customerInvoices
      });
    } catch (error) {
      console.error('خطأ في تحميل تفاصيل العميل:', error);
      setError('حدث خطأ أثناء تحميل تفاصيل العميل');
    }
  };

  // اختيار عميل
  const handleSelectCustomer = async (customer) => {
    console.log('تم اختيار العميل:', customer);

    // تعيين العميل المحدد
    setSelectedCustomer(customer);

    // تعيين العرض النشط
    setActiveView('customerDetails');

    // تحميل تفاصيل العميل
    setIsLoading(true);
    try {
      // استخدام معرف العميل (مع مراعاة الاختلافات في هيكل البيانات)
      const customerId = customer.id || customer._id || customer.customer_id;
      console.log('جاري تحميل تفاصيل العميل بالمعرف:', customerId);

      if (!customerId) {
        console.error('معرف العميل غير متوفر:', customer);
        setError('معرف العميل غير متوفر');
        setIsLoading(false);
        return;
      }

      await loadCustomerDetails(customerId);
    } catch (error) {
      console.error('خطأ في تحميل تفاصيل العميل:', error);
      setError('حدث خطأ أثناء تحميل تفاصيل العميل');
    } finally {
      setIsLoading(false);
    }
  };

  // تغيير نطاق التاريخ
  const handleDateRangeChange = (newRange) => {
    setDateRange(newRange);
  };

  // طباعة التقرير
  const handlePrintReport = () => {
    if (printRef.current) {
      const printContent = printRef.current;
      const printWindow = window.open('', '_blank');

      // محاولة الحصول على معلومات الشركة من الإعدادات
      let companyName = 'نظام إدارة المخزون';
      let companyLogo = '';
      let companyAddress = '';
      let companyPhone = '';

      // محاولة الحصول على معلومات الشركة من الإعدادات إذا كانت متوفرة
      if (window.api && window.api.settings && typeof window.api.settings.get === 'function') {
        window.api.settings.get().then(settings => {
          if (settings && settings.company) {
            companyName = settings.company.name || companyName;
            companyLogo = settings.company.logo || companyLogo;
            companyAddress = settings.company.address || companyAddress;
            companyPhone = settings.company.phone || companyPhone;
          }
        }).catch(err => {
          console.error('خطأ في الحصول على إعدادات الشركة:', err);
        });
      }

      const today = new Date();
      const formattedDate = `${today.getFullYear()}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getDate().toString().padStart(2, '0')}`;
      const formattedTime = `${today.getHours().toString().padStart(2, '0')}:${today.getMinutes().toString().padStart(2, '0')}`;

      printWindow.document.write(`
        <html>
          <head>
            <title>تقرير العملاء</title>
            <meta charset="UTF-8">
            <link rel="stylesheet" href="./print-styles.css">
            <style>
              @font-face {
                font-family: 'Cairo';
                src: local('Cairo'), url('./fonts/Cairo-Regular.ttf') format('truetype');
                font-weight: normal;
                font-style: normal;
              }

              body {
                font-family: 'Cairo', 'Arial', sans-serif;
                direction: rtl;
                padding: 20px;
                margin: 0;
              }

              .company-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 10px;
                border-bottom: 2px solid #333;
              }

              .company-logo {
                max-width: 150px;
                max-height: 80px;
                margin-bottom: 10px;
              }

              .company-name {
                font-size: 24px;
                font-weight: bold;
                margin: 5px 0;
              }

              .company-info {
                font-size: 14px;
                color: #555;
                margin: 5px 0;
              }

              .report-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
              }

              .report-date {
                font-size: 14px;
                color: #666;
              }

              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 14px;
              }

              th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: right;
              }

              th {
                background-color: #f2f2f2;
                font-weight: bold;
              }

              .report-header {
                text-align: center;
                margin-bottom: 20px;
              }

              .report-title {
                font-size: 20px;
                font-weight: bold;
                margin: 10px 0;
              }

              .stats-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-bottom: 20px;
              }

              .stat-card {
                border: 1px solid #ddd;
                padding: 10px;
                border-radius: 5px;
                flex: 1;
                min-width: 150px;
                background-color: #f9f9f9;
              }

              .stat-title {
                font-size: 14px;
                color: #666;
              }

              .stat-value {
                font-size: 18px;
                font-weight: bold;
              }

              .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #777;
                border-top: 1px solid #ddd;
                padding-top: 10px;
              }

              @media print {
                body {
                  font-size: 12pt;
                }

                .no-print {
                  display: none;
                }
              }
            </style>
          </head>
          <body>
            <div class="company-header">
              ${companyLogo ? `<img src="${companyLogo}" class="company-logo" alt="شعار الشركة">` : ''}
              <h1 class="company-name">${companyName}</h1>
              ${companyAddress ? `<p class="company-info">${companyAddress}</p>` : ''}
              ${companyPhone ? `<p class="company-info">هاتف: ${companyPhone}</p>` : ''}
            </div>

            <div class="report-info">
              <div class="report-date">
                <p>تاريخ التقرير: ${formattedDate}</p>
                <p>وقت الإنشاء: ${formattedTime}</p>
              </div>
            </div>

            ${printContent.innerHTML}

            <div class="footer">
              <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون - جميع الحقوق محفوظة &copy; ${today.getFullYear()}</p>
            </div>
          </body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // إضافة تأخير قصير للتأكد من تحميل الصفحة قبل الطباعة
      setTimeout(() => {
        printWindow.print();
      }, 500);
    }
  };

  // تصدير التقرير
  const handleExportReport = () => {
    // تنفيذ تصدير التقرير (CSV أو Excel)
    showAlert('info', 'جاري تطوير ميزة تصدير التقارير...');
  };

  // عرض لوحة المعلومات
  const renderDashboard = () => {
    return (
      <div className="customer-dashboard">
        {/* بطاقات الإحصائيات */}
        <div className="stats-cards">
          <Card className="stat-card">
            <div className="stat-icon"><FaUser /></div>
            <div className="stat-content">
              <h3 className="stat-value">{formatNumber(stats.totalCustomers || 0)}</h3>
              <p className="stat-title">إجمالي العملاء</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaMoneyBillWave /></div>
            <div className="stat-content">
              <h3 className="stat-value"><FormattedCurrency amount={stats.totalSales || 0} /></h3>
              <p className="stat-title">إجمالي المبيعات</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaChartLine /></div>
            <div className="stat-content">
              <h3 className="stat-value"><FormattedCurrency amount={stats.totalProfit || 0} isProfit={true} /></h3>
              <p className="stat-title">إجمالي الأرباح</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaPercentage /></div>
            <div className="stat-content">
              <h3 className="stat-value">{(stats.profitMargin || 0).toFixed(2)}%</h3>
              <p className="stat-title">نسبة الربح</p>
            </div>
          </Card>
        </div>

        {/* أعلى 5 عملاء */}
        <Card className="top-customers-card">
          <div className="card-header">
            <h3><FaUser /> أعلى 5 عملاء</h3>
            <Button
              variant="primary"
              size="sm"
              icon={<FaEye />}
              onClick={() => setActiveView('topCustomers')}
            >
              عرض الكل
            </Button>
          </div>

          <DataTable
            columns={[
              {
                header: '#',
                accessor: 'index',
                cell: (_, index) => index + 1,
                style: { width: '50px' }
              },
              {
                header: 'اسم العميل',
                accessor: 'customer_name',
                cell: (row) => row.customer_name
              },
              {
                header: 'إجمالي المبيعات',
                accessor: 'total_sales',
                cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
              },
              {
                header: 'إجمالي الأرباح',
                accessor: 'total_profit',
                cell: (row) => <FormattedCurrency amount={row.total_profit || 0} isProfit={true} />
              },
              {
                header: 'عدد الفواتير',
                accessor: 'invoice_count',
                cell: (row) => row.invoice_count || 0
              }
            ]}
            data={topCustomers}
            pagination={false}
            searchable={false}
            emptyMessage="لا توجد بيانات للعرض"
            onRowClick={handleSelectCustomer}
          />
        </Card>
      </div>
    );
  };

  // عرض العملاء الأكثر شراءً
  const renderTopCustomers = () => {
    return (
      <div className="top-customers-report">
        <div className="report-header">
          <h2><FaUser /> تقرير العملاء الأكثر شراءً</h2>
          <p className="report-period">
            الفترة: {formatDate(dateRange.startDate)} إلى {formatDate(dateRange.endDate)}
          </p>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="stats-cards">
          <Card className="stat-card">
            <div className="stat-icon"><FaUser /></div>
            <div className="stat-content">
              <h3 className="stat-value">{formatNumber(stats.totalCustomers || 0)}</h3>
              <p className="stat-title">إجمالي العملاء</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaMoneyBillWave /></div>
            <div className="stat-content">
              <h3 className="stat-value"><FormattedCurrency amount={stats.totalSales || 0} /></h3>
              <p className="stat-title">إجمالي المبيعات</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaChartLine /></div>
            <div className="stat-content">
              <h3 className="stat-value"><FormattedCurrency amount={stats.totalProfit || 0} isProfit={true} /></h3>
              <p className="stat-title">إجمالي الأرباح</p>
            </div>
          </Card>

          <Card className="stat-card">
            <div className="stat-icon"><FaPercentage /></div>
            <div className="stat-content">
              <h3 className="stat-value">{(stats.profitMargin || 0).toFixed(2)}%</h3>
              <p className="stat-title">نسبة الربح</p>
            </div>
          </Card>
        </div>

        {/* جدول العملاء الأكثر شراءً */}
        <Card className="data-card">
          <DataTable
            columns={[
              {
                header: '#',
                accessor: 'index',
                cell: (_, index) => index + 1,
                style: { width: '50px' }
              },
              {
                header: 'اسم العميل',
                accessor: 'customer_name',
                cell: (row) => row.customer_name
              },
              {
                header: 'نوع العميل',
                accessor: 'customer_type',
                cell: (row) => row.customer_type === 'regular' ? 'دائم' : 'عادي'
              },
              {
                header: 'رقم الهاتف',
                accessor: 'phone',
                cell: (row) => row.phone || '-'
              },
              {
                header: 'عدد المعاملات',
                accessor: 'transaction_count',
                cell: (row) => row.transaction_count || 0
              },
              {
                header: 'عدد الفواتير',
                accessor: 'invoice_count',
                cell: (row) => row.invoice_count || 0
              },
              {
                header: 'إجمالي المبيعات',
                accessor: 'total_sales',
                cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
              },
              {
                header: 'إجمالي الأرباح',
                accessor: 'total_profit',
                cell: (row) => <FormattedCurrency amount={row.total_profit || 0} isProfit={true} />
              },
              {
                header: 'نسبة الربح',
                accessor: 'profit_margin',
                cell: (row) => `${row.profit_margin ? row.profit_margin.toFixed(2) : 0}%`
              },
              {
                header: 'آخر عملية شراء',
                accessor: 'last_purchase_date',
                cell: (row) => row.last_purchase_date ? formatDate(row.last_purchase_date) : '-'
              }
            ]}
            data={topCustomers}
            pagination={true}
            pageSize={10}
            searchable={true}
            searchPlaceholder="بحث عن عميل..."
            emptyMessage="لا توجد بيانات للعرض"
            onRowClick={handleSelectCustomer}
          />
        </Card>

        {/* عرض الأصناف الأكثر شراءً لكل عميل */}
        {topCustomers.map((customer) => {
          if (!customer.top_items || customer.top_items.length === 0) return null;

          return (
            <Card key={customer.customer_id} className="customer-items-card mt-4">
              <div className="card-header">
                <h3>الأصناف الأكثر شراءً للعميل: {customer.customer_name}</h3>
              </div>

              <DataTable
                columns={[
                  {
                    header: '#',
                    accessor: 'index',
                    cell: (_, index) => index + 1,
                    style: { width: '50px' }
                  },
                  {
                    header: 'الصنف',
                    accessor: 'item_name',
                    cell: (row) => row.item_name
                  },
                  {
                    header: 'الكمية المباعة',
                    accessor: 'total_quantity',
                    cell: (row) => row.total_quantity || 0
                  },
                  {
                    header: 'إجمالي المبيعات',
                    accessor: 'total_sales',
                    cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
                  }
                ]}
                data={customer.top_items}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد أصناف لهذا العميل"
              />
            </Card>
          );
        })}
      </div>
    );
  };

  // عرض مبيعات العملاء الفرعيين
  const renderSubCustomers = () => {
    return (
      <div className="sub-customers-report">
        <div className="report-header">
          <h2><FaUserFriends /> تقرير مبيعات العملاء الفرعيين</h2>
          <p className="report-period">
            الفترة: {formatDate(dateRange.startDate)} إلى {formatDate(dateRange.endDate)}
          </p>
        </div>

        {/* جدول العملاء الفرعيين */}
        <Card className="data-card">
          <DataTable
            columns={[
              {
                header: '#',
                accessor: 'index',
                cell: (_, index) => index + 1,
                style: { width: '50px' }
              },
              {
                header: 'اسم العميل الرئيسي',
                accessor: 'parentName',
                cell: (row) => row.parentName
              },
              {
                header: 'اسم العميل الفرعي',
                accessor: 'subCustomerName',
                cell: (row) => row.subCustomerName
              },
              {
                header: 'عدد عمليات البيع',
                accessor: 'salesCount',
                cell: (row) => row.salesCount || 0
              },
              {
                header: 'إجمالي المبيعات',
                accessor: 'totalSales',
                cell: (row) => <FormattedCurrency amount={row.totalSales || 0} />
              },
              {
                header: 'إجمالي الأرباح',
                accessor: 'totalProfit',
                cell: (row) => <FormattedCurrency amount={row.totalProfit || 0} isProfit={true} />
              },
              {
                header: 'نسبة الربح',
                accessor: 'profitMargin',
                cell: (row) => `${row.profitMargin ? row.profitMargin.toFixed(2) : 0}%`
              }
            ]}
            data={subCustomersSales}
            pagination={true}
            pageSize={10}
            searchable={true}
            searchPlaceholder="بحث عن عميل..."
            emptyMessage="لا توجد بيانات للعرض"
          />
        </Card>
      </div>
    );
  };

  // عرض فواتير العملاء
  const renderCustomerInvoices = () => {
    return (
      <div className="customer-invoices-report">
        <div className="report-header">
          <h2><FaFileInvoice /> تقرير فواتير العملاء</h2>
          <p className="report-period">
            الفترة: {formatDate(dateRange.startDate)} إلى {formatDate(dateRange.endDate)}
          </p>
        </div>

        {/* اختيار العميل */}
        <Card className="customer-selector-card">
          <div className="card-header">
            <h3>اختر العميل</h3>
          </div>

          <div className="customer-selector">
            <div className="search-box">
              <FaSearch />
              <input
                type="text"
                placeholder="ابحث عن عميل..."
                onChange={(e) => {
                  // تنفيذ البحث عن العملاء
                  const searchTerm = e.target.value.toLowerCase();
                  // يمكن تنفيذ البحث هنا
                }}
              />
            </div>

            <div className="customers-list">
              {customers.length === 0 ? (
                <div className="empty-message">لا يوجد عملاء</div>
              ) : (
                customers.map((customer) => (
                  <div
                    key={customer.id || customer._id}
                    className={`customer-item ${selectedCustomer && (selectedCustomer.id === customer.id || selectedCustomer._id === customer._id) ? 'selected' : ''}`}
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <div className="customer-icon"><FaUser /></div>
                    <div className="customer-name">{customer.name}</div>
                  </div>
                ))
              )}
            </div>
          </div>
        </Card>

        {/* عرض فواتير العميل المحدد */}
        {selectedCustomer && (
          <Card className="customer-invoices-card">
            <div className="card-header">
              <h3>فواتير العميل: {selectedCustomer.name}</h3>
              <Button
                variant="primary"
                size="sm"
                icon={<FaSync />}
                onClick={() => loadCustomerInvoices(selectedCustomer.id || selectedCustomer._id)}
              >
                تحديث الفواتير
              </Button>
            </div>

            {isLoading ? (
              <div className="loading-state">
                <div className="spinner"></div>
                <p>جاري تحميل الفواتير...</p>
              </div>
            ) : customerInvoices.length === 0 ? (
              <div className="empty-state">
                <FaFileInvoice />
                <h3>لا توجد فواتير</h3>
                <p>لا توجد فواتير لهذا العميل في الفترة المحددة</p>
              </div>
            ) : (
              <>
                {/* إحصائيات الفواتير */}
                <div className="invoices-stats">
                  <div className="stat-item">
                    <div className="stat-label">عدد الفواتير</div>
                    <div className="stat-value">{customerInvoices.length}</div>
                  </div>

                  <div className="stat-item">
                    <div className="stat-label">إجمالي قيمة الفواتير</div>
                    <div className="stat-value">
                      <FormattedCurrency
                        amount={customerInvoices.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0)}
                      />
                    </div>
                  </div>
                </div>

                {/* جدول الفواتير */}
                <DataTable
                  columns={[
                    {
                      header: '#',
                      accessor: 'index',
                      cell: (_, index) => index + 1,
                      style: { width: '50px' }
                    },
                    {
                      header: 'رقم الفاتورة',
                      accessor: 'invoice_number',
                      cell: (row) => row.invoice_number || `INV-${row.id.toString().padStart(6, '0')}`
                    },
                    {
                      header: 'التاريخ',
                      accessor: 'invoice_date',
                      cell: (row) => formatDate(row.invoice_date)
                    },
                    {
                      header: 'إجمالي القيمة',
                      accessor: 'total_amount',
                      cell: (row) => <FormattedCurrency amount={row.total_amount || 0} />
                    },
                    {
                      header: 'حالة الدفع',
                      accessor: 'payment_status',
                      cell: (row) => (
                        <span className={`payment-status ${row.payment_status}`}>
                          {row.payment_status === 'paid' ? 'مدفوعة' : 'غير مدفوعة'}
                        </span>
                      )
                    },
                    {
                      header: 'ملاحظات',
                      accessor: 'notes',
                      cell: (row) => row.notes || '-'
                    }
                  ]}
                  data={customerInvoices}
                  pagination={true}
                  pageSize={10}
                  searchable={true}
                  searchPlaceholder="بحث عن فاتورة..."
                  emptyMessage="لا توجد فواتير للعرض"
                />
              </>
            )}
          </Card>
        )}
      </div>
    );
  };

  // عرض تفاصيل العميل
  const renderCustomerDetails = () => {
    if (!customerDetails) return null;

    return (
      <div className="customer-details">
        <div className="report-header">
          <h2><FaUser /> تفاصيل العميل: {customerDetails.name}</h2>
          <Button
            variant="light"
            size="sm"
            icon={<FaTimes />}
            onClick={() => {
              setSelectedCustomer(null);
              setCustomerDetails(null);
              setActiveView('dashboard');
            }}
          >
            العودة
          </Button>
        </div>

        {/* معلومات العميل */}
        <Card className="customer-info-card">
          <div className="customer-info-section">
            <h3>المعلومات الأساسية</h3>

            <div className="info-item">
              <div className="info-label">الاسم:</div>
              <div className="info-value">{customerDetails.name}</div>
            </div>

            <div className="info-item">
              <div className="info-label">نوع العميل:</div>
              <div className="info-value">{customerDetails.customer_type === 'regular' ? 'دائم' : 'عادي'}</div>
            </div>

            <div className="info-item">
              <div className="info-label">رقم الهاتف:</div>
              <div className="info-value">{customerDetails.phone || '-'}</div>
            </div>

            <div className="info-item">
              <div className="info-label">البريد الإلكتروني:</div>
              <div className="info-value">{customerDetails.email || '-'}</div>
            </div>

            <div className="info-item">
              <div className="info-label">العنوان:</div>
              <div className="info-value">{customerDetails.address || '-'}</div>
            </div>
          </div>


        </Card>

        {/* إحصائيات العميل */}
        <div className="stats-cards">
          <Card className="stat-card">
            <div className="stat-icon"><FaFileInvoice /></div>
            <div className="stat-content">
              <h3 className="stat-value">{customerInvoices.length}</h3>
              <p className="stat-title">عدد الفواتير</p>
            </div>
          </Card>
        </div>

        {/* فواتير العميل */}
        <Card className="customer-invoices-card">
          <div className="card-header">
            <h3>فواتير العميل</h3>
          </div>

          <DataTable
            columns={[
              {
                header: '#',
                accessor: 'index',
                cell: (_, index) => index + 1,
                style: { width: '50px' }
              },
              {
                header: 'رقم الفاتورة',
                accessor: 'invoice_number',
                cell: (row) => row.invoice_number || `INV-${row.id.toString().padStart(6, '0')}`
              },
              {
                header: 'التاريخ',
                accessor: 'invoice_date',
                cell: (row) => formatDate(row.invoice_date)
              },
              {
                header: 'إجمالي القيمة',
                accessor: 'total_amount',
                cell: (row) => <FormattedCurrency amount={row.total_amount || 0} />
              },
              {
                header: 'حالة الدفع',
                accessor: 'payment_status',
                cell: (row) => (
                  <span className={`payment-status ${row.payment_status}`}>
                    {row.payment_status === 'paid' ? 'مدفوعة' : 'غير مدفوعة'}
                  </span>
                )
              }
            ]}
            data={customerInvoices}
            pagination={true}
            pageSize={5}
            searchable={true}
            searchPlaceholder="بحث عن فاتورة..."
            emptyMessage="لا توجد فواتير للعرض"
          />
        </Card>
      </div>
    );
  };

  // عرض واجهة المستخدم الرئيسية
  return (
    <div className="unified-customer-reports">
      {/* شريط الأدوات */}
      <div className="reports-toolbar">
        <div className="view-selector">
          <Button
            variant={activeView === 'dashboard' ? 'primary' : 'light'}
            icon={<FaChartPie />}
            onClick={() => setActiveView('dashboard')}
          >
            لوحة المعلومات
          </Button>

          <Button
            variant={activeView === 'topCustomers' ? 'primary' : 'light'}
            icon={<FaUser />}
            onClick={() => setActiveView('topCustomers')}
          >
            العملاء الأكثر شراءً
          </Button>

          <Button
            variant={activeView === 'subCustomers' ? 'primary' : 'light'}
            icon={<FaUserFriends />}
            onClick={() => setActiveView('subCustomers')}
          >
            العملاء الفرعيين
          </Button>

          <Button
            variant={activeView === 'customerInvoices' ? 'primary' : 'light'}
            icon={<FaFileInvoice />}
            onClick={() => setActiveView('customerInvoices')}
          >
            فواتير العملاء
          </Button>
        </div>

        <div className="date-filter">
          <DateRangePicker
            startDate={dateRange.startDate}
            endDate={dateRange.endDate}
            onChange={handleDateRangeChange}
          />
        </div>

        <div className="actions">
          <Button
            variant="light"
            icon={<FaPrint />}
            onClick={handlePrintReport}
          >
            طباعة
          </Button>

          <Button
            variant="light"
            icon={<FaDownload />}
            onClick={handleExportReport}
          >
            تصدير
          </Button>
        </div>
      </div>

      {/* محتوى التقرير */}
      <div className="report-content" ref={printRef}>
        {isLoading ? (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <FaExclamationTriangle />
            <h3>حدث خطأ</h3>
            <p>{error}</p>
            <Button onClick={() => loadReportData(activeView)}>إعادة المحاولة</Button>
          </div>
        ) : (
          <>
            {activeView === 'dashboard' && renderDashboard()}
            {activeView === 'topCustomers' && renderTopCustomers()}
            {activeView === 'subCustomers' && renderSubCustomers()}
            {activeView === 'customerInvoices' && renderCustomerInvoices()}
            {activeView === 'customerDetails' && renderCustomerDetails()}
          </>
        )}
      </div>
    </div>
  );
};

export default UnifiedCustomerReports;
