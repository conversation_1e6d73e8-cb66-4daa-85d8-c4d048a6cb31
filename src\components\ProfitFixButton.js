import React, { useState } from 'react';

const ProfitFixButton = ({ onSuccess, onError }) => {
  const [isFixing, setIsFixing] = useState(false);
  const [results, setResults] = useState(null);

  const handleFixProfits = async () => {
    try {
      setIsFixing(true);
      setResults(null);
      
      console.log('[PROFIT-FIX-COMPONENT] بدء إصلاح الأرباح...');
      
      // التحقق من توفر window.api
      if (!window.api) {
        throw new Error('window.api غير متوفر');
      }
      
      if (!window.api.updateProfitValues) {
        throw new Error('window.api.updateProfitValues غير متوفر');
      }
      
      const result = await window.api.updateProfitValues();
      
      console.log('[PROFIT-FIX-COMPONENT] نتيجة إصلاح الأرباح:', result);
      
      if (result.success) {
        setResults(result);
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        throw new Error(result.error || 'فشل في إصلاح الأرباح');
      }
      
    } catch (error) {
      console.error('[PROFIT-FIX-COMPONENT] خطأ في إصلاح الأرباح:', error);
      if (onError) {
        onError(error);
      }
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '1px solid #ddd', 
      borderRadius: '8px', 
      margin: '10px 0',
      backgroundColor: '#f9f9f9'
    }}>
      <h3>🔧 إصلاح الأرباح</h3>
      
      <button
        onClick={handleFixProfits}
        disabled={isFixing}
        style={{
          backgroundColor: isFixing ? '#ccc' : '#28a745',
          color: 'white',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: isFixing ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          marginBottom: '10px'
        }}
      >
        {isFixing ? 'جاري الإصلاح...' : 'إصلاح الأرباح'}
      </button>
      
      {results && (
        <div style={{
          backgroundColor: '#d4edda',
          color: '#155724',
          padding: '10px',
          borderRadius: '5px',
          border: '1px solid #c3e6cb'
        }}>
          <h4>✅ تم الإصلاح بنجاح!</h4>
          <p><strong>عدد المعاملات المحدثة:</strong> {results.updatedCount}</p>
          <p><strong>إجمالي الأرباح الجديد:</strong> {results.totalProfit}</p>
          {results.message && <p><strong>الرسالة:</strong> {results.message}</p>}
        </div>
      )}
      
      <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
        <p>هذا الزر يقوم بإعادة حساب جميع الأرباح في النظام وتحديث قاعدة البيانات.</p>
      </div>
    </div>
  );
};

export default ProfitFixButton;
