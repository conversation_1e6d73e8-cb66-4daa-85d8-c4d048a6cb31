/**
 * وظائف تحديث الأرباح
 * هذا الملف يحتوي على وظائف لتحديث قيم الأرباح في جميع أنحاء التطبيق
 */

/**
 * تحديث قيم الأرباح في جميع أنحاء التطبيق
 * @param {Object} api - واجهة برمجة التطبيق
 * @param {Function} setLoading - وظيفة لتحديث حالة التحميل
 * @param {Function} showNotification - وظيفة لعرض الإشعارات
 * @param {Object} callbacks - وظائف استدعاء لتحديث الحالة
 * @returns {Promise<void>}
 */
export const updateProfitValues = async (api, setLoading, showNotification, callbacks = {}) => {
  const {
    setCashbox,
    setProfits,
    setTransactions,
    setTopSellingItems,
    setMostProfitableItems
  } = callbacks;

  try {
    if (setLoading) setLoading(true);

    // تحديث الخزينة
    if (api.cashbox && api.cashbox.get && setCashbox) {
      try {
        const cashboxData = await api.cashbox.get();
        if (cashboxData) {
          setCashbox(cashboxData);
        }
      } catch (error) {
        console.error('خطأ في تحديث الخزينة:', error);
      }
    }

    // تحديث تقرير الأرباح
    if (api.reports && api.reports.getProfitsReport && setProfits) {
      try {
        const profitsReport = await api.reports.getProfitsReport();
        if (profitsReport && profitsReport.stats) {
          // تحديث الأرباح للفترات المختلفة
          const now = new Date();
          const currentYear = now.getFullYear();
          const currentMonth = now.getMonth();

          // تصفية المعاملات حسب الفترات الزمنية
          if (profitsReport.transactions && profitsReport.transactions.length > 0) {
            // الربع سنوي
            const quarterlyTransactions = profitsReport.transactions.filter(t => {
              const transactionDate = new Date(t.transaction_date);
              return (
                transactionDate.getFullYear() === currentYear &&
                transactionDate.getMonth() >= currentMonth - 3 &&
                transactionDate.getMonth() <= currentMonth
              );
            });

            const quarterlyProfit = quarterlyTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

            // النصف سنوي
            const halfYearlyTransactions = profitsReport.transactions.filter(t => {
              const transactionDate = new Date(t.transaction_date);
              return (
                transactionDate.getFullYear() === currentYear &&
                transactionDate.getMonth() >= currentMonth - 6 &&
                transactionDate.getMonth() <= currentMonth
              );
            });

            const halfYearlyProfit = halfYearlyTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

            // ثلاثة أرباع السنة
            const threeQuartersTransactions = profitsReport.transactions.filter(t => {
              const transactionDate = new Date(t.transaction_date);
              return (
                transactionDate.getFullYear() === currentYear &&
                transactionDate.getMonth() >= currentMonth - 9 &&
                transactionDate.getMonth() <= currentMonth
              );
            });

            const threeQuartersProfit = threeQuartersTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

            // السنوي
            const yearlyProfit = profitsReport.stats.totalProfit || 0;

            setProfits({
              quarterly: quarterlyProfit,
              halfYearly: halfYearlyProfit,
              threeQuarters: threeQuartersProfit,
              yearly: yearlyProfit
            });
          }
        }
      } catch (error) {
        console.error('خطأ في تحديث تقرير الأرباح:', error);
      }
    }

    // تحديث المعاملات
    if (api.transactions && api.transactions.getAll && setTransactions) {
      try {
        const transactionsData = await api.transactions.getAll();
        if (Array.isArray(transactionsData)) {
          setTransactions(transactionsData);
        }
      } catch (error) {
        console.error('خطأ في تحديث المعاملات:', error);
      }
    }

    // تحديث الأصناف الأكثر مبيعًا
    if (api.reports && api.reports.getTopSellingItemsReport && setTopSellingItems) {
      try {
        const topSellingItemsReport = await api.reports.getTopSellingItemsReport();
        if (topSellingItemsReport && topSellingItemsReport.items) {
          setTopSellingItems(topSellingItemsReport.items);
        }
      } catch (error) {
        console.error('خطأ في تحديث الأصناف الأكثر مبيعًا:', error);
      }
    }

    // تحديث الأصناف الأكثر ربحًا
    if (api.reports && api.reports.getTopProfitableItemsReport && setMostProfitableItems) {
      try {
        const mostProfitableItemsReport = await api.reports.getTopProfitableItemsReport();
        if (mostProfitableItemsReport && mostProfitableItemsReport.items) {
          setMostProfitableItems(mostProfitableItemsReport.items);
        }
      } catch (error) {
        console.error('خطأ في تحديث الأصناف الأكثر ربحًا:', error);
      }
    }

    if (showNotification) {
      showNotification('تم تحديث البيانات بنجاح', 'success');
    }
  } catch (error) {
    console.error('خطأ في تحديث قيم الأرباح:', error);
    if (showNotification) {
      showNotification('حدث خطأ أثناء تحديث البيانات', 'error');
    }
  } finally {
    if (setLoading) setLoading(false);
  }
};

/**
 * تحديث قيم الربح في قاعدة البيانات
 * @param {Object} api - واجهة برمجة التطبيق
 * @param {Function} setLoading - وظيفة لتحديث حالة التحميل
 * @param {Function} showNotification - وظيفة لعرض الإشعارات
 * @param {Function} setCashbox - وظيفة لتحديث حالة الخزينة (اختياري)
 * @param {Function} setProfits - وظيفة لتحديث قيم الأرباح (اختياري)
 * @returns {Promise<void>}
 */
export const updateProfitValuesInDatabase = async (api, setLoading, showNotification, setCashbox, setProfits) => {
  try {
    if (setLoading) setLoading(true);
    console.log('بدء تحديث قيم الربح في قاعدة البيانات...');

    if (showNotification) {
      showNotification('جاري تحديث قيم الربح في قاعدة البيانات...', 'info');
    }

    // استخدام window.api.invoke إذا كان متاحًا، وإلا استخدام window.invokeChannel
    const invokeFunction = window.api && window.api.invoke ? window.api.invoke : window.invokeChannel;

    // تحديث الخزينة أولاً إذا كانت دالة setCashbox متوفرة
    if (setCashbox) {
      try {
        console.log('جاري تحديث بيانات الخزينة...');
        let cashboxData = null;

        if (api && api.cashbox && api.cashbox.get) {
          cashboxData = await api.cashbox.get();
        } else if (invokeFunction) {
          cashboxData = await invokeFunction('get-cashbox');
        }

        if (cashboxData) {
          console.log('تم الحصول على بيانات الخزينة:', cashboxData);
          setCashbox(cashboxData);
        }
      } catch (cashboxError) {
        console.error('خطأ في تحديث الخزينة:', cashboxError);
      }
    }

    // تحديث تقرير الأرباح إذا كانت دالة setProfits متوفرة
    if (setProfits) {
      try {
        console.log('جاري تحديث بيانات الأرباح...');
        let profitsReport = null;

        if (api && api.reports && api.reports.getProfitsReport) {
          profitsReport = await api.reports.getProfitsReport();
        } else if (invokeFunction) {
          profitsReport = await invokeFunction('get-profits-report');
        }

        if (profitsReport && profitsReport.stats) {
          console.log('تم الحصول على تقرير الأرباح:', profitsReport);

          // حساب الأرباح للفترات المختلفة
          let quarterly = profitsReport.stats.totalProfit || 0;
          let halfYearly = profitsReport.stats.totalProfit || 0;
          let threeQuarters = profitsReport.stats.totalProfit || 0;
          let yearly = profitsReport.stats.totalProfit || 0;

          // استخدام الأرباح الشهرية إذا كانت متوفرة
          if (profitsReport.monthlyProfits && profitsReport.monthlyProfits.length > 0) {
            console.log('استخدام الأرباح الشهرية لحساب الفترات المختلفة');

            // ترتيب الأرباح الشهرية حسب التاريخ (الأحدث أولاً)
            const sortedMonthlyProfits = [...profitsReport.monthlyProfits].sort((a, b) => {
              return new Date(b.month) - new Date(a.month);
            });

            quarterly = sortedMonthlyProfits.slice(0, 3).reduce((sum, month) => sum + (month.total_profit || 0), 0);
            halfYearly = sortedMonthlyProfits.slice(0, 6).reduce((sum, month) => sum + (month.total_profit || 0), 0);
            threeQuarters = sortedMonthlyProfits.slice(0, 9).reduce((sum, month) => sum + (month.total_profit || 0), 0);
            yearly = sortedMonthlyProfits.slice(0, 12).reduce((sum, month) => sum + (month.total_profit || 0), 0);
          }

          // تحديث قيم الأرباح
          setProfits({
            quarterly,
            halfYearly,
            threeQuarters,
            yearly
          });

          console.log('تم تحديث قيم الأرباح:', { quarterly, halfYearly, threeQuarters, yearly });
        }
      } catch (profitsError) {
        console.error('خطأ في تحديث تقرير الأرباح:', profitsError);
      }
    }

    // استدعاء وظيفة تحديث قيم الربح في قاعدة البيانات
    if (api && api.utils && api.utils.updateProfitValues) {
      const result = await api.utils.updateProfitValues();

      if (result.success) {
        if (showNotification) {
          showNotification(`تم تحديث ${result.updatedCount} معاملة بيع بنجاح`, 'success');
        }
      } else {
        if (showNotification) {
          showNotification(`حدث خطأ أثناء تحديث قيم الربح: ${result.error}`, 'error');
        }
      }
    } else if (invokeFunction) {
      // استخدام invokeFunction كبديل
      try {
        const result = await invokeFunction('update-profit-values');

        if (result && result.success) {
          if (showNotification) {
            showNotification(`تم تحديث ${result.updatedCount} معاملة بيع بنجاح`, 'success');
          }
        } else {
          if (showNotification) {
            showNotification(`حدث خطأ أثناء تحديث قيم الربح: ${result ? result.error : 'خطأ غير معروف'}`, 'error');
          }
        }
      } catch (invokeError) {
        console.error('خطأ في استدعاء update-profit-values:', invokeError);
        if (showNotification) {
          showNotification(`حدث خطأ أثناء تحديث قيم الربح: ${invokeError.message}`, 'error');
        }
      }
    } else {
      console.log('وظيفة تحديث قيم الربح غير متوفرة، تم تحديث البيانات من مصادر أخرى');
      if (showNotification) {
        showNotification('تم تحديث البيانات بنجاح', 'success');
      }
    }
  } catch (error) {
    console.error('خطأ في تحديث قيم الربح في قاعدة البيانات:', error);
    if (showNotification) {
      showNotification(`حدث خطأ أثناء تحديث قيم الربح: ${error.message}`, 'error');
    }
  } finally {
    if (setLoading) setLoading(false);
    console.log('اكتمل تحديث قيم الربح في قاعدة البيانات');
  }
};

export default {
  updateProfitValues,
  updateProfitValuesInDatabase
};
