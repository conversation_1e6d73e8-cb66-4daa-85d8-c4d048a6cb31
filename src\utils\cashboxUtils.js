/**
 * وظائف مساعدة للخزينة
 * هذا الملف يحتوي على وظائف مساعدة للتعامل مع الخزينة
 */

/**
 * حساب توزيع المبلغ بين الرصيد الحالي والأرباح
 *
 * هذه الدالة تحسب كيفية توزيع المبلغ المضاف بين الرصيد الحالي والأرباح
 * بحيث لا يتجاوز الرصيد الحالي الرصيد الافتتاحي.
 *
 * المبدأ الأساسي هو:
 * 1. إذا كان الرصيد الحالي + المبلغ المضاف <= الرصيد الافتتاحي، يضاف كل المبلغ إلى الرصيد الحالي.
 * 2. إذا كان الرصيد الحالي + المبلغ المضاف > الرصيد الافتتاحي، يضاف فقط ما يكفي للوصول إلى الرصيد الافتتاحي،
 *    والباقي يذهب إلى الأرباح.
 *
 * @param {number} amount - المبلغ المراد إضافته
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - توزيع المبلغ
 */
const calculateBalanceDistribution = (amount, currentBalance, initialBalance) => {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numAmount = Number(amount) || 0;
  const numCurrentBalance = Number(currentBalance) || 0;
  const numInitialBalance = Number(initialBalance) || 0;

  // حساب المبلغ الذي يمكن إضافته إلى الرصيد الحالي
  // هذا هو الحد الأقصى للمبلغ الذي يمكن إضافته إلى الرصيد الحالي بحيث لا يتجاوز الرصيد الافتتاحي
  const maxAddableAmount = Math.max(0, numInitialBalance - numCurrentBalance);

  // حساب المبلغ الذي سيضاف إلى الرصيد الحالي
  const amountToAddToBalance = Math.min(numAmount, maxAddableAmount);

  // حساب المبلغ الزائد الذي سيضاف إلى الأرباح
  const excessAmount = Math.max(0, numAmount - amountToAddToBalance);

  // التحقق مما إذا كان هناك مبلغ زائد
  const hasExcess = excessAmount > 0;

  console.log(`[CASHBOX-UTILS] توزيع المبلغ - المبلغ الكلي: ${numAmount}, للرصيد: ${amountToAddToBalance}, للأرباح: ${excessAmount}`);

  return {
    amountToAddToBalance,  // المبلغ الذي سيضاف إلى الرصيد الحالي
    excessAmount,          // المبلغ الزائد الذي سيضاف إلى الأرباح
    hasExcess              // هل هناك مبلغ زائد؟
  };
};

/**
 * تحديث الخزينة بعد المعاملة
 * @param {Object} cashbox - الخزينة الحالية
 * @param {Object} transaction - المعاملة
 * @returns {Object} - الخزينة المحدثة
 */
const updateCashboxAfterTransaction = (cashbox, transaction) => {
  if (!cashbox || !transaction) {
    console.log('[CASHBOX-UTILS] خطأ: الخزينة أو المعاملة غير موجودة');
    return cashbox;
  }

  // نسخة جديدة من الخزينة
  const updatedCashbox = { ...cashbox };

  const { type, amount, profit = 0 } = transaction;
  const numAmount = Number(amount) || 0;
  const numProfit = Number(profit) || 0;

  console.log(`[CASHBOX-UTILS] تحديث الخزينة - النوع: ${type}, المبلغ: ${numAmount}, الربح: ${numProfit}`);

  if (type === 'income' || type === 'sale') {
    // في حالة الدخل أو البيع
    console.log(`[CASHBOX-UTILS] معاملة دخل/بيع - المبلغ: ${numAmount}, الربح: ${numProfit}`);

    // تحديث الرصيد الحالي (يمكن أن يتجاوز الرصيد الافتتاحي)
    updatedCashbox.current_balance += numAmount;

    // تحديث إجمالي الأرباح
    updatedCashbox.profit_total += numProfit;

    // تحديث إجمالي المبيعات
    updatedCashbox.sales_total += numAmount;

    console.log(`[CASHBOX-UTILS] بعد التحديث - الرصيد: ${updatedCashbox.current_balance}, الأرباح: ${updatedCashbox.profit_total}, المبيعات: ${updatedCashbox.sales_total}`);
  } else if (type === 'expense' || type === 'purchase') {
    // في حالة المصروفات أو الشراء
    console.log(`[CASHBOX-UTILS] معاملة مصروفات/شراء - المبلغ: ${numAmount}`);

    // تحديث الرصيد الحالي
    updatedCashbox.current_balance -= numAmount;

    // تحديث إجمالي المشتريات
    updatedCashbox.purchases_total += numAmount;

    console.log(`[CASHBOX-UTILS] بعد التحديث - الرصيد: ${updatedCashbox.current_balance}, المشتريات: ${updatedCashbox.purchases_total}`);
  } else if (type === 'return') {
    // في حالة الإرجاع
    console.log(`[CASHBOX-UTILS] معاملة إرجاع - المبلغ: ${numAmount}`);

    // تحديث الرصيد الحالي بخصم المبلغ المرجع (لأننا نعيد المال للعميل)
    updatedCashbox.current_balance -= numAmount;

    // تقليل إجمالي المبيعات بالمبلغ المرجع
    updatedCashbox.sales_total -= numAmount;

    // زيادة إجمالي المرتجعات
    updatedCashbox.returns_total = (updatedCashbox.returns_total || 0) + numAmount;

    // تحديث إجمالي الأرباح (خصم الربح المرتبط بالمبيعات المرتجعة)
    // نفترض أن الربح كان 20% من قيمة المبيعات إذا لم يتم تحديده
    const returnProfit = numProfit > 0 ? numProfit : (numAmount * 0.2);
    updatedCashbox.profit_total = Math.max(0, updatedCashbox.profit_total - returnProfit);

    console.log(`[CASHBOX-UTILS] بعد التحديث - الرصيد: ${updatedCashbox.current_balance}, المبيعات: ${updatedCashbox.sales_total}, المرتجعات: ${updatedCashbox.returns_total}, الأرباح: ${updatedCashbox.profit_total}`);
  }

  return updatedCashbox;
};

/**
 * إنشاء خزينة جديدة
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - الخزينة الجديدة
 */
const createNewCashbox = (initialBalance = 0) => {
  const numInitialBalance = Number(initialBalance) || 0;

  return {
    id: 1,
    initial_balance: numInitialBalance,
    current_balance: numInitialBalance,
    profit_total: 0,
    sales_total: 0,
    purchases_total: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    exists: true
  };
};

/**
 * إصلاح قيم الخزينة السالبة
 * @param {Object} cashbox - الخزينة الحالية
 * @returns {Object} - الخزينة المصححة
 */
const fixNegativeCashboxValues = (cashbox) => {
  if (!cashbox) {
    console.log('[CASHBOX-UTILS] خطأ: الخزينة غير موجودة');
    return cashbox;
  }

  console.log('[CASHBOX-UTILS] إصلاح القيم السالبة في الخزينة');
  console.log('[CASHBOX-UTILS] قيم الخزينة قبل الإصلاح:', {
    initial_balance: cashbox.initial_balance,
    current_balance: cashbox.current_balance,
    profit_total: cashbox.profit_total,
    sales_total: cashbox.sales_total,
    purchases_total: cashbox.purchases_total,
    returns_total: cashbox.returns_total
  });

  // نسخة جديدة من الخزينة
  const fixedCashbox = { ...cashbox };

  // تصحيح القيم السالبة
  fixedCashbox.profit_total = Math.max(0, Number(fixedCashbox.profit_total) || 0);
  fixedCashbox.sales_total = Math.max(0, Number(fixedCashbox.sales_total) || 0);
  fixedCashbox.purchases_total = Math.max(0, Number(fixedCashbox.purchases_total) || 0);
  fixedCashbox.returns_total = Math.max(0, Number(fixedCashbox.returns_total) || 0);
  fixedCashbox.current_balance = Math.max(0, Number(fixedCashbox.current_balance) || 0);
  fixedCashbox.initial_balance = Math.max(0, Number(fixedCashbox.initial_balance) || 0);

  console.log('[CASHBOX-UTILS] قيم الخزينة بعد الإصلاح:', {
    initial_balance: fixedCashbox.initial_balance,
    current_balance: fixedCashbox.current_balance,
    profit_total: fixedCashbox.profit_total,
    sales_total: fixedCashbox.sales_total,
    purchases_total: fixedCashbox.purchases_total,
    returns_total: fixedCashbox.returns_total
  });

  return fixedCashbox;
};

/**
 * إعادة حساب الأرباح بناءً على الرصيد الافتتاحي والرصيد الحالي
 *
 * هذه الدالة تستخدم عند تغيير الرصيد الافتتاحي لإعادة حساب الأرباح بشكل صحيح
 *
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} salesTotal - إجمالي المبيعات
 * @param {number} purchasesTotal - إجمالي المشتريات
 * @returns {Object} - نتائج إعادة الحساب
 */
const recalculateProfitAfterInitialBalanceChange = (initialBalance, currentBalance, salesTotal, purchasesTotal = 0) => {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numInitialBalance = Number(initialBalance) || 0;
  const numCurrentBalance = Number(currentBalance) || 0;
  const numSalesTotal = Number(salesTotal) || 0;
  const numPurchasesTotal = Number(purchasesTotal) || 0;

  console.log('[CASHBOX-UTILS] إعادة حساب الأرباح بعد تغيير الرصيد الافتتاحي');
  console.log('[CASHBOX-UTILS] القيم المستخدمة في الحساب:', {
    initialBalance: numInitialBalance,
    currentBalance: numCurrentBalance,
    salesTotal: numSalesTotal,
    purchasesTotal: numPurchasesTotal
  });

  // الرصيد الحالي يبقى كما هو
  const newCurrentBalance = numCurrentBalance;

  // حساب الربح الإجمالي الجديد بطريقة أكثر دقة
  // الربح = إجمالي المبيعات - إجمالي المشتريات
  const newProfit = numSalesTotal - numPurchasesTotal;

  console.log('[CASHBOX-UTILS] نتائج إعادة الحساب:', {
    newCurrentBalance,
    newProfit: Math.max(0, newProfit)
  });

  return {
    newCurrentBalance,
    excessToProfit: 0,
    newProfit: Math.max(0, newProfit)  // التأكد من أن الربح لا يكون سالباً
  };
};

// تصدير الوظائف بصيغة CommonJS
module.exports = {
  calculateBalanceDistribution,
  updateCashboxAfterTransaction,
  createNewCashbox,
  fixNegativeCashboxValues,
  recalculateProfitAfterInitialBalanceChange
};
