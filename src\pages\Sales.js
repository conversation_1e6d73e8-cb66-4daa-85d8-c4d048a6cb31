import React, { useState, useEffect } from 'react';
import { FaPlus, FaSearch, FaExclamationTriangle, FaPrint } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import InvoicePrinter from '../components/InvoicePrinter';

const Sales = () => {
  // استخدام سياق التطبيق
  const { inventory, transactions, loading, addTransaction } = useApp();

  // حالة البحث والفلترة
  // Variable no utilizada - considere eliminarla o usarla
  const [searchTerm, setSearchTerm] = useState('');
  // Variable no utilizada - considere eliminarla o usarla
  const [filteredInventory, setFilteredInventory] = useState([]);

  // حالة النافذة المنبثقة
  // Variable no utilizada - considere eliminarla o usarla
  const [showModal, setShowModal] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [selectedItem, setSelectedItem] = useState(null);

  // حالة النموذج
  // Variable no utilizada - considere eliminarla o usarla
  const [formData, setFormData] = useState({
    quantity: 1,
    price: 0,
    notes: ''
  });

  // حالة التنبيهات
  // Variable no utilizada - considere eliminarla o usarla
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });

  // الحصول على معاملات البيع
  const sales = transactions.filter(t => t.transaction_type === 'sale');

  // تصفية المخزون بناءً على مصطلح البحث
  useEffect(() => {
    if (inventory && inventory.length > 0) {
      setFilteredInventory(
        inventory.filter(item =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredInventory([]);
    }
  }, [inventory, searchTerm]);

  // معالجة تغيير قيم النموذج
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'quantity' || name === 'price' ? parseFloat(value) || 0 : value
    });
  };

  // معالجة اختيار صنف
  const handleItemSelect = (item) => {
    console.log('Selected item for sale:', item);
    // استخدام سعر البيع المحدث من المخزون
    // item هنا يأتي من المخزون مباشرة، لذا يحتوي على أحدث سعر بيع
    setSelectedItem(item);

    // التأكد من أن سعر البيع موجود وأكبر من صفر
    const sellingPrice = typeof item.selling_price === 'number' && item.selling_price > 0
      ? item.selling_price
      : 0;

    console.log('Setting selling price:', sellingPrice);

    setFormData({
      ...formData,
      price: sellingPrice
    });
    setShowModal(true);
  };

  // معالجة إرسال النموذج
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted', formData);

    if (!selectedItem) {
      showAlert('danger', 'الرجاء اختيار صنف');
      return;
    }

    if (formData.quantity <= 0) {
      showAlert('danger', 'الرجاء إدخال كمية صحيحة');
      return;
    }

    if (formData.price <= 0) {
      showAlert('danger', 'الرجاء إدخال سعر صحيح');
      return;
    }

    if (formData.quantity > selectedItem.current_quantity) {
      showAlert('danger', 'الكمية المطلوبة أكبر من الكمية المتوفرة');
      return;
    }

    try {
      // حساب السعر الإجمالي والربح
      const totalPrice = formData.quantity * formData.price;
      const costPrice = selectedItem.avg_price * formData.quantity;
      const profit = totalPrice - costPrice;

      // إنشاء معاملة بيع جديدة
      const newSale = {
        transaction_type: 'sale',
        item_id: selectedItem._id || selectedItem.id || selectedItem.item_id, // استخدام أي معرف متاح
        item_name: selectedItem.name,
        quantity: formData.quantity,
        price: formData.price,
        total_price: totalPrice,
        profit: profit,
        notes: formData.notes
      };

      console.log('New sale transaction:', newSale);

      console.log('New sale:', newSale);

      // إضافة المعاملة باستخدام وظيفة السياق
      addTransaction(newSale);

      // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
      setShowModal(false);
      setFormData({
        quantity: 1,
        price: 0,
        notes: ''
      });

      // عرض رسالة نجاح
      showAlert('success', 'تم تسجيل عملية البيع بنجاح');
    } catch (err) {
      console.error('Error adding sale:', err);
      showAlert('danger', 'فشل في تسجيل عملية البيع');
    }
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });

    // إخفاء التنبيه بعد 3 ثوان
    setTimeout(() => {
      setAlert({ show: false, type: '', message: '' });
    }, 3000);
  };

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  // التحقق من صلاحيات المستخدم
  const currentUserRole = localStorage.getItem('currentUserRole');
  const isViewer = currentUserRole === 'viewer';
  const isAdmin = currentUserRole === 'admin';

  // المدير لا يستطيع إجراء عمليات البيع
  if (isAdmin) {
    return (
      <div className="alert alert-info">
        <h4>تنبيه</h4>
        <p>المدير لا يمكنه إجراء عمليات البيع. يرجى تسجيل الدخول كموظف لإجراء عمليات البيع.</p>
      </div>
    );
  }

  return (
    <div>
      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      {/* قائمة المخزون */}
      <div className="card mb-4">
        <div className="card-header text-white d-flex justify-content-between align-items-center" style={{ backgroundColor: 'var(--accent-color)' }}>
          <h3 className="card-title mb-0">المبيعات - المخزون المتاح</h3>
          <div className="input-group" style={{ maxWidth: '300px' }}>
            <div className="input-group-prepend">
              <span className="input-group-text">
                <FaSearch />
              </span>
            </div>
            <input
              type="text"
              className="form-control"
              placeholder="بحث عن صنف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="thead-dark">
                <tr>
                  <th>#</th>
                  <th>الصنف</th>
                  <th>وحدة القياس</th>
                  <th>الكمية المتوفرة</th>
                  <th>سعر البيع</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredInventory.length > 0 ? (
                  filteredInventory.map((item, index) => (
                    <tr key={item.id} className={item.current_quantity <= item.minimum_quantity ? 'table-warning' : ''}>
                      <td>{index + 1}</td>
                      <td>
                        <strong>{item.name}</strong>
                      </td>
                      <td>{item.unit || '-'}</td>
                      <td>
                        {item.current_quantity === 0 ? (
                          <span className="badge badge-danger">نفذت الكمية</span>
                        ) : item.current_quantity <= item.minimum_quantity ? (
                          <span className="badge" style={{ backgroundColor: 'var(--accent-color)', color: 'white' }}>
                            <FaExclamationTriangle className="ml-1" />
                            {item.current_quantity}
                          </span>
                        ) : (
                          <span className="badge" style={{ backgroundColor: 'var(--primary-color)', color: 'white' }}>{item.current_quantity}</span>
                        )}
                      </td>
                      <td className="text-nowrap">{item.selling_price ? item.selling_price.toFixed(2) + ' د ل' : '-'}</td>
                      <td>
                        {!isViewer && (
                          <button
                            className="btn btn-sm"
                            style={{ backgroundColor: 'var(--accent-color)', color: 'white' }}
                            onClick={() => handleItemSelect(item)}
                            disabled={item.current_quantity === 0}
                          >
                            <FaPlus className="ml-1" />
                            بيع
                          </button>
                        )}
                        {isViewer && (
                          <span className="text-muted">غير مسموح</span>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="text-center py-4">
                      {searchTerm ? (
                        <div className="alert alert-warning">
                          <FaSearch className="ml-2" />
                          لا توجد نتائج للبحث عن "{searchTerm}"
                        </div>
                      ) : (
                        <div className="alert alert-info">
                          <FaPlus className="ml-2" />
                          لا توجد أصناف مسجلة. قم بإضافة أصناف أولاً.
                        </div>
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* آخر عمليات البيع */}
      <div className="card">
        <div className="card-header text-white" style={{ backgroundColor: 'var(--accent-color)' }}>
          <h3 className="card-title mb-0">آخر عمليات البيع</h3>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="thead-light">
                <tr>
                  <th>التاريخ</th>
                  <th>رقم الفاتورة</th>
                  <th>الصنف</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>الإجمالي</th>
                  <th>ملاحظات</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {sales.length > 0 ? (
                  sales.map((sale) => (
                    <tr key={sale.id}>
                      <td>{formatDate(sale.transaction_date)}</td>
                      <td>{sale.invoice_number || '-'}</td>
                      <td><strong>{sale.item_name}</strong></td>
                      <td>{sale.quantity}</td>
                      <td className="text-nowrap">{sale.price.toFixed(2)} د ل</td>
                      <td className="text-nowrap font-weight-bold">{sale.total_price.toFixed(2)} د ل</td>
                      <td>
                        {sale.notes ? (
                          <span title={sale.notes}>
                            {sale.notes.length > 20 ? sale.notes.substring(0, 20) + '...' : sale.notes}
                          </span>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td>
                        <InvoicePrinter
                          sale={{
                            ...sale,
                            // استخدام رقم الفاتورة الموجود إذا كان متاحًا، وإلا استخدام الصيغة الجديدة
                            invoice_number: sale.invoice_number || `H${sale.customer_id?.toString().padStart(5, '0')}` || `H${sale.id.toString().padStart(5, '0')}`,
                            paid_amount: sale.total_price,
                            remaining_amount: 0
                          }}
                          companyInfo={{
                            name: 'شركة اتش قروب',
                            address: 'للتصميم و تصنيع الأثاث والديكورات',
                            logo: window.appSettings && window.appSettings.logoUrl
                          }}
                        />
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      <div className="alert alert-info">
                        لا توجد عمليات بيع مسجلة بعد
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* نافذة إضافة عملية بيع */}
      {showModal && selectedItem && (
        <div className="modal-backdrop">
          <div className="modal-container">
            <div className="modal-header">
              <h3>
                تسجيل عملية بيع - {selectedItem.name}
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowModal(false)}
              >
                &times;
              </button>
            </div>

            <div className="modal-body">
              <form onSubmit={handleSubmit}>
                <div className="alert alert-info">
                  <div className="d-flex justify-content-between">
                    <div>
                      <strong>الصنف:</strong> {selectedItem.name}
                    </div>
                    <div>
                      <strong>وحدة القياس:</strong> {selectedItem.unit}
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group col-md-6">
                    <label className="form-label">الكمية المتوفرة</label>
                    <div className="input-group">
                      <div className="form-control bg-light">
                        {selectedItem.current_quantity}
                      </div>
                      <div className="input-group-append">
                        <span className="input-group-text">{selectedItem.unit}</span>
                      </div>
                    </div>
                    {selectedItem.current_quantity <= selectedItem.minimum_quantity && (
                      <small className="text-danger">
                        <FaExclamationTriangle className="ml-1" />
                        الكمية منخفضة وتحتاج إلى إعادة طلب
                      </small>
                    )}
                  </div>

                  <div className="form-group col-md-6">
                    <label className="form-label">الكمية المطلوبة *</label>
                    <div className="input-group">
                      <input
                        type="number"
                        name="quantity"
                        className="form-control"
                        value={formData.quantity}
                        onChange={handleInputChange}
                        min="1"
                        max={selectedItem.current_quantity}
                        required
                      />
                      <div className="input-group-append">
                        <span className="input-group-text">{selectedItem.unit}</span>
                      </div>
                    </div>
                    <small className="form-text text-muted">
                      الحد الأقصى: {selectedItem.current_quantity} {selectedItem.unit}
                    </small>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group col-md-6">
                    <label className="form-label">سعر البيع (د ل) *</label>
                    <div className="input-group">
                      <input
                        type="number"
                        name="price"
                        className="form-control"
                        value={formData.price}
                        onChange={handleInputChange}
                        min="0.01"
                        step="0.01"
                        required
                      />
                      <div className="input-group-append">
                        <span className="input-group-text">د ل</span>
                      </div>
                    </div>
                    <small className="form-text text-muted">
                      سعر الشراء: {selectedItem.avg_price.toFixed(2)} د ل
                    </small>
                  </div>

                  <div className="form-group col-md-6">
                    <label className="form-label">الإجمالي</label>
                    <div className="input-group">
                      <div className="form-control bg-light font-weight-bold">
                        {(formData.quantity * formData.price).toFixed(2)}
                      </div>
                      <div className="input-group-append">
                        <span className="input-group-text">د ل</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group col-md-12">
                    <label className="form-label">تاريخ البيع</label>
                    <input
                      type="date"
                      name="transaction_date"
                      className="form-control"
                      value={formData.transaction_date || new Date().toISOString().split('T')[0]}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">ملاحظات</label>
                  <textarea
                    name="notes"
                    className="form-control"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows="2"
                    placeholder="ملاحظات إضافية (اختياري)"
                  ></textarea>
                </div>

                <div className="card bg-light mb-3">
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h5 className="card-title mb-0">ملخص العملية</h5>
                      </div>
                      <div className="font-weight-bold" style={{ color: 'var(--accent-color)' }}>
                        الربح المتوقع: {((formData.price * formData.quantity) - (selectedItem.avg_price * formData.quantity)).toFixed(2)} د ل
                      </div>
                    </div>
                  </div>
                </div>

                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowModal(false)}
                  >
                    إلغاء
                  </button>
                  <button type="submit" className="btn" style={{ backgroundColor: 'var(--accent-color)', color: 'white' }}>
                    تسجيل عملية البيع
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sales;
