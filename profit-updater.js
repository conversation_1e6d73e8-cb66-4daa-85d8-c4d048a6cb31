/**
 * وحدة تحديث قيم الربح
 * هذا الملف يحتوي على وظائف لتحديث قيم الربح في قاعدة البيانات
 */

// استيراد المكتبات اللازمة
const { logSystem, logError } = require('./utils/logger');
const { calculateProfit } = require('./utils/profitCalculator');
const eventSystem = require('./event-system');

// متغير لتخزين اتصال قاعدة البيانات
let db = null;

/**
 * تهيئة وحدة تحديث قيم الربح
 * @param {Object} database - اتصال قاعدة البيانات
 */
function init(database) {
  db = database;
  logSystem('تم تهيئة وحدة تحديث قيم الربح', 'info');
}

/**
 * تحديث قيم الربح لجميع معاملات البيع
 * @returns {Object} - نتيجة التحديث
 */
function updateProfitValues() {
  try {
    logSystem('بدء تحديث قيم الربح...', 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في updateProfitValues', 'error');
      const DatabaseManager = require('./database-singleton');
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        logSystem('تم إعادة الاتصال بقاعدة البيانات في updateProfitValues', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // الحصول على جميع معاملات البيع
    const salesStmt = db.prepare(`
      SELECT t.id, t.item_id, t.item_name, t.quantity, t.selling_price, t.profit,
             i.avg_price, t.total_price, t.customer_id
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
    `);

    const sales = salesStmt.all();
    logSystem(`تم العثور على ${sales.length} معاملة بيع للتحديث`, 'info');

    // تحضير استعلام تحديث الربح
    const updateStmt = db.prepare(`
      UPDATE transactions
      SET profit = ?
      WHERE id = ?
    `);

    // تحضير استعلام تحديث سجل مبيعات العملاء
    const updateCustomerHistoryStmt = db.prepare(`
      UPDATE customer_sales_history
      SET profit = ?
      WHERE transaction_id = ?
    `);

    // بدء معاملة قاعدة البيانات
    const updateTransaction = db.transaction(() => {
      let updatedCount = 0;
      let unchangedCount = 0;
      let updatedHistoryCount = 0;
      let totalProfit = 0;

      for (const sale of sales) {
        // حساب الربح
        let profit;
        if (sale.avg_price > 0) {
          // حساب الربح الفعلي بدون استخدام Math.abs
          profit = calculateProfit(sale.selling_price, sale.avg_price, sale.quantity);
        } else {
          // استخدام 20% من سعر البيع كتكلفة تقديرية إذا كان متوسط سعر الشراء غير متوفر
          profit = sale.selling_price * sale.quantity * 0.2;
        }

        // تحديث قيمة الربح في جدول المعاملات إذا كانت مختلفة
        if (Math.abs(profit - (sale.profit || 0)) > 0.01) {
          updateStmt.run(profit, sale.id);
          logSystem(`تم تحديث الربح للمعاملة ${sale.id} (${sale.item_name}): من ${sale.profit || 0} إلى ${profit}`, 'info');
          updatedCount++;

          // تحديث سجل مبيعات العملاء إذا كان هناك عميل
          if (sale.customer_id) {
            try {
              updateCustomerHistoryStmt.run(profit, sale.id);
              updatedHistoryCount++;
            } catch (historyError) {
              logError(historyError, 'updateProfitValues - updateCustomerHistory');
            }
          }
        } else {
          unchangedCount++;
        }

        totalProfit += profit;
      }

      return {
        updatedCount,
        unchangedCount,
        updatedHistoryCount,
        totalProfit
      };
    });

    // تنفيذ المعاملة
    const updateResult = updateTransaction();

    // إرسال إشعار بتحديث قيم الربح
    eventSystem.notifyProfitsUpdated({
      updatedCount: updateResult.updatedCount,
      totalProfit: updateResult.totalProfit,
      timestamp: new Date().toISOString()
    });

    logSystem(`تم تحديث ${updateResult.updatedCount} معاملة بيع`, 'info');
    logSystem(`لم يتم تغيير ${updateResult.unchangedCount} معاملة بيع`, 'info');
    logSystem(`تم تحديث ${updateResult.updatedHistoryCount} سجل مبيعات للعملاء`, 'info');
    logSystem(`إجمالي الربح: ${updateResult.totalProfit}`, 'info');

    return {
      success: true,
      ...updateResult
    };
  } catch (error) {
    logError(error, 'updateProfitValues');
    return {
      success: false,
      error: error.message || 'حدث خطأ أثناء تحديث قيم الربح'
    };
  }
}

// تصدير الوظائف
module.exports = {
  init,
  updateProfitValues
};
