/**
 * ملف ipc-handlers.js
 * يقوم بتسجيل جميع معالجات IPC في مكان واحد
 *
 * ملاحظات هامة:
 * 1. يجب تسجيل كل معالج IPC مرة واحدة فقط لتجنب أخطاء "Attempted to register a second handler"
 * 2. يجب تسجيل معالجات كل وحدة في الدالة المخصصة لها (مثل registerItemsHandlers للأصناف)
 * 3. تجنب تسجيل نفس المعالج في أكثر من دالة
 */

const { ipcMain } = require('electron');
const { logError, logSystem } = require('./error-handler');

// استيراد مديري النظام
const itemsManager = require('./items-manager');
const inventoryManager = require('./inventory-manager');
const customersManager = require('./customers-manager');
const transactionManager = require('./unified-transaction-manager');
const returnTransactionsManager = require('./return-transactions-manager');
const cashboxManager = require('./cashbox-manager');
const reportsManager = require('./reports-manager');

// استيراد معالجات إضافية
const missingHandlers = require('./missing-handlers');

/**
 * تسجيل جميع معالجات IPC
 * @returns {boolean} - نجاح العملية
 */
function registerAllHandlers() {
  try {
    logSystem('جاري تسجيل معالجات IPC...', 'info');

    // محاولة تسجيل كل مجموعة من المعالجات بشكل منفصل
    // للتمكن من الاستمرار حتى لو فشلت إحدى المجموعات

    try {
      // تسجيل معالجات الأصناف
      registerItemsHandlers();
      logSystem('تم تسجيل معالجات الأصناف بنجاح', 'info');
    } catch (itemsError) {
      logError(itemsError, 'registerItemsHandlers');
      logSystem('فشل في تسجيل بعض معالجات الأصناف', 'warning');
    }

    try {
      // تسجيل معالجات المخزون
      registerInventoryHandlers();
      logSystem('تم تسجيل معالجات المخزون بنجاح', 'info');
    } catch (inventoryError) {
      logError(inventoryError, 'registerInventoryHandlers');
      logSystem('فشل في تسجيل بعض معالجات المخزون', 'warning');
    }

    try {
      // تسجيل معالجات العملاء
      registerCustomersHandlers();
      logSystem('تم تسجيل معالجات العملاء بنجاح', 'info');
    } catch (customersError) {
      logError(customersError, 'registerCustomersHandlers');
      logSystem('فشل في تسجيل بعض معالجات العملاء', 'warning');
    }

    try {
      // تسجيل معالجات المعاملات
      registerTransactionsHandlers();
      logSystem('تم تسجيل معالجات المعاملات بنجاح', 'info');
    } catch (transactionsError) {
      logError(transactionsError, 'registerTransactionsHandlers');
      logSystem('فشل في تسجيل بعض معالجات المعاملات', 'warning');
    }

    try {
      // تسجيل معالجات عمليات الإرجاع
      registerReturnTransactionsHandlers();
      logSystem('تم تسجيل معالجات عمليات الإرجاع بنجاح', 'info');
    } catch (returnTransactionsError) {
      logError(returnTransactionsError, 'registerReturnTransactionsHandlers');
      logSystem('فشل في تسجيل بعض معالجات عمليات الإرجاع', 'warning');
    }

    try {
      // تسجيل معالجات الخزينة
      registerCashboxHandlers();
      logSystem('تم تسجيل معالجات الخزينة بنجاح', 'info');
    } catch (cashboxError) {
      logError(cashboxError, 'registerCashboxHandlers');
      logSystem('فشل في تسجيل بعض معالجات الخزينة', 'warning');
    }

    try {
      // تسجيل معالجات التقارير
      registerReportsHandlers();
      logSystem('تم تسجيل معالجات التقارير بنجاح', 'info');
    } catch (reportsError) {
      logError(reportsError, 'registerReportsHandlers');
      logSystem('فشل في تسجيل بعض معالجات التقارير', 'warning');
    }

    try {
      // تسجيل معالجات تسجيل الأخطاء
      registerLogHandlers();
      logSystem('تم تسجيل معالجات تسجيل الأخطاء بنجاح', 'info');
    } catch (logError) {
      logError(logError, 'registerLogHandlers');
      logSystem('فشل في تسجيل بعض معالجات تسجيل الأخطاء', 'warning');
    }

    try {
      // تسجيل معالجات الإعدادات
      registerSettingsHandlers();
      logSystem('تم تسجيل معالجات الإعدادات بنجاح', 'info');
    } catch (settingsError) {
      logError(settingsError, 'registerSettingsHandlers');
      logSystem('فشل في تسجيل بعض معالجات الإعدادات', 'warning');
    }

    try {
      // تسجيل معالجات إضافية
      registerAdditionalHandlers();
      logSystem('تم تسجيل معالجات إضافية بنجاح', 'info');
    } catch (additionalError) {
      logError(additionalError, 'registerAdditionalHandlers');
      logSystem('فشل في تسجيل بعض المعالجات الإضافية', 'warning');
    }

    // تسجيل المعالجات المفقودة بشكل آمن مع التحقق من وجودها
    try {
      const missingHandlers = require('./missing-handlers');
      const missingHandlersResult = missingHandlers.registerMissingHandlers();
      logSystem(`نتيجة تسجيل المعالجات المفقودة: ${missingHandlersResult}`, 'info');
    } catch (missingError) {
      logError(missingError, 'registerMissingHandlers');
      logSystem('فشل في تسجيل المعالجات المفقودة', 'warning');
    }

    logSystem('تم تسجيل معالجات IPC بنجاح', 'info');
    return true;
  } catch (error) {
    logError(error, 'registerAllHandlers');
    return false;
  }
}

/**
 * تسجيل معالجات إضافية
 */
function registerAdditionalHandlers() {
  // معالج للحصول على المستخدمين
  ipcMain.handle('get-users', async () => {
    try {
      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // استعلام SQL للحصول على المستخدمين
      const stmt = db.prepare('SELECT id, username, full_name, role, created_at FROM users ORDER BY username ASC');
      const users = stmt.all();

      // تحويل النتائج إلى التنسيق المطلوب
      const processedUsers = users.map(user => ({
        ...user,
        _id: user.id.toString() // إضافة حقل _id للتوافق مع الواجهة الأمامية
      }));

      return processedUsers;
    } catch (error) {
      logError(error, 'get-users');
      return [];
    }
  });

  // معالج لإضافة مستخدم جديد
  ipcMain.handle('add-user', async (event, user) => {
    try {
      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();
      const bcrypt = require('bcryptjs');

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود المستخدم
      const checkStmt = db.prepare('SELECT * FROM users WHERE username = ?');
      const existingUser = checkStmt.get(user.username);

      if (existingUser) {
        throw new Error('اسم المستخدم موجود بالفعل');
      }

      // تشفير كلمة المرور
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(user.password, salt);

      // إضافة المستخدم الجديد
      const insertStmt = db.prepare(`
        INSERT INTO users (username, password, full_name, role, created_at)
        VALUES (?, ?, ?, ?, ?)
      `);

      const now = new Date().toISOString();
      const result = insertStmt.run(
        user.username,
        hashedPassword,
        user.full_name || '',
        user.role || 'employee',
        now
      );

      // الحصول على المستخدم المضاف
      const getUserStmt = db.prepare('SELECT id, username, full_name, role, created_at FROM users WHERE id = ?');
      const newUser = getUserStmt.get(result.lastInsertRowid);

      // إضافة حقل _id للتوافق مع الواجهة الأمامية
      return {
        ...newUser,
        _id: newUser.id.toString()
      };
    } catch (error) {
      logError(error, 'add-user');
      throw error;
    }
  });

  // معالج لتحديث مستخدم
  ipcMain.handle('update-user', async (event, user) => {
    try {
      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();
      const bcrypt = require('bcryptjs');

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود المستخدم
      const checkStmt = db.prepare('SELECT * FROM users WHERE id = ?');
      const existingUser = checkStmt.get(user.id);

      if (!existingUser) {
        throw new Error('المستخدم غير موجود');
      }

      // تحديث المستخدم
      let updateQuery = 'UPDATE users SET full_name = ?, role = ? WHERE id = ?';
      let params = [user.full_name || '', user.role || 'employee', user.id];

      // إذا تم توفير كلمة مرور جديدة، قم بتشفيرها وتحديثها
      if (user.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(user.password, salt);
        updateQuery = 'UPDATE users SET full_name = ?, role = ?, password = ? WHERE id = ?';
        params = [user.full_name || '', user.role || 'employee', hashedPassword, user.id];
      }

      const updateStmt = db.prepare(updateQuery);
      updateStmt.run(...params);

      // الحصول على المستخدم المحدث
      const getUserStmt = db.prepare('SELECT id, username, full_name, role, created_at FROM users WHERE id = ?');
      const updatedUser = getUserStmt.get(user.id);

      // إضافة حقل _id للتوافق مع الواجهة الأمامية
      return {
        ...updatedUser,
        _id: updatedUser.id.toString()
      };
    } catch (error) {
      logError(error, 'update-user');
      throw error;
    }
  });

  // معالج لحذف مستخدم
  ipcMain.handle('delete-user', async (event, id) => {
    try {
      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود المستخدم
      const checkStmt = db.prepare('SELECT * FROM users WHERE id = ?');
      const existingUser = checkStmt.get(id);

      if (!existingUser) {
        throw new Error('المستخدم غير موجود');
      }

      // التحقق من عدم حذف المستخدم الوحيد بدور مدير
      const adminCountStmt = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ? AND id != ?');
      const { count } = adminCountStmt.get('admin', id);

      if (existingUser.role === 'admin' && count === 0) {
        throw new Error('لا يمكن حذف المستخدم الوحيد بدور مدير');
      }

      // حذف المستخدم
      const deleteStmt = db.prepare('DELETE FROM users WHERE id = ?');
      deleteStmt.run(id);

      return {
        success: true,
        message: 'تم حذف المستخدم بنجاح',
        id
      };
    } catch (error) {
      logError(error, 'delete-user');
      throw error;
    }
  });

  // معالج لتسجيل الدخول
  ipcMain.handle('login', async (event, credentials) => {
    try {
      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();
      const bcrypt = require('bcryptjs');

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود المستخدم
      const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
      const user = stmt.get(credentials.username);

      if (!user) {
        return {
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
        };
      }

      // التحقق من كلمة المرور
      const isMatch = await bcrypt.compare(credentials.password, user.password);

      if (!isMatch) {
        return {
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
        };
      }

      // إرجاع بيانات المستخدم بدون كلمة المرور
      return {
        success: true,
        user: {
          id: user.id,
          _id: user.id.toString(),
          username: user.username,
          full_name: user.full_name,
          role: user.role,
          created_at: user.created_at
        }
      };
    } catch (error) {
      logError(error, 'login');
      return {
        success: false,
        message: 'حدث خطأ أثناء تسجيل الدخول'
      };
    }
  });

  // معالج للحصول على الآلات
  ipcMain.handle('get-machines', async () => {
    try {
      logSystem('الحصول على جميع الآلات', 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود جدول machines
      const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='machines'");
      const tableExists = tableCheckStmt.get();

      if (!tableExists) {
        logSystem('جدول machines غير موجود، جاري إنشاؤه...', 'info');

        // إنشاء جدول machines
        db.exec(`
          CREATE TABLE IF NOT EXISTS machines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            purchase_date TEXT NOT NULL,
            purchase_price REAL NOT NULL,
            current_value REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // إنشاء فهرس للاسم
        db.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

        logSystem('تم إنشاء جدول machines بنجاح', 'info');
        return [];
      }

      // الحصول على جميع الآلات
      const stmt = db.prepare('SELECT * FROM machines ORDER BY name');
      const machines = stmt.all();

      // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
      const processedMachines = machines.map(machine => ({
        ...machine,
        _id: machine.id.toString(),
        id: machine.id.toString(),
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0)
      }));

      return processedMachines;
    } catch (error) {
      logError(error, 'get-machines');
      return [];
    }
  });

  // معالج للحصول على الآلات بالتحميل التدريجي
  ipcMain.handle('get-machines-paginated', async (event, { page, limit, searchTerm }) => {
    try {
      logSystem(`الحصول على الآلات (صفحة: ${page}, حد: ${limit}, بحث: ${searchTerm || 'لا يوجد'})`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود جدول machines
      const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='machines'");
      const tableExists = tableCheckStmt.get();

      if (!tableExists) {
        logSystem('جدول machines غير موجود، جاري إنشاؤه...', 'info');

        // إنشاء جدول machines
        db.exec(`
          CREATE TABLE IF NOT EXISTS machines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            purchase_date TEXT NOT NULL,
            purchase_price REAL NOT NULL,
            current_value REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // إنشاء فهرس للاسم
        db.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

        logSystem('تم إنشاء جدول machines بنجاح', 'info');
        return { machines: [], total: 0 };
      }

      // حساب إجمالي عدد الآلات
      let countQuery = 'SELECT COUNT(*) as total FROM machines';
      let countParams = [];

      if (searchTerm) {
        countQuery += ' WHERE name LIKE ?';
        countParams.push(`%${searchTerm}%`);
      }

      const countStmt = db.prepare(countQuery);
      const { total } = countStmt.get(...countParams);

      // الحصول على الآلات
      let query = 'SELECT * FROM machines';
      let params = [];

      if (searchTerm) {
        query += ' WHERE name LIKE ?';
        params.push(`%${searchTerm}%`);
      }

      query += ' ORDER BY name';

      if (limit > 0) {
        query += ' LIMIT ? OFFSET ?';
        params.push(limit, (page - 1) * limit);
      }

      const stmt = db.prepare(query);
      const machines = stmt.all(...params);

      // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
      const processedMachines = machines.map(machine => ({
        ...machine,
        _id: machine.id.toString(),
        id: machine.id.toString(),
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0)
      }));

      return { machines: processedMachines, total };
    } catch (error) {
      logError(error, 'get-machines-paginated');
      return { machines: [], total: 0 };
    }
  });

  // معالج لإضافة آلة جديدة
  ipcMain.handle('add-machine', async (event, machine) => {
    try {
      logSystem(`إضافة آلة جديدة: ${machine.name}`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود جدول machines
      const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='machines'");
      const tableExists = tableCheckStmt.get();

      if (!tableExists) {
        logSystem('جدول machines غير موجود، جاري إنشاؤه...', 'info');

        // إنشاء جدول machines
        db.exec(`
          CREATE TABLE IF NOT EXISTS machines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            purchase_date TEXT NOT NULL,
            purchase_price REAL NOT NULL,
            current_value REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // إنشاء فهرس للاسم
        db.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

        logSystem('تم إنشاء جدول machines بنجاح', 'info');
      }

      // تنظيف البيانات
      const cleanedMachine = {
        name: machine.name || '',
        purchase_date: machine.purchase_date || new Date().toISOString().split('T')[0],
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0),
        notes: machine.notes || ''
      };

      // إضافة الآلة إلى قاعدة البيانات
      const stmt = db.prepare(`
        INSERT INTO machines (name, purchase_date, purchase_price, current_value, notes)
        VALUES (?, ?, ?, ?, ?)
      `);

      const info = stmt.run(
        cleanedMachine.name,
        cleanedMachine.purchase_date,
        cleanedMachine.purchase_price,
        cleanedMachine.current_value,
        cleanedMachine.notes
      );

      // الحصول على الآلة المضافة
      const newMachine = db.prepare('SELECT * FROM machines WHERE id = ?').get(info.lastInsertRowid);

      // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
      const processedMachine = {
        ...newMachine,
        _id: newMachine.id.toString(),
        id: newMachine.id.toString(),
        purchase_price: Number(newMachine.purchase_price || 0),
        current_value: Number(newMachine.current_value || 0)
      };

      return processedMachine;
    } catch (error) {
      logError(error, 'add-machine');
      throw new Error(`فشل في إضافة الآلة: ${error.message}`);
    }
  });

  // معالج لتحديث آلة
  ipcMain.handle('update-machine', async (event, machine) => {
    try {
      logSystem(`تحديث الآلة: ${machine.name} (المعرف: ${machine.id || machine._id})`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود معرف الآلة
      const machineId = machine.id || machine._id;
      if (!machineId) {
        throw new Error('معرف الآلة غير موجود');
      }

      // تنظيف البيانات
      const cleanedMachine = {
        name: machine.name || '',
        purchase_date: machine.purchase_date || new Date().toISOString().split('T')[0],
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0),
        notes: machine.notes || '',
        updated_at: new Date().toISOString()
      };

      // تحديث الآلة في قاعدة البيانات
      const stmt = db.prepare(`
        UPDATE machines
        SET name = ?, purchase_date = ?, purchase_price = ?, current_value = ?, notes = ?, updated_at = ?
        WHERE id = ?
      `);

      stmt.run(
        cleanedMachine.name,
        cleanedMachine.purchase_date,
        cleanedMachine.purchase_price,
        cleanedMachine.current_value,
        cleanedMachine.notes,
        cleanedMachine.updated_at,
        machineId
      );

      // الحصول على الآلة المحدثة
      const updatedMachine = db.prepare('SELECT * FROM machines WHERE id = ?').get(machineId);

      if (!updatedMachine) {
        throw new Error('لم يتم العثور على الآلة بعد التحديث');
      }

      // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
      const processedMachine = {
        ...updatedMachine,
        _id: updatedMachine.id.toString(),
        id: updatedMachine.id.toString(),
        purchase_price: Number(updatedMachine.purchase_price || 0),
        current_value: Number(updatedMachine.current_value || 0)
      };

      return processedMachine;
    } catch (error) {
      logError(error, 'update-machine');
      throw new Error(`فشل في تحديث الآلة: ${error.message}`);
    }
  });

  // معالج لحذف آلة
  ipcMain.handle('delete-machine', async (event, id) => {
    try {
      logSystem(`حذف الآلة بالمعرف: ${id}`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // التحقق من وجود معرف الآلة
      if (!id) {
        throw new Error('معرف الآلة غير موجود');
      }

      // حذف الآلة من قاعدة البيانات
      const stmt = db.prepare('DELETE FROM machines WHERE id = ?');
      const info = stmt.run(id);

      if (info.changes === 0) {
        throw new Error('لم يتم العثور على الآلة للحذف');
      }

      return { success: true, message: 'تم حذف الآلة بنجاح' };
    } catch (error) {
      logError(error, 'delete-machine');
      throw new Error(`فشل في حذف الآلة: ${error.message}`);
    }
  });

  // معالج لتحديث قيم الربح في قاعدة البيانات
  ipcMain.handle('update-profit-values', async () => {
    try {
      logSystem('بدء تحديث قيم الربح في قاعدة البيانات...', 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // استيراد وظائف حساب الربح
      const { calculateProfit } = require('./src/utils/profitCalculator');

      // الحصول على معاملات البيع
      const salesStmt = db.prepare(`
        SELECT t.id, t.item_id, t.quantity, t.selling_price, t.profit, i.name as item_name,
               inv.avg_price
        FROM transactions t
        JOIN items i ON t.item_id = i.id
        LEFT JOIN inventory inv ON t.item_id = inv.item_id
        WHERE t.transaction_type = 'sale'
      `);
      const sales = salesStmt.all();

      logSystem(`عدد معاملات البيع: ${sales.length}`, 'info');

      // بدء معاملة قاعدة البيانات
      const updateResult = db.transaction(() => {
        const updateStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
        let updatedCount = 0;
        let unchangedCount = 0;
        let totalProfit = 0;

        for (const sale of sales) {
          // حساب الربح
          let profit;
          if (sale.avg_price > 0) {
            // حساب الربح الفعلي بدون استخدام Math.abs
            profit = calculateProfit(sale.selling_price, sale.avg_price, sale.quantity);
          } else {
            // استخدام 50% من سعر البيع كتكلفة تقديرية إذا كان متوسط سعر الشراء غير متوفر
            profit = sale.selling_price * sale.quantity * 0.5;
          }

          // تحديث قيمة الربح في جدول المعاملات إذا كانت مختلفة
          if (Math.max(0, Math.abs(profit - sale.profit)) > 0.01) {
            updateStmt.run(profit, sale.id);
            logSystem(`تم تحديث الربح للمعاملة ${sale.id} (${sale.item_name}): من ${sale.profit} إلى ${profit}`, 'info');
            updatedCount++;
          } else {
            unchangedCount++;
          }

          totalProfit += profit;
        }

        // تحديث إجمالي الربح في جدول الخزينة
        const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const cashbox = cashboxStmt.get();

        if (cashbox) {
          const updateCashboxStmt = db.prepare('UPDATE cashbox SET profit_total = ? WHERE id = ?');
          updateCashboxStmt.run(totalProfit, cashbox.id);
          logSystem(`تم تحديث إجمالي الربح في الخزينة: ${totalProfit}`, 'info');
        }

        // تحديث قيم الربح في سجل مبيعات العملاء
        const historyStmt = db.prepare(`
          SELECT h.id, h.customer_id, h.transaction_id, h.total_profit,
                 t.profit as transaction_profit
          FROM customer_sales_history h
          JOIN transactions t ON h.transaction_id = t.id
          WHERE t.transaction_type = 'sale'
        `);
        const history = historyStmt.all();

        logSystem(`عدد سجلات مبيعات العملاء: ${history.length}`, 'info');

        const updateHistoryStmt = db.prepare('UPDATE customer_sales_history SET total_profit = ? WHERE id = ?');
        let updatedHistoryCount = 0;

        for (const record of history) {
          // تحديث قيمة الربح في سجل مبيعات العملاء إذا كانت مختلفة
          if (Math.max(0, Math.abs(record.transaction_profit - record.total_profit)) > 0.01) {
            updateHistoryStmt.run(Math.max(0, record.transaction_profit), record.id);
            updatedHistoryCount++;
          }
        }

        return {
          updatedCount,
          unchangedCount,
          totalProfit,
          updatedHistoryCount
        };
      })();

      logSystem(`تم تحديث ${updateResult.updatedCount} معاملة بيع`, 'info');
      logSystem(`لم يتم تغيير ${updateResult.unchangedCount} معاملة بيع`, 'info');
      logSystem(`تم تحديث ${updateResult.updatedHistoryCount} سجل مبيعات للعملاء`, 'info');
      logSystem(`إجمالي الربح: ${updateResult.totalProfit}`, 'info');

      return {
        success: true,
        updatedCount: updateResult.updatedCount,
        unchangedCount: updateResult.unchangedCount,
        updatedHistoryCount: updateResult.updatedHistoryCount,
        totalProfit: updateResult.totalProfit
      };
    } catch (error) {
      logError(error, 'update-profit-values');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تحديث قيم الربح'
      };
    }
  });

  // معالج لتحديث قيم الربح
  ipcMain.handle('update-profit-values-all', async () => {
    try {
      logSystem('بدء تحديث جميع قيم الربح...', 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // نحن لا نبحث عن قيم الربح السالبة لأننا نريد الاحتفاظ بها
      // بدلاً من ذلك، نقوم بتحديث جميع قيم الربح
      const countStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE transaction_type = "sale"');
      const { count } = countStmt.get();
      logSystem(`عدد معاملات البيع: ${count}`, 'info');

      if (count === 0) {
        logSystem('لا توجد معاملات بيع، لا حاجة للتحديث', 'info');
        return {
          success: true,
          updatedCount: 0,
          message: 'لا توجد معاملات بيع'
        };
      }

      // الحصول على جميع معاملات البيع
      const salesStmt = db.prepare(`
        SELECT t.id, t.item_id, t.quantity, t.selling_price, t.profit, i.name as item_name
        FROM transactions t
        JOIN items i ON t.item_id = i.id
        WHERE t.transaction_type = 'sale'
      `);
      const sales = salesStmt.all();

      // بدء معاملة قاعدة البيانات
      const updateResult = db.transaction(() => {
        const updateStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
        let updatedCount = 0;
        let totalFixed = 0;

        for (const sale of sales) {
          // لا نقوم بتحويل قيمة الربح السالبة إلى موجبة
          // نحتفظ بالقيمة الفعلية للربح
          const actualProfit = sale.profit;

          // تحديث قيمة الربح في جدول المعاملات
          updateStmt.run(actualProfit, sale.id);
          logSystem(`تم تصحيح الربح للمعاملة ${sale.id} (${sale.item_name}): من ${sale.profit} إلى ${actualProfit}`, 'info');
          updatedCount++;
          totalFixed += actualProfit;
        }

        // تحديث إجمالي الربح في جدول الخزينة
        const totalProfitStmt = db.prepare('SELECT SUM(profit) as total FROM transactions WHERE transaction_type = "sale"');
        const { total: totalProfit } = totalProfitStmt.get();

        const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const cashbox = cashboxStmt.get();

        if (cashbox) {
          const updateCashboxStmt = db.prepare('UPDATE cashbox SET profit_total = ? WHERE id = ?');
          updateCashboxStmt.run(totalProfit || 0, cashbox.id);
          logSystem(`تم تحديث إجمالي الربح في الخزينة: ${totalProfit || 0}`, 'info');
        }

        // تحديث قيم الربح في سجل مبيعات العملاء
        const historyStmt = db.prepare(`
          SELECT h.id, h.transaction_id, h.total_profit,
                 t.profit as transaction_profit
          FROM customer_sales_history h
          JOIN transactions t ON h.transaction_id = t.id
          WHERE t.transaction_type = 'sale'
        `);
        const history = historyStmt.all();

        logSystem(`عدد سجلات مبيعات العملاء: ${history.length}`, 'info');

        const updateHistoryStmt = db.prepare('UPDATE customer_sales_history SET total_profit = ? WHERE id = ?');
        let updatedHistoryCount = 0;

        for (const record of history) {
          // نحتفظ بالقيمة الفعلية للربح
          const actualProfit = record.transaction_profit;

          // تحديث قيمة الربح في سجل مبيعات العملاء
          updateHistoryStmt.run(actualProfit, record.id);
          logSystem(`تم تصحيح الربح لسجل المبيعات ${record.id}: من ${record.total_profit} إلى ${actualProfit}`, 'info');
          updatedHistoryCount++;
        }

        return {
          updatedCount,
          totalFixed,
          updatedHistoryCount
        };
      })();

      logSystem(`تم تحديث ${updateResult.updatedCount} معاملة بيع`, 'info');
      logSystem(`تم تحديث ${updateResult.updatedHistoryCount} سجل مبيعات للعملاء`, 'info');
      logSystem(`إجمالي الربح: ${updateResult.totalFixed}`, 'info');

      return {
        success: true,
        updatedCount: updateResult.updatedCount,
        updatedHistoryCount: updateResult.updatedHistoryCount,
        totalFixed: updateResult.totalFixed
      };
    } catch (error) {
      logError(error, 'fix-negative-profits');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تصحيح قيم الربح السالبة'
      };
    }
  });

  // تم إزالة معالج update-profit-values - الأرباح تتحدث تلقائياً الآن

  // إصلاح الخزينة بناءً على المعاملات الموجودة
  ipcMain.handle('fix-cashbox-from-transactions', async () => {
    try {
      logSystem('بدء إصلاح الخزينة بناءً على المعاملات الموجودة...', 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // الحصول على الخزينة الحالية
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const currentCashbox = getCashboxStmt.get();

      if (!currentCashbox) {
        throw new Error('لا توجد خزينة في قاعدة البيانات');
      }

      console.log('[CASHBOX-FIX] الخزينة الحالية:', {
        id: currentCashbox.id,
        initial_balance: currentCashbox.initial_balance,
        current_balance: currentCashbox.current_balance,
        sales_total: currentCashbox.sales_total,
        purchases_total: currentCashbox.purchases_total,
        profit_total: currentCashbox.profit_total
      });

      // الحصول على جميع المعاملات
      const getTransactionsStmt = db.prepare(`
        SELECT t.*, inv.avg_price
        FROM transactions t
        LEFT JOIN inventory inv ON t.item_id = inv.item_id
        ORDER BY t.transaction_date ASC
      `);
      const transactions = getTransactionsStmt.all();

      console.log(`[CASHBOX-FIX] تم العثور على ${transactions.length} معاملة`);

      // حساب الإجماليات من المعاملات
      let totalSales = 0;
      let totalPurchases = 0;
      let totalReturns = 0;
      let totalProfit = 0;
      let totalTransportCost = 0;

      for (const transaction of transactions) {
        const amount = parseFloat(transaction.total_price) || 0;
        const transportCost = parseFloat(transaction.transport_cost) || 0;

        if (transaction.transaction_type === 'sale') {
          totalSales += amount;

          // حساب الربح
          let profit = 0;
          if (transaction.profit && transaction.profit > 0) {
            profit = parseFloat(transaction.profit);
          } else if (transaction.selling_price > 0 && transaction.avg_price > 0) {
            // حساب الربح الأساسي
            const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
            // خصم مصاريف النقل
            profit = Math.max(0, basicProfit - transportCost);
          } else {
            // تقدير 20% من سعر البيع
            profit = Math.max(0, (amount * 0.2) - transportCost);
          }

          totalProfit += profit;

        } else if (transaction.transaction_type === 'purchase') {
          totalPurchases += amount;
          totalTransportCost += transportCost;

        } else if (transaction.transaction_type === 'return') {
          totalReturns += amount;

          // خصم الربح المرتبط بالإرجاع
          let returnProfit = 0;
          if (transaction.profit && transaction.profit > 0) {
            returnProfit = parseFloat(transaction.profit);
          } else {
            returnProfit = amount * 0.2; // تقدير 20%
          }
          totalProfit -= returnProfit;
        }
      }

      // التأكد من أن الأرباح لا تكون سالبة
      totalProfit = Math.max(0, totalProfit);

      console.log('[CASHBOX-FIX] الإجماليات المحسوبة من المعاملات:', {
        totalSales,
        totalPurchases,
        totalReturns,
        totalProfit,
        totalTransportCost
      });

      // حساب الرصيد الحالي الجديد
      // مصاريف النقل تؤثر فقط على الأرباح وليس على الرصيد الحالي
      const newCurrentBalance = currentCashbox.initial_balance + totalSales - totalPurchases - totalReturns;

      console.log('[CASHBOX-FIX] الرصيد الحالي الجديد المحسوب:', newCurrentBalance);

      // تحديث الخزينة
      const updateCashboxStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = ?,
            sales_total = ?,
            purchases_total = ?,
            returns_total = ?,
            profit_total = ?,
            transport_total = ?,
            updated_at = ?
        WHERE id = ?
      `);

      const updateResult = updateCashboxStmt.run(
        newCurrentBalance,
        totalSales,
        totalPurchases,
        totalReturns,
        totalProfit,
        totalTransportCost,
        new Date().toISOString(),
        currentCashbox.id
      );

      console.log('[CASHBOX-FIX] نتيجة تحديث الخزينة:', updateResult);

      // التحقق من التحديث
      const updatedCashbox = getCashboxStmt.get();
      console.log('[CASHBOX-FIX] الخزينة بعد التحديث:', {
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total,
        profit_total: updatedCashbox.profit_total,
        transport_total: updatedCashbox.transport_total
      });

      // إرسال إشعار بتحديث الخزينة
      try {
        const eventSystem = require('./event-system');
        eventSystem.notifyCashboxUpdated({
          id: updatedCashbox.id,
          current_balance: updatedCashbox.current_balance,
          sales_total: updatedCashbox.sales_total,
          purchases_total: updatedCashbox.purchases_total,
          returns_total: updatedCashbox.returns_total || 0,
          transport_total: updatedCashbox.transport_total || 0,
          profit_total: updatedCashbox.profit_total,
          success: true
        });
        console.log('[CASHBOX-FIX] تم إرسال إشعار تحديث الخزينة');
      } catch (eventError) {
        console.error('[CASHBOX-FIX] خطأ في إرسال إشعار تحديث الخزينة:', eventError);
      }

      logSystem('تم إصلاح الخزينة بنجاح', 'info');
      return {
        success: true,
        message: 'تم إصلاح الخزينة بنجاح',
        before: {
          current_balance: currentCashbox.current_balance,
          sales_total: currentCashbox.sales_total,
          purchases_total: currentCashbox.purchases_total,
          profit_total: currentCashbox.profit_total
        },
        after: {
          current_balance: updatedCashbox.current_balance,
          sales_total: updatedCashbox.sales_total,
          purchases_total: updatedCashbox.purchases_total,
          profit_total: updatedCashbox.profit_total
        }
      };

    } catch (error) {
      console.error('[CASHBOX-FIX] خطأ في إصلاح الخزينة:', error);
      logError(error, 'fix-cashbox-from-transactions');
      return {
        success: false,
        error: error.message
      };
    }
  });

  // ملاحظة: تم نقل معالج get-transactions إلى دالة registerTransactionsHandlers

  // ملاحظة: تم نقل معالجات get-items و sync-all-inventory إلى الدوال المخصصة لها
  // لتجنب تسجيل نفس المعالج مرتين

  logSystem('تم تسجيل معالجات إضافية بنجاح', 'info');
}

/**
 * تسجيل معالجات الأصناف
 */
function registerItemsHandlers() {
  // الحصول على جميع الأصناف
  ipcMain.handle('get-items', async () => {
    try {
      return itemsManager.getAllItems();
    } catch (error) {
      logError(error, 'get-items');
      throw error;
    }
  });

  // نسخة بديلة للحصول على جميع الأصناف
  ipcMain.handle('get-all-items', async (event, forceRefresh = false) => {
    try {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`الحصول على جميع الأصناف (تحديث إجباري)`, 'info');
      }

      // استخدام معلمة forceRefresh للتحكم في تحديث التخزين المؤقت
      return itemsManager.getAllItems(forceRefresh);
    } catch (error) {
      logError(error, 'get-all-items');
      throw error;
    }
  });

  // الحصول على صنف بواسطة المعرف
  ipcMain.handle('get-item-by-id', async (event, id) => {
    try {
      return itemsManager.getItemById(id);
    } catch (error) {
      logError(error, 'get-item-by-id');
      throw error;
    }
  });

  // البحث عن الأصناف
  ipcMain.handle('search-items', async (event, searchTerm) => {
    try {
      return itemsManager.searchItems(searchTerm);
    } catch (error) {
      logError(error, 'search-items');
      throw error;
    }
  });

  // إضافة صنف جديد
  ipcMain.handle('add-item', async (event, item) => {
    try {
      return itemsManager.addItem(item);
    } catch (error) {
      logError(error, 'add-item');
      throw error;
    }
  });

  // تحديث صنف موجود
  ipcMain.handle('update-item', async (event, id, updates, userRole) => {
    try {
      logSystem(`محاولة تحديث الصنف بالمعرف: ${id} بواسطة مستخدم بدور: ${userRole}`, 'info');
      logSystem(`بيانات التحديث: ${JSON.stringify(updates)}`, 'info');

      // التحقق من وجود معرف الصنف
      if (!id) {
        const errorMsg = 'معرف الصنف غير موجود';
        logSystem(errorMsg, 'error');
        throw new Error(errorMsg);
      }

      // التحقق من وجود بيانات التحديث
      if (!updates) {
        logSystem('بيانات التحديث غير موجودة، إنشاء كائن فارغ', 'warning');
        updates = {};
      }

      // التأكد من أن التحديثات هي كائن وليست قيمة فارغة
      if (typeof updates !== 'object' || Array.isArray(updates)) {
        logSystem(`نوع التحديثات غير صالح: ${typeof updates}، تحويل إلى كائن`, 'warning');
        updates = {};
      }

      // التأكد من أن التحديثات تحتوي على حقول
      if (Object.keys(updates).length === 0) {
        logSystem('كائن التحديثات فارغ، سيتم استخدام القيم الحالية', 'warning');
        // سنستمر في العملية ونستخدم القيم الحالية من قاعدة البيانات
      }

      // تنفيذ عملية التحديث مع تمرير دور المستخدم
      const result = await itemsManager.updateItem(id, updates, userRole);

      // تسجيل نتيجة التحديث
      logSystem(`نتيجة تحديث الصنف: ${JSON.stringify(result)}`, 'info');

      return result;
    } catch (error) {
      logError(error, 'update-item');
      return { success: false, error: error.message };
    }
  });

  // حذف صنف
  ipcMain.handle('delete-item', async (event, params, userRole) => {
    try {
      // استخراج المعرف وخيار الحذف القسري
      let id, forceDelete = false;

      if (typeof params === 'object') {
        id = params.id;
        forceDelete = params.forceDelete === true; // تحويل إلى قيمة منطقية
        // إذا كان userRole غير محدد في المعلمات، نحاول الحصول عليه من الكائن
        if (!userRole && params.userRole) {
          userRole = params.userRole;
        }
      } else {
        id = params;
        // إذا كان هناك معلمة ثانية، فهي forceDelete
        if (arguments.length > 2 && typeof arguments[2] === 'boolean') {
          forceDelete = arguments[2] === true;
        }
      }

      logSystem(`محاولة حذف الصنف بالمعرف: ${id}, forceDelete: ${forceDelete}, userRole: ${userRole}`, 'info');

      // تسجيل معلومات إضافية للتشخيص
      logSystem(`نوع المعرف: ${typeof id}, القيمة: ${id}`, 'info');
      logSystem(`نوع forceDelete: ${typeof forceDelete}, القيمة: ${forceDelete}`, 'info');
      logSystem(`نوع userRole: ${typeof userRole}, القيمة: ${userRole}`, 'info');

      // محاولة حذف الصنف مع تمرير معلمة forceDelete ودور المستخدم
      // نحول الدالة المتزامنة إلى وعد باستخدام Promise.resolve
      const result = await Promise.resolve(itemsManager.deleteItem(id, forceDelete, userRole));

      // تسجيل نتيجة العملية للتشخيص
      logSystem(`نتيجة عملية الحذف: ${JSON.stringify(result)}`, 'info');

      // التحقق من نجاح العملية
      if (!result.success) {
        logSystem(`فشل في حذف الصنف: ${result.error}`, 'error');

        // إذا كان الخطأ متعلقًا بمعاملات البيع، نضيف علامة protected
        if (result.error && result.error.includes('معاملة بيع')) {
          return {
            ...result,
            protected: true
          };
        }

        return result;
      }

      logSystem(`تم حذف الصنف بنجاح: ${id}`, 'info');
      return result;
    } catch (error) {
      logError(error, 'delete-item');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء محاولة حذف الصنف',
        stack: error.stack
      };
    }
  });

  // تم نقل رسالة النجاح إلى الدالة الرئيسية

  // تحديث سعر البيع للصنف
  ipcMain.handle('update-item-selling-price', async (event, { itemId, sellingPrice }) => {
    try {
      logSystem(`تحديث سعر البيع للصنف بالمعرف: ${itemId}`, 'info');
      logSystem(`سعر البيع الجديد: ${sellingPrice}`, 'info');

      // التحقق من وجود دالة updateItemSellingPrice
      if (typeof itemsManager.updateItemSellingPrice !== 'function') {
        logSystem('دالة updateItemSellingPrice غير موجودة في مدير الأصناف', 'error');
        return {
          success: false,
          error: 'دالة updateItemSellingPrice غير موجودة في مدير الأصناف'
        };
      }

      return itemsManager.updateItemSellingPrice(itemId, sellingPrice);
    } catch (error) {
      logError(error, 'update-item-selling-price');
      return { success: false, error: error.message };
    }
  });

  // مسح التخزين المؤقت للأصناف
  ipcMain.handle('clear-items-cache', async (event, operation = '', itemId = null) => {
    try {
      logSystem(`مسح التخزين المؤقت للأصناف (${operation}, ${itemId})`, 'info');
      return itemsManager.clearItemsCache(operation, itemId);
    } catch (error) {
      logError(error, 'clear-items-cache');
      return false;
    }
  });
}

/**
 * تسجيل معالجات المخزون
 */
function registerInventoryHandlers() {
  // الحصول على جميع المخزون
  ipcMain.handle('get-inventory', async (event, forceRefresh = false) => {
    try {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`الحصول على جميع المخزون (تحديث إجباري)`, 'info');
      }

      // استخدام معلمة forceRefresh للتحكم في تحديث التخزين المؤقت
      const inventory = inventoryManager.getAllInventory(forceRefresh);

      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`تم استرجاع ${inventory.length} صنف من المخزون`, 'info');
      }
      return inventory;
    } catch (error) {
      logError(error, 'get-inventory');
      return [];
    }
  });

  // الحصول على مخزون صنف معين
  ipcMain.handle('get-inventory-for-item', async (event, itemId, bypassCache = false) => {
    try {
      logSystem(`الحصول على مخزون الصنف: ${itemId}${bypassCache ? ' (تجاوز التخزين المؤقت)' : ''}`, 'info');

      // استخدام معلمة bypassCache للتحكم في استخدام التخزين المؤقت
      const item = inventoryManager.getInventoryForItem(itemId, bypassCache);

      if (!item) {
        logSystem(`لم يتم العثور على الصنف بالمعرف: ${itemId}`, 'warning');
        return null;
      }

      return item;
    } catch (error) {
      logError(error, 'get-inventory-for-item');
      return null;
    }
  });

  // الحصول على عنصر مخزون بواسطة معرف الصنف
  ipcMain.handle('get-inventory-item', async (event, itemId, bypassCache = false) => {
    try {
      logSystem(`الحصول على عنصر المخزون للصنف: ${itemId}${bypassCache ? ' (تجاوز التخزين المؤقت)' : ''}`, 'info');

      // استخدام معلمة bypassCache للتحكم في استخدام التخزين المؤقت
      const item = inventoryManager.getInventoryForItem(itemId, bypassCache);

      if (!item) {
        logSystem(`لم يتم العثور على الصنف بالمعرف: ${itemId}`, 'warning');
        return null;
      }

      logSystem(`تم العثور على الصنف: ${item.name} (${itemId})`, 'info');
      return item;
    } catch (error) {
      logError(error, 'get-inventory-item');
      return null;
    }
  });

  // مزامنة المخزون
  ipcMain.handle('sync-inventory', async () => {
    try {
      return inventoryManager.syncInventoryWithItems();
    } catch (error) {
      logError(error, 'sync-inventory');
      throw error;
    }
  });

  // مزامنة جميع المخزون
  ipcMain.handle('sync-all-inventory', async () => {
    try {
      logSystem('استدعاء sync-all-inventory من registerInventoryHandlers', 'info');

      // التحقق من وجود مزامنة قيد التنفيذ
      if (global.isSyncInProgress) {
        logSystem('هناك عملية مزامنة قيد التنفيذ بالفعل، سيتم تجاهل هذا الطلب', 'warning');
        return {
          success: false,
          error: 'هناك عملية مزامنة قيد التنفيذ بالفعل، يرجى الانتظار'
        };
      }

      return inventoryManager.syncInventoryWithItems();
    } catch (error) {
      logError(error, 'sync-all-inventory');
      return { success: false, error: error.message };
    }
  });

  // التحقق من حالة المخزون بعد عملية الإرجاع
  ipcMain.handle('verify-inventory-after-return', async (event, itemId) => {
    try {
      logSystem(`التحقق من حالة المخزون بعد عملية الإرجاع للصنف: ${itemId}`, 'info');

      // التحقق من وجود دالة verifyInventoryAfterReturn
      if (typeof inventoryManager.verifyInventoryAfterReturn !== 'function') {
        return {
          success: false,
          error: 'دالة verifyInventoryAfterReturn غير موجودة في مدير المخزون'
        };
      }

      const result = inventoryManager.verifyInventoryAfterReturn(itemId);

      return {
        success: true,
        result
      };
    } catch (error) {
      logError(error, 'verify-inventory-after-return');
      return { success: false, error: error.message };
    }
  });

  // التحقق من حالة المخزون بعد عملية المزامنة
  ipcMain.handle('verify-inventory-after-sync', async (event, itemId) => {
    try {
      logSystem(`التحقق من حالة المخزون بعد عملية المزامنة للصنف: ${itemId}`, 'info');

      // التحقق من وجود دالة verifyInventoryAfterSync
      if (typeof inventoryManager.verifyInventoryAfterSync !== 'function') {
        return {
          success: false,
          error: 'دالة verifyInventoryAfterSync غير موجودة في مدير المخزون'
        };
      }

      const result = inventoryManager.verifyInventoryAfterSync(itemId);

      return {
        success: true,
        result
      };
    } catch (error) {
      logError(error, 'verify-inventory-after-sync');
      return { success: false, error: error.message };
    }
  });

  // تحديث المخزون
  ipcMain.handle('update-inventory', async (event, { itemId, updates }) => {
    try {
      logSystem(`تحديث المخزون للصنف: ${itemId} بالتحديثات: ${JSON.stringify(updates)}`, 'info');

      // استخدام وظائف مدير المخزون للتحديث
      // نستخدم وظيفة updateInventoryItem من مدير المخزون بدلاً من الوصول المباشر لقاعدة البيانات
      const result = await inventoryManager.updateInventoryItem(itemId, updates);

      return {
        success: true,
        itemId,
        updates,
        ...result
      };
    } catch (error) {
      logError(error, 'update-inventory');
      throw error;
    }
  });

  // مسح التخزين المؤقت للمخزون
  ipcMain.handle('clear-inventory-cache', async () => {
    try {
      logSystem('مسح التخزين المؤقت للمخزون', 'info');
      inventoryManager.clearInventoryCache();
      return { success: true };
    } catch (error) {
      logError(error, 'clear-inventory-cache');
      return { success: false, error: error.message };
    }
  });

  // تحديث المخزون بعد عملية الإرجاع
  ipcMain.handle('update-inventory-after-return', async (event, { itemId, quantity }) => {
    try {
      logSystem(`تحديث المخزون بعد عملية الإرجاع للصنف: ${itemId} بكمية: ${quantity}`, 'info');

      // التحقق من وجود دالة updateInventoryAfterReturn
      if (typeof inventoryManager.updateInventoryAfterReturn === 'function') {
        // استخدام الدالة الجديدة
        const result = await inventoryManager.updateInventoryAfterReturn(itemId, quantity);

        logSystem(`نتيجة تحديث المخزون بعد الإرجاع: ${JSON.stringify(result)}`, 'info');

        // تسجيل معلومات إضافية للتشخيص
        try {
          // التحقق من حالة المخزون بعد التحديث
          const updatedInventory = inventoryManager.getInventoryForItem(itemId, true);
          if (updatedInventory) {
            logSystem(`حالة المخزون بعد التحديث - الصنف: ${itemId}, الكمية الحالية: ${updatedInventory.current_quantity}`, 'info');
          }
        } catch (verifyError) {
          logSystem(`تحذير: فشل في التحقق من حالة المخزون بعد التحديث: ${verifyError.message}`, 'warning');
        }

        return {
          success: result.success,
          itemId,
          quantity,
          newQuantity: result.newQuantity,
          error: result.error
        };
      } else {
        // استخدام الطريقة القديمة
        logSystem(`الدالة updateInventoryAfterReturn غير موجودة، استخدام updateInventoryItem بدلاً منها`, 'warning');

        // الحصول على معلومات المخزون الحالية
        const inventory = inventoryManager.getInventoryForItem(itemId, true);

        if (!inventory) {
          throw new Error(`الصنف غير موجود: ${itemId}`);
        }

        // تسجيل معلومات المخزون الحالية للتشخيص
        logSystem(`معلومات المخزون الحالية - الصنف: ${itemId}, الكمية الحالية: ${inventory.current_quantity}, الكمية المسترجعة: ${quantity}`, 'info');

        // حساب الكمية الجديدة
        const currentQuantity = inventory.current_quantity || 0;
        const newQuantity = currentQuantity + quantity;

        logSystem(`الكمية الجديدة بعد الإرجاع: ${newQuantity} (${currentQuantity} + ${quantity})`, 'info');

        // تحديث المخزون
        const result = await inventoryManager.updateInventoryItem(itemId, {
          current_quantity: newQuantity
        });

        // تسجيل معلومات إضافية للتشخيص
        try {
          // التحقق من حالة المخزون بعد التحديث
          const updatedInventory = inventoryManager.getInventoryForItem(itemId, true);
          if (updatedInventory) {
            logSystem(`حالة المخزون بعد التحديث - الصنف: ${itemId}, الكمية الجديدة: ${updatedInventory.current_quantity}`, 'info');
          }
        } catch (verifyError) {
          logSystem(`تحذير: فشل في التحقق من حالة المخزون بعد التحديث: ${verifyError.message}`, 'warning');
        }

        return {
          success: true,
          itemId,
          quantity,
          newQuantity,
          ...result
        };
      }
    } catch (error) {
      logError(error, 'update-inventory-after-return');
      return {
        success: false,
        itemId,
        quantity,
        error: error.message
      };
    }
  });

  // معالج لطلب تحديث واجهة المستخدم
  ipcMain.handle('refresh-needed', async (event, { target }) => {
    try {
      logSystem(`طلب تحديث واجهة المستخدم للهدف: ${target}`, 'info');

      // استدعاء نظام الأحداث لإرسال إشعار بالتحديث
      const eventSystem = require('./event-system');

      // إرسال إشعار بالحاجة للتحديث
      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: target || 'all',
        timestamp: new Date().toISOString()
      });

      logSystem(`تم إرسال إشعار بالحاجة للتحديث للهدف: ${target}`, 'info');

      return {
        success: true,
        message: `تم إرسال طلب التحديث للهدف: ${target}`
      };
    } catch (error) {
      logError(error, 'refresh-needed');
      return {
        success: false,
        error: error.message
      };
    }
  });

  // معالج لإرسال إشعار بالحاجة للتحديث
  ipcMain.handle('send-refresh-event', async (event, { target, timestamp }) => {
    try {
      logSystem(`إرسال إشعار بالحاجة للتحديث للهدف: ${target}`, 'info');

      // استدعاء نظام الأحداث لإرسال إشعار بالتحديث
      const eventSystem = require('./event-system');

      // إرسال إشعار بالحاجة للتحديث
      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: target || 'all',
        timestamp: timestamp || new Date().toISOString()
      });

      logSystem(`تم إرسال إشعار بالحاجة للتحديث للهدف: ${target}`, 'info');

      return {
        success: true,
        message: `تم إرسال طلب التحديث للهدف: ${target}`
      };
    } catch (error) {
      logError(error, 'send-refresh-event');
      return {
        success: false,
        error: error.message
      };
    }
  });

  // معالج لإعادة تحميل الصفحة
  ipcMain.handle('reload-page', async (event) => {
    try {
      logSystem('طلب إعادة تحميل الصفحة', 'info');

      // الحصول على النافذة الحالية
      const { BrowserWindow } = require('electron');
      const currentWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

      if (currentWindow) {
        logSystem('إعادة تحميل النافذة الحالية', 'info');
        currentWindow.webContents.reload();
        return { success: true, message: 'تم إعادة تحميل الصفحة بنجاح' };
      } else {
        logSystem('لم يتم العثور على نافذة لإعادة تحميلها', 'warning');
        return { success: false, error: 'لم يتم العثور على نافذة لإعادة تحميلها' };
      }
    } catch (error) {
      logError(error, 'reload-page');
      return {
        success: false,
        error: error.message
      };
    }
  });

  // معالج لإرسال إشعار بتحديث المخزون
  ipcMain.handle('notify-inventory-updated', async (event, { itemId, operation, quantity }) => {
    try {
      logSystem(`إرسال إشعار بتحديث المخزون للصنف ${itemId} (${operation})`, 'info');

      // استدعاء نظام الأحداث لإرسال إشعار بتحديث المخزون
      const eventSystem = require('./event-system');

      // الحصول على معلومات الصنف
      const itemInfo = await inventoryManager.getInventoryForItem(itemId, true);

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId,
        name: itemInfo ? itemInfo.name : `الصنف ${itemId}`,
        current_quantity: itemInfo ? itemInfo.current_quantity : null,
        operation,
        quantity,
        timestamp: new Date().toISOString()
      });

      // إرسال إشعار بالحاجة للتحديث أيضاً
      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: 'inventory',
        timestamp: new Date().toISOString()
      });

      // إذا كانت العملية هي إضافة صنف جديد، نرسل إشعار بتحديث المشتريات والأصناف أيضاً
      if (operation === 'add-item') {
        // إرسال إشعار بتحديث المشتريات
        eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
          target: 'transactions',
          timestamp: new Date().toISOString()
        });

        // إرسال إشعار بتحديث الأصناف
        eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
          target: 'items',
          timestamp: new Date().toISOString()
        });

        // إرسال إشعار إضافي بإضافة صنف جديد
        eventSystem.notifyItemAdded({
          id: itemId,
          name: itemInfo ? itemInfo.name : `الصنف ${itemId}`,
          operation: 'add-item'
        });
      }

      logSystem(`تم إرسال إشعار بتحديث المخزون للصنف ${itemId}`, 'info');

      return {
        success: true,
        message: `تم إرسال إشعار بتحديث المخزون للصنف ${itemId}`,
        itemInfo
      };
    } catch (error) {
      logError(error, 'notify-inventory-updated');
      return {
        success: false,
        error: error.message
      };
    }
  });

  // الحصول على معلومات المخزون لصنف محدد
  try {
    // التحقق من وجود المعالج قبل تسجيله
    if (!ipcMain.eventNames().includes('get-inventory-for-item')) {
      ipcMain.handle('get-inventory-for-item', async (event, { itemId, skipCache }) => {
        try {
          logSystem(`الحصول على معلومات المخزون للصنف ${itemId} (تجاوز التخزين المؤقت: ${skipCache})`, 'info');

          // الحصول على معلومات المخزون للصنف
          const itemInfo = inventoryManager.getInventoryForItem(itemId, skipCache);

          if (itemInfo) {
            logSystem(`تم الحصول على معلومات المخزون للصنف ${itemId} بنجاح`, 'info');
            return {
              success: true,
              itemInfo
            };
          } else {
            logSystem(`لم يتم العثور على معلومات المخزون للصنف ${itemId}`, 'warning');
            return {
              success: false,
              error: `لم يتم العثور على معلومات المخزون للصنف ${itemId}`
            };
          }
        } catch (error) {
          logError(error, 'get-inventory-for-item');
          return {
            success: false,
            error: error.message
          };
        }
      });
    } else {
      logSystem('معالج get-inventory-for-item مسجل بالفعل، تم تجاوزه', 'info');
    }
  } catch (error) {
    logError(error, 'تسجيل معالج get-inventory-for-item');
  }

  // الحصول على جميع المخزون (معالج جديد)
  ipcMain.handle('get-all-inventory', async (event, forceRefresh = false) => {
    try {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`الحصول على جميع المخزون (تحديث إجباري)`, 'info');
      }

      // استخدام معلمة forceRefresh للتحكم في تحديث التخزين المؤقت
      const inventory = inventoryManager.getAllInventory(forceRefresh);

      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`تم استرجاع ${inventory.length} صنف من المخزون`, 'info');
      }
      return inventory;
    } catch (error) {
      logError(error, 'get-all-inventory');
      return [];
    }
  });

  // إرسال حدث لجميع النوافذ المفتوحة
  ipcMain.handle('broadcast-event', async (event, { eventName, data }) => {
    try {
      logSystem(`إرسال حدث ${eventName} لجميع النوافذ المفتوحة`, 'info');

      // الحصول على جميع النوافذ المفتوحة
      const { BrowserWindow } = require('electron');
      const windows = BrowserWindow.getAllWindows();

      // إرسال الحدث لجميع النوافذ
      windows.forEach(window => {
        try {
          window.webContents.send(eventName, data);
        } catch (windowError) {
          logSystem(`فشل في إرسال الحدث ${eventName} للنافذة: ${windowError.message}`, 'warning');
        }
      });

      logSystem(`تم إرسال الحدث ${eventName} لـ ${windows.length} نافذة`, 'info');

      return {
        success: true,
        message: `تم إرسال الحدث ${eventName} لـ ${windows.length} نافذة`,
        windowsCount: windows.length
      };
    } catch (error) {
      logError(error, 'broadcast-event');
      return {
        success: false,
        error: error.message
      };
    }
  });

  logSystem('تم تسجيل معالجات المخزون بنجاح', 'info');
}

/**
 * تسجيل معالجات العملاء
 */
function registerCustomersHandlers() {
  // الحصول على جميع العملاء
  ipcMain.handle('get-all-customers', async () => {
    try {
      return customersManager.getAllCustomers();
    } catch (error) {
      logError(error, 'get-all-customers');
      throw error;
    }
  });

  // الحصول على عميل بواسطة المعرف
  ipcMain.handle('get-customer-by-id', async (event, id) => {
    try {
      return customersManager.getCustomerById(id);
    } catch (error) {
      logError(error, 'get-customer-by-id');
      throw error;
    }
  });

  // البحث عن العملاء
  ipcMain.handle('search-customers', async (event, searchTerm) => {
    try {
      return customersManager.searchCustomers(searchTerm);
    } catch (error) {
      logError(error, 'search-customers');
      throw error;
    }
  });

  // الحصول على العملاء حسب النوع
  ipcMain.handle('get-customers-by-type', async (event, customerType) => {
    try {
      return customersManager.getCustomersByType(customerType);
    } catch (error) {
      logError(error, 'get-customers-by-type');
      throw error;
    }
  });

  // الحصول على العملاء الفرعيين
  ipcMain.handle('get-sub-customers', async (event, parentId) => {
    try {
      return customersManager.getSubCustomers(parentId);
    } catch (error) {
      logError(error, 'get-sub-customers');
      throw error;
    }
  });

  // إضافة عميل جديد
  ipcMain.handle('add-customer', async (event, customer) => {
    try {
      return customersManager.addCustomer(customer);
    } catch (error) {
      logError(error, 'add-customer');
      throw error;
    }
  });

  // تحديث عميل موجود
  ipcMain.handle('update-customer', async (event, id, updates, userRole) => {
    try {
      logSystem(`محاولة تحديث العميل بالمعرف: ${id} بواسطة مستخدم بدور: ${userRole}`, 'info');
      logSystem(`بيانات التحديث: ${JSON.stringify(updates)}`, 'info');

      // التحقق من صلاحيات المستخدم
      if (userRole === 'viewer') {
        logSystem(`رفض تحديث العميل: المستخدم بدور مشاهد لا يملك الصلاحية`, 'warning');
        return {
          success: false,
          error: 'ليس لديك صلاحية لتعديل العملاء. المشاهد يمكنه فقط عرض البيانات.'
        };
      }

      // التحقق من وجود معرف العميل
      if (!id) {
        const errorMsg = 'معرف العميل غير موجود';
        logSystem(errorMsg, 'error');
        return { success: false, error: errorMsg };
      }

      // التحقق من وجود تحديثات
      if (!updates || Object.keys(updates).length === 0) {
        const errorMsg = 'لم يتم تحديد أي تحديثات';
        logSystem(errorMsg, 'error');
        return { success: false, error: errorMsg };
      }

      // تنفيذ عملية التحديث
      const result = await customersManager.updateCustomer(id, updates, userRole);

      // تسجيل نتيجة العملية
      if (result && result.success) {
        logSystem(`تم تحديث العميل بنجاح: ${id}`, 'info');
      } else {
        const errorMsg = result ? result.error : 'سبب غير معروف';
        logSystem(`فشل في تحديث العميل: ${errorMsg}`, 'error');
      }

      return result || { success: false, error: 'لم يتم استلام نتيجة من وظيفة تحديث العميل' };
    } catch (error) {
      logError(error, 'update-customer');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء محاولة تحديث العميل',
        stack: error.stack
      };
    }
  });

  // حذف عميل
  ipcMain.handle('delete-customer', async (event, id, userRole) => {
    try {
      logSystem(`محاولة حذف العميل بالمعرف: ${id} بواسطة مستخدم بدور: ${userRole}`, 'info');

      // التحقق من صلاحيات المستخدم
      if (userRole === 'viewer') {
        logSystem(`رفض حذف العميل: المستخدم بدور مشاهد لا يملك الصلاحية`, 'warning');
        return {
          success: false,
          error: 'ليس لديك صلاحية لحذف العملاء. المشاهد يمكنه فقط عرض البيانات.'
        };
      }

      // تحويل المعرف إلى رقم إذا كان نصًا
      const customerId = typeof id === 'string' ? Number(id) : id;

      // استدعاء وظيفة حذف العميل مباشرة مع تمرير دور المستخدم
      const result = await customersManager.deleteCustomer(customerId, userRole);

      // تسجيل نتيجة العملية
      if (result && result.success) {
        logSystem(`تم حذف العميل بنجاح: ${customerId}`, 'info');

        // تحديث سياق العملاء في الذاكرة بعد الحذف الناجح
        try {
          // الحصول على قائمة العملاء المحدثة
          const updatedCustomers = await customersManager.getAllCustomers(true);

          // تحديث سياق العملاء في الذاكرة
          if (customersManager && typeof customersManager.updateCustomersContext === 'function') {
            const contextResult = await customersManager.updateCustomersContext(updatedCustomers);
            logSystem(`تم تحديث سياق العملاء في الذاكرة بعد الحذف: ${contextResult.success ? 'نجاح' : 'فشل'}`, 'info');
          } else {
            // استخدام الطريقة البديلة
            global.customersCache = updatedCustomers;
            logSystem(`تم تحديث سياق العملاء في الذاكرة العالمية بعد الحذف (${updatedCustomers.length} عميل)`, 'info');
          }
        } catch (contextError) {
          logError(contextError, 'update-context-after-delete');
          logSystem(`فشل في تحديث سياق العملاء بعد الحذف: ${contextError.message}`, 'warning');
          // لا نريد إيقاف العملية إذا فشل تحديث السياق
        }
      } else {
        const errorMsg = result ? result.error : 'سبب غير معروف';
        logSystem(`فشل في حذف العميل: ${errorMsg}`, 'error');

        // إضافة علامات إضافية للواجهة الأمامية
        if (result && result.error) {
          if (result.error.includes('عميل فرعي')) {
            result.hasSubCustomers = true;
            // استخراج عدد العملاء الفرعيين من رسالة الخطأ
            const match = result.error.match(/(\d+) عميل فرعي/);
            result.subCustomersCount = match ? parseInt(match[1]) : 0;
          } else if (result.error.includes('معاملة')) {
            result.hasTransactions = true;
            // استخراج عدد المعاملات من رسالة الخطأ
            const match = result.error.match(/(\d+) معاملة/);
            result.transactionsCount = match ? parseInt(match[1]) : 0;
          }
        }
      }

      return result || { success: false, error: 'لم يتم استلام نتيجة من وظيفة حذف العميل' };
    } catch (error) {
      logError(error, 'delete-customer');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء محاولة حذف العميل',
        stack: error.stack
      };
    }
  });

  // الحصول على سجل مبيعات العميل
  ipcMain.handle('get-customer-sales-history', async (event, customerId) => {
    try {
      logSystem(`استدعاء get-customer-sales-history للعميل: ${customerId}`, 'info');
      logSystem(`نوع معرف العميل: ${typeof customerId}`, 'info');
      logSystem(`قيمة معرف العميل: ${customerId}`, 'info');

      // التحقق من صحة معرف العميل
      if (!customerId) {
        logSystem('معرف العميل غير صالح أو غير محدد', 'error');
        throw new Error('معرف العميل غير صالح أو غير محدد');
      }

      // التحقق من وجود مدير العملاء
      if (!customersManager) {
        logSystem('مدير العملاء غير متوفر', 'error');
        throw new Error('مدير العملاء غير متوفر');
      }

      // التحقق من وجود وظيفة getCustomerSalesHistory
      if (typeof customersManager.getCustomerSalesHistory !== 'function') {
        logSystem('وظيفة getCustomerSalesHistory غير متوفرة في مدير العملاء', 'error');
        throw new Error('وظيفة getCustomerSalesHistory غير متوفرة في مدير العملاء');
      }

      // استدعاء وظيفة الحصول على سجل مبيعات العميل
      logSystem(`استدعاء customersManager.getCustomerSalesHistory مع المعرف: ${customerId}`, 'info');
      const result = await customersManager.getCustomerSalesHistory(customerId);

      // تسجيل نتيجة الاستدعاء
      if (result) {
        logSystem(`تم الحصول على سجل مبيعات العميل ${customerId} بنجاح: ${result.count} معاملة، ${result.invoiceCount} فاتورة`, 'info');
        logSystem(`نتيجة الاستدعاء: ${JSON.stringify(result)}`, 'info');
      } else {
        logSystem(`لم يتم استلام نتيجة من customersManager.getCustomerSalesHistory`, 'error');
      }

      return result;
    } catch (error) {
      logError(error, 'get-customer-sales-history');
      logSystem(`خطأ في استدعاء get-customer-sales-history: ${error.message}`, 'error');
      logSystem(`مكدس الخطأ: ${error.stack}`, 'error');

      // إرجاع كائن بالتنسيق المتوقع بدلاً من رمي استثناء
      const defaultResult = {
        customer: { name: 'عميل غير معروف', id: customerId },
        sales: [],
        groupedSales: [],
        totalSales: 0,
        totalProfit: 0,
        count: 0,
        invoiceCount: 0
      };

      logSystem(`إرجاع نتيجة افتراضية: ${JSON.stringify(defaultResult)}`, 'info');
      return defaultResult;
    }
  });

  // تحديث سياق العملاء في الذاكرة
  ipcMain.handle('update-customers-context', async (event, customers) => {
    try {
      logSystem(`تحديث سياق العملاء في الذاكرة: ${customers.length} عميل`, 'info');

      // تحديث قائمة العملاء في الذاكرة
      if (customersManager && typeof customersManager.updateCustomersInMemory === 'function') {
        // إذا كانت الوظيفة موجودة، استخدمها
        const result = await customersManager.updateCustomersInMemory(customers);
        logSystem(`تم تحديث سياق العملاء في الذاكرة بنجاح`, 'info');
        return result;
      } else {
        // إذا لم تكن الوظيفة موجودة، قم بتخزين العملاء في متغير عالمي
        global.customersCache = customers;
        logSystem(`تم تخزين العملاء في متغير عالمي (${customers.length} عميل)`, 'info');
        return { success: true, count: customers.length };
      }
    } catch (error) {
      logError(error, 'update-customers-context');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تحديث سياق العملاء في الذاكرة',
        stack: error.stack
      };
    }
  });

  // الحصول على مبيعات العميل (للتوافق مع الواجهة الأمامية)
  ipcMain.handle('get-customer-sales', async (event, customerId) => {
    try {
      logSystem(`استدعاء get-customer-sales للعميل: ${customerId}`, 'info');

      // استخدام نفس وظيفة الحصول على سجل مبيعات العميل
      return await customersManager.getCustomerSalesHistory(customerId);
    } catch (error) {
      logError(error, 'get-customer-sales');

      // إرجاع كائن بالتنسيق المتوقع بدلاً من رمي استثناء
      return {
        customer: { name: 'عميل غير معروف', id: customerId },
        sales: [],
        groupedSales: [],
        totalSales: 0,
        totalProfit: 0,
        count: 0,
        invoiceCount: 0
      };
    }
  });

  // الحصول على معلومات الصنف للبيع
  ipcMain.handle('get-item-info-for-sale', async (event, itemId) => {
    try {
      logSystem(`الحصول على معلومات الصنف للبيع: ${itemId}`, 'info');
      return customersManager.getItemInfoForSale(itemId);
    } catch (error) {
      logError(error, 'get-item-info-for-sale');
      return null;
    }
  });

  // الحصول على إجمالي الكمية المباعة للعميل لصنف معين
  ipcMain.handle('get-customer-item-sales', async (event, { customerId, itemId }) => {
    try {
      logSystem(`الحصول على إجمالي الكمية المباعة للعميل ${customerId} للصنف ${itemId}`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // استعلام SQL للحصول على إجمالي الكمية المباعة
      const stmt = db.prepare(`
        SELECT SUM(quantity) as totalSold
        FROM transactions
        WHERE item_id = ? AND customer_id = ? AND transaction_type = 'sale'
      `);

      const result = stmt.get(itemId, customerId);

      logSystem(`نتيجة استعلام إجمالي الكمية المباعة: ${JSON.stringify(result)}`, 'info');

      return {
        customerId,
        itemId,
        totalSold: result ? result.totalSold || 0 : 0
      };
    } catch (error) {
      logError(error, 'get-customer-item-sales');
      return {
        customerId,
        itemId,
        totalSold: 0,
        error: error.message
      };
    }
  });

  // الحصول على إجمالي الكمية المسترجعة للعميل لصنف معين
  ipcMain.handle('get-customer-item-returns', async (event, { customerId, itemId }) => {
    try {
      logSystem(`الحصول على إجمالي الكمية المسترجعة للعميل ${customerId} للصنف ${itemId}`, 'info');

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // استعلام SQL للحصول على إجمالي الكمية المسترجعة
      const stmt = db.prepare(`
        SELECT SUM(quantity) as totalReturned
        FROM transactions
        WHERE item_id = ? AND customer_id = ? AND transaction_type = 'return'
      `);

      const result = stmt.get(itemId, customerId);

      logSystem(`نتيجة استعلام إجمالي الكمية المسترجعة: ${JSON.stringify(result)}`, 'info');

      return {
        customerId,
        itemId,
        totalReturned: result ? result.totalReturned || 0 : 0
      };
    } catch (error) {
      logError(error, 'get-customer-item-returns');
      return {
        customerId,
        itemId,
        totalReturned: 0,
        error: error.message
      };
    }
  });

  // إنشاء فاتورة فرعية
  ipcMain.handle('create-sub-invoice', async (event, parentInvoiceNumber, customerId, selectedItems) => {
    try {
      logSystem(`إنشاء فاتورة فرعية - الفاتورة الرئيسية: ${parentInvoiceNumber}, العميل: ${customerId}, عدد الأصناف: ${selectedItems.length}`, 'info');

      // التحقق من صحة البيانات
      if (!parentInvoiceNumber) {
        return { success: false, error: 'رقم الفاتورة الرئيسية مطلوب' };
      }

      if (!customerId) {
        return { success: false, error: 'معرف العميل مطلوب' };
      }

      if (!selectedItems || !Array.isArray(selectedItems) || selectedItems.length === 0) {
        return { success: false, error: 'يجب تحديد صنف واحد على الأقل للفاتورة الفرعية' };
      }

      // استدعاء وظيفة إنشاء فاتورة فرعية من مدير العملاء
      const result = await customersManager.createSubInvoice(parentInvoiceNumber, customerId, selectedItems);

      // تسجيل نتيجة العملية
      if (result && result.success) {
        logSystem(`تم إنشاء الفاتورة الفرعية بنجاح: ${result.subInvoice.invoice_number}`, 'info');
      } else {
        const errorMsg = result ? result.error : 'سبب غير معروف';
        logSystem(`فشل في إنشاء الفاتورة الفرعية: ${errorMsg}`, 'error');
      }

      return result;
    } catch (error) {
      logError(error, 'create-sub-invoice');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء إنشاء الفاتورة الفرعية'
      };
    }
  });

  logSystem('تم تسجيل معالجات العملاء بنجاح', 'info');
}

/**
 * تسجيل معالجات المعاملات
 */
function registerTransactionsHandlers() {
  // إضافة معاملة جديدة
  ipcMain.handle('add-transaction', async (event, transaction) => {
    try {
      logSystem(`[TRANSACTION-DEBUG] إضافة معاملة جديدة: ${transaction.transaction_type} للصنف ${transaction.item_id}`, 'info');
      console.log(`[TRANSACTION-DEBUG] إضافة معاملة جديدة: ${transaction.transaction_type} للصنف ${transaction.item_id}`);

      // تسجيل المعاملة كاملة للتشخيص
      logSystem(`[TRANSACTION-DEBUG] بيانات المعاملة الكاملة: ${JSON.stringify(transaction)}`, 'info');
      console.log(`[TRANSACTION-DEBUG] بيانات المعاملة الكاملة:`, transaction);

      // التحقق من وجود الحد الأدنى
      if (transaction.minimum_quantity !== undefined) {
        logSystem(`[TRANSACTION-DEBUG] الحد الأدنى في المعاملة: ${transaction.minimum_quantity}`, 'info');
        console.log(`[TRANSACTION-DEBUG] الحد الأدنى في المعاملة: ${transaction.minimum_quantity}`);
      } else {
        logSystem(`[TRANSACTION-DEBUG] الحد الأدنى غير موجود في المعاملة`, 'info');
        console.log(`[TRANSACTION-DEBUG] الحد الأدنى غير موجود في المعاملة`);
      }

      // استخدام وعد مع معالجة الأخطاء المحسنة
      const result = await new Promise((resolve, reject) => {
        console.log(`[TRANSACTION-DEBUG] استدعاء createTransaction...`);
        transactionManager.createTransaction(transaction)
          .then(result => {
            logSystem(`[TRANSACTION-DEBUG] تم إضافة المعاملة بنجاح: ${JSON.stringify(result)}`, 'info');
            console.log(`[TRANSACTION-DEBUG] تم إضافة المعاملة بنجاح:`, result);

            // إرسال إشعار بالحاجة للتحديث
            try {
              const eventSystem = require('./event-system');
              eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
                target: 'cashbox',
                timestamp: new Date().toISOString()
              });
              console.log(`[TRANSACTION-DEBUG] تم إرسال إشعار بالحاجة لتحديث الخزينة`);
            } catch (eventError) {
              console.error(`[TRANSACTION-DEBUG] خطأ في إرسال إشعار التحديث:`, eventError);
            }

            resolve(result);
          })
          .catch(error => {
            logError(error, 'add-transaction - inner promise');
            console.error(`[TRANSACTION-DEBUG] خطأ في إضافة المعاملة:`, error);
            reject(error);
          });
      });

      return result;
    } catch (error) {
      logError(error, 'add-transaction');
      console.error(`[TRANSACTION-DEBUG] خطأ في معالج add-transaction:`, error);
      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء إضافة المعاملة'
      };
    }
  });

  // الحصول على جميع المعاملات
  ipcMain.handle('get-transactions', async (event, filters) => {
    try {
      logSystem(`الحصول على المعاملات مع الفلاتر: ${JSON.stringify(filters)}`, 'info');
      // استخدام وظيفة الحصول على المعاملات من مدير المعاملات
      const transactions = await transactionManager.getTransactions(filters);
      logSystem(`تم الحصول على ${transactions.length} معاملة بنجاح`, 'info');
      return transactions;
    } catch (error) {
      logError(error, 'get-transactions');
      return [];
    }
  });

  // مسح التخزين المؤقت للمعاملات
  ipcMain.handle('clear-transactions-cache', async () => {
    try {
      logSystem('مسح التخزين المؤقت للمعاملات', 'info');
      // لا يوجد تخزين مؤقت للمعاملات في الخادم حالياً، لكن نعيد نجاح العملية
      return { success: true };
    } catch (error) {
      logError(error, 'clear-transactions-cache');
      return { success: false, error: error.message };
    }
  });

  logSystem('تم تسجيل معالجات المعاملات بنجاح', 'info');
}

/**
 * تسجيل معالجات الخزينة
 */
function registerCashboxHandlers() {
  // الحصول على الخزينة
  ipcMain.handle('get-cashbox', async () => {
    try {
      return cashboxManager.getCashbox();
    } catch (error) {
      logError(error, 'get-cashbox');
      throw error;
    }
  });

  // إنشاء خزينة جديدة
  ipcMain.handle('create-cashbox', async (event, { initial_balance }) => {
    try {
      return cashboxManager.createCashbox(initial_balance);
    } catch (error) {
      logError(error, 'create-cashbox');
      throw error;
    }
  });

  // تحديث الرصيد الابتدائي للخزينة
  ipcMain.handle('update-cashbox-initial-balance', async (event, { initial_balance }) => {
    try {
      return cashboxManager.updateInitialBalance(initial_balance);
    } catch (error) {
      logError(error, 'update-cashbox-initial-balance');
      throw error;
    }
  });

  // إضافة معاملة للخزينة
  ipcMain.handle('add-cashbox-transaction', async (event, transaction) => {
    try {
      return cashboxManager.addTransaction(transaction);
    } catch (error) {
      logError(error, 'add-cashbox-transaction');
      throw error;
    }
  });

  // الحصول على معاملات الخزينة
  ipcMain.handle('get-cashbox-transactions', async (event, filters) => {
    try {
      return cashboxManager.getTransactions(filters);
    } catch (error) {
      logError(error, 'get-cashbox-transactions');
      throw error;
    }
  });

  // إصلاح قيم الخزينة السالبة
  ipcMain.handle('fix-negative-cashbox-values', async () => {
    try {
      logSystem('استدعاء fix-negative-cashbox-values', 'info');

      // استيراد مدير المعاملات الموحد
      const transactionManager = require('./unified-transaction-manager');

      // التحقق من وجود دالة إصلاح قيم الخزينة السالبة
      if (!transactionManager || typeof transactionManager.fixNegativeCashboxValues !== 'function') {
        throw new Error('مدير المعاملات الموحد غير متوفر أو دالة fixNegativeCashboxValues غير موجودة');
      }

      // استدعاء دالة إصلاح قيم الخزينة السالبة
      const result = transactionManager.fixNegativeCashboxValues();

      // تسجيل نتيجة العملية
      if (result && result.success) {
        logSystem('تم إصلاح قيم الخزينة السالبة بنجاح', 'info');
        logSystem(`قيم الخزينة قبل الإصلاح: ${JSON.stringify(result.before)}`, 'info');
        logSystem(`قيم الخزينة بعد الإصلاح: ${JSON.stringify(result.after)}`, 'info');
      } else {
        const errorMsg = result ? result.error : 'سبب غير معروف';
        logSystem(`فشل في إصلاح قيم الخزينة السالبة: ${errorMsg}`, 'error');
      }

      return result;
    } catch (error) {
      logError(error, 'fix-negative-cashbox-values');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء إصلاح قيم الخزينة السالبة'
      };
    }
  });

  // إصلاح حساب الأرباح في الخزينة
  ipcMain.handle('fix-profit-calculation', async () => {
    try {
      logSystem('استدعاء fix-profit-calculation', 'info');

      // التحقق من وجود دالة إصلاح حساب الأرباح
      if (!cashboxManager || typeof cashboxManager.fixProfitCalculation !== 'function') {
        throw new Error('مدير الخزينة غير متوفر أو دالة fixProfitCalculation غير موجودة');
      }

      // استدعاء دالة إصلاح حساب الأرباح
      const result = cashboxManager.fixProfitCalculation();

      // تسجيل نتيجة العملية
      if (result && result.success) {
        logSystem('تم إصلاح حساب الأرباح في الخزينة بنجاح', 'info');
        logSystem(`قيم الخزينة بعد الإصلاح: ${JSON.stringify(result.cashbox)}`, 'info');
      } else {
        const errorMsg = result ? result.error : 'سبب غير معروف';
        logSystem(`فشل في إصلاح حساب الأرباح في الخزينة: ${errorMsg}`, 'error');
      }

      return result;
    } catch (error) {
      logError(error, 'fix-profit-calculation');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء إصلاح حساب الأرباح في الخزينة'
      };
    }
  });

  // إعادة حساب الأرباح يدوياً
  ipcMain.handle('recalculate-profits', async () => {
    try {
      logSystem('استدعاء recalculate-profits', 'info');

      // استيراد مدير المعاملات الموحد
      const transactionManager = require('./unified-transaction-manager');

      // التحقق من وجود الدوال المطلوبة
      if (!transactionManager ||
          typeof transactionManager.recalculateTotalProfits !== 'function' ||
          typeof transactionManager.updateProfitTotalInDatabase !== 'function') {
        throw new Error('مدير المعاملات الموحد غير متوفر أو الدوال المطلوبة غير موجودة');
      }

      // إعادة حساب الأرباح
      const totalProfit = transactionManager.recalculateTotalProfits();
      logSystem(`إجمالي الأرباح المحسوب: ${totalProfit}`, 'info');

      // تحديث قاعدة البيانات
      const updateSuccess = transactionManager.updateProfitTotalInDatabase(totalProfit);

      if (updateSuccess) {
        logSystem('تم تحديث الأرباح بنجاح', 'info');

        // إرسال إشعار بتحديث الخزينة
        const eventSystem = require('./event-system');
        eventSystem.notifyCashboxUpdated({
          profit_total: totalProfit,
          operation: 'recalculate-profits',
          success: true,
          instant_update: true
        });

        return {
          success: true,
          totalProfit: totalProfit,
          message: 'تم إعادة حساب وتحديث الأرباح بنجاح'
        };
      } else {
        logSystem('فشل في تحديث الأرباح في قاعدة البيانات', 'error');
        return {
          success: false,
          error: 'فشل في تحديث الأرباح في قاعدة البيانات'
        };
      }
    } catch (error) {
      logError(error, 'recalculate-profits');
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء إعادة حساب الأرباح'
      };
    }
  });

  logSystem('تم تسجيل معالجات الخزينة بنجاح', 'info');
}

/**
 * تسجيل معالجات التقارير
 */
function registerReportsHandlers() {
  logSystem('بدء تسجيل معالجات التقارير...', 'info');

  // التأكد من تهيئة مدير التقارير
  if (!reportsManager) {
    logSystem('مدير التقارير غير متوفر، محاولة إعادة استيراده...', 'warning');
       try {
      // محاولة إعادة استيراد مدير التقارير
      const ReportsManager = require('./reports-manager');
      if (ReportsManager) {
        reportsManager = ReportsManager;
        logSystem('تم إعادة استيراد مدير التقارير بنجاح', 'info');
      } else {
        throw new Error('فشل في إعادة استيراد مدير التقارير');
      }
    } catch (importError) {
      logError(importError, 'registerReportsHandlers - import');
      logSystem('فشل في إعادة استيراد مدير التقارير: ' + importError.message, 'error');
      throw importError;
    }
  }

  // تهيئة مدير التقارير إذا لم يكن مهيأ بالفعل
  try {
    if (typeof reportsManager.initialize === 'function') {
      const initResult = reportsManager.initialize();
      logSystem(`نتيجة تهيئة مدير التقارير: ${initResult ? 'نجاح' : 'فشل'}`, 'info');
    } else {
      logSystem('دالة التهيئة غير متوفرة في مدير التقارير', 'warning');
    }
  } catch (initError) {
    logError(initError, 'registerReportsHandlers - initialize');
    logSystem('فشل في تهيئة مدير التقارير: ' + initError.message, 'error');
  }

  // الحصول على تقرير المخزون
  ipcMain.handle('get-inventory-report', async () => {
    try {
      logSystem('استدعاء get-inventory-report', 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getInventoryReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getInventoryReport غير موجودة');
      }

      const result = reportsManager.getInventoryReport();
      logSystem(`تم الحصول على تقرير المخزون: ${result.inventory.length} صنف`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-inventory-report');
      logSystem(`خطأ في الحصول على تقرير المخزون: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        inventory: [],
        stats: { totalItems: 0, totalValue: 0, lowStockItems: 0, outOfStockItems: 0 },
        error: error.message
      };
    }
  });

  // الحصول على تقرير المعاملات
  ipcMain.handle('get-transactions-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-transactions-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getTransactionsReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getTransactionsReport غير موجودة');
      }

      const result = reportsManager.getTransactionsReport(filters);
      logSystem(`تم الحصول على تقرير المعاملات: ${result.transactions.length} معاملة`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-transactions-report');
      logSystem(`خطأ في الحصول على تقرير المعاملات: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        transactions: [],
        stats: { totalTransactions: 0, totalPurchases: 0, totalSales: 0, totalProfit: 0 },
        error: error.message
      };
    }
  });

  // الحصول على تقرير الأرباح
  ipcMain.handle('get-profits-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-profits-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getProfitsReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getProfitsReport غير موجودة');
      }

      const result = reportsManager.getProfitsReport(filters);
      logSystem(`تم الحصول على تقرير الأرباح: ${result.transactions.length} معاملة، إجمالي الربح: ${result.stats.totalProfit}`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-profits-report');
      logSystem(`خطأ في الحصول على تقرير الأرباح: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        transactions: [],
        stats: { totalSales: 0, totalProfit: 0, totalQuantity: 0, profitMargin: 0, transactionCount: 0 },
        topItems: [],
        topCustomers: [],
        error: error.message
      };
    }
  });

  // الحصول على تقرير مبيعات العملاء الفرعيين
  ipcMain.handle('get-sub-customers-sales-report', async (event, parentId, filters) => {
    try {
      logSystem(`استدعاء get-sub-customers-sales-report للعميل: ${parentId} مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getSubCustomersSalesReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getSubCustomersSalesReport غير موجودة');
      }

      // التحقق من صحة معرف العميل الرئيسي
      if (!parentId) {
        throw new Error('معرف العميل الرئيسي مطلوب');
      }

      const result = reportsManager.getSubCustomersSalesReport(parentId, filters);
      logSystem(`تم الحصول على تقرير مبيعات العملاء الفرعيين: ${result.subCustomers.length} عميل فرعي`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-sub-customers-sales-report');
      logSystem(`خطأ في الحصول على تقرير مبيعات العملاء الفرعيين: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        parent: null,
        subCustomers: [],
        stats: { totalSubCustomers: 0, totalSales: 0, totalProfit: 0 },
        error: error.message
      };
    }
  });

  // الحصول على تقرير الخزينة
  ipcMain.handle('get-cashbox-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-cashbox-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager) {
        logSystem('مدير التقارير غير متوفر، محاولة إعادة استيراده...', 'warning');
        try {
          // محاولة إعادة استيراد مدير التقارير
          const ReportsManager = require('./reports-manager');
          if (ReportsManager) {
            reportsManager = ReportsManager;
            logSystem('تم إعادة استيراد مدير التقارير بنجاح', 'info');
          } else {
            throw new Error('فشل في إعادة استيراد مدير التقارير');
          }
        } catch (importError) {
          logError(importError, 'get-cashbox-report - import');
          throw new Error('فشل في استيراد مدير التقارير: ' + importError.message);
        }
      }

      // محاولة استخدام مدير الخزينة مباشرة إذا كان متاحًا
      const cashboxManager = require('./cashbox-manager');
      if (!cashboxManager) {
        logSystem('مدير الخزينة غير متوفر، سيتم استخدام مدير التقارير', 'warning');
      }

      // الحصول على معلومات الخزينة
      let cashbox;
      try {
        if (cashboxManager && typeof cashboxManager.getCashbox === 'function') {
          cashbox = await cashboxManager.getCashbox();
          logSystem(`تم الحصول على معلومات الخزينة من مدير الخزينة: ${JSON.stringify(cashbox)}`, 'info');
        } else {
          // استخدام مدير التقارير كبديل
          if (typeof reportsManager.getCashboxReport !== 'function') {
            throw new Error('دالة getCashboxReport غير موجودة في مدير التقارير');
          }

          const reportResult = reportsManager.getCashboxReport(filters);
          cashbox = reportResult.cashbox;
          logSystem(`تم الحصول على معلومات الخزينة من مدير التقارير: ${JSON.stringify(cashbox)}`, 'info');
        }
      } catch (cashboxError) {
        logError(cashboxError, 'get-cashbox-report - getCashbox');
        logSystem('فشل في الحصول على معلومات الخزينة، سيتم استخدام قيم افتراضية', 'warning');

        // إنشاء كائن خزينة افتراضي
        cashbox = {
          id: 0,
          initial_balance: 0,
          current_balance: 0,
          profit_total: 0,
          sales_total: 0,
          purchases_total: 0,
          returns_total: 0,
          transport_total: 0,
          last_updated: new Date().toISOString()
        };
      }

      // الحصول على معاملات الخزينة
      let transactions = [];
      try {
        if (cashboxManager && typeof cashboxManager.getTransactions === 'function') {
          // تمرير الفلاتر إلى مدير الخزينة
          transactions = await cashboxManager.getTransactions(filters);
          logSystem(`تم الحصول على ${transactions.length} معاملة من مدير الخزينة`, 'info');
        } else {
          // استخدام مدير التقارير كبديل
          const reportResult = reportsManager.getCashboxReport(filters);
          transactions = reportResult.transactions || [];
          logSystem(`تم الحصول على ${transactions.length} معاملة من مدير التقارير`, 'info');
        }
      } catch (transactionsError) {
        logError(transactionsError, 'get-cashbox-report - getTransactions');
        logSystem('فشل في الحصول على معاملات الخزينة، سيتم استخدام مصفوفة فارغة', 'warning');
      }

      // حساب إحصائيات الخزينة
      const stats = {
        totalIncome: 0,
        totalExpenses: 0,
        totalSales: cashbox.sales_total || 0,
        totalPurchases: cashbox.purchases_total || 0,
        totalReturns: cashbox.returns_total || 0,
        totalTransport: cashbox.transport_total || 0,
        totalProfit: cashbox.profit_total || 0,
        balance: cashbox.current_balance || 0,
        initialBalance: cashbox.initial_balance || 0
      };

      // تحديث الإحصائيات من المعاملات إذا كانت متوفرة
      if (transactions && transactions.length > 0) {
        for (const tx of transactions) {
          if (tx.type === 'income' || tx.type === 'sale') {
            stats.totalIncome += tx.amount || 0;
          } else if (tx.type === 'expense' || tx.type === 'purchase') {
            stats.totalExpenses += tx.amount || 0;
          } else if (tx.source === 'transport') {
            stats.totalTransport += tx.amount || 0;
          }
        }
      }

      // إنشاء النتيجة النهائية
      const result = {
        success: true,
        cashbox,
        transactions,
        stats
      };

      logSystem(`تم إنشاء تقرير الخزينة بنجاح: ${transactions.length} معاملة`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-cashbox-report');
      logSystem(`خطأ في الحصول على تقرير الخزينة: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        success: false,
        cashbox: {
          id: 0,
          initial_balance: 0,
          current_balance: 0,
          profit_total: 0,
          sales_total: 0,
          purchases_total: 0,
          returns_total: 0,
          transport_total: 0,
          last_updated: new Date().toISOString()
        },
        transactions: [],
        stats: {
          totalIncome: 0,
          totalExpenses: 0,
          totalSales: 0,
          totalPurchases: 0,
          totalReturns: 0,
          totalTransport: 0,
          totalProfit: 0,
          balance: 0,
          initialBalance: 0
        },
        error: error.message
      };
    }
  });

  // الحصول على تقرير الأصناف الأكثر مبيعًا
  ipcMain.handle('get-top-selling-items-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-top-selling-items-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getTopSellingItemsReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getTopSellingItemsReport غير موجودة');
      }

      const result = reportsManager.getTopSellingItemsReport(filters);
      logSystem(`تم الحصول على تقرير الأصناف الأكثر مبيعًا: ${result.items.length} صنف`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-top-selling-items-report');
      logSystem(`خطأ في الحصول على تقرير الأصناف الأكثر مبيعًا: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        items: [],
        stats: {
          totalItems: 0,
          totalQuantity: 0,
          totalSales: 0,
          totalProfit: 0,
          profitMargin: 0
        },
        error: error.message
      };
    }
  });

  // الحصول على تقرير العملاء الأكثر شراءً
  ipcMain.handle('get-top-customers-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-top-customers-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getTopCustomersReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getTopCustomersReport غير موجودة');
      }

      const result = reportsManager.getTopCustomersReport(filters);
      logSystem(`تم الحصول على تقرير العملاء الأكثر شراءً: ${result.customers.length} عميل`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-top-customers-report');
      logSystem(`خطأ في الحصول على تقرير العملاء الأكثر شراءً: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        customers: [],
        stats: {
          totalCustomers: 0,
          totalSales: 0,
          totalProfit: 0,
          totalTransactions: 0,
          totalInvoices: 0,
          profitMargin: 0,
          averageSalePerCustomer: 0
        },
        error: error.message
      };
    }
  });

  // الحصول على تقرير الأصناف التي تحتاج إلى إعادة طلب
  ipcMain.handle('get-low-stock-report', async (event, options) => {
    try {
      logSystem(`استدعاء get-low-stock-report مع الخيارات: ${JSON.stringify(options || {})}`, 'info');

      // التحقق من وجود مدير التقارير
      if (!reportsManager || typeof reportsManager.getLowStockReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getLowStockReport غير موجودة');
      }

      const result = reportsManager.getLowStockReport(options);
      logSystem(`تم الحصول على تقرير الأصناف التي تحتاج إلى إعادة طلب: ${result.items.length} صنف`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-low-stock-report');
      logSystem(`خطأ في الحصول على تقرير الأصناف التي تحتاج إلى إعادة طلب: ${error.message}`, 'error');

      // إرجاع كائن خطأ بدلاً من رمي استثناء
      return {
        items: [],
        stats: {
          totalItems: 0,
          totalValue: 0,
          totalQuantity: 0,
          zeroQuantityItems: 0,
          lowStockItems: 0
        },
        error: error.message
      };
    }
  });

  // الحصول على تقرير الإرجاعات
  ipcMain.handle('get-return-transactions-report', async (event, filters) => {
    try {
      logSystem(`استدعاء get-return-transactions-report مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');
      if (!reportsManager || typeof reportsManager.getReturnTransactionsReport !== 'function') {
        throw new Error('مدير التقارير غير متوفر أو دالة getReturnTransactionsReport غير موجودة');
      }
      const result = reportsManager.getReturnTransactionsReport(filters || {});
      logSystem(`تم الحصول على تقرير الإرجاعات: ${result.returnTransactions.length} عملية`, 'info');
      return result;
    } catch (error) {
      logError(error, 'get-return-transactions-report');
      logSystem(`خطأ في الحصول على تقرير الإرجاعات: ${error.message}`, 'error');
      return {
        returnTransactions: [],
        stats: { totalReturns: 0, totalReturnAmount: 0, totalReturnQuantity: 0, averageReturnAmount: 0 },
        error: error.message
      };
    }
  });

  // الحصول على فواتير العملاء
  ipcMain.handle('get-customer-invoices', async (event, filters) => {
    try {
      logSystem(`استدعاء get-customer-invoices مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق من وجود معرف العميل
      if (!filters || !filters.customerId) {
        throw new Error('معرف العميل مطلوب للحصول على الفواتير');
      }

      // الحصول على اتصال قاعدة البيانات
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // إنشاء استعلام SQL للحصول على فواتير العميل
      let query = `
        SELECT t.id, t.invoice_number, t.transaction_date as invoice_date,
               t.total_price as total_amount, t.payment_status, t.notes, t.customer_id,
               c.name as customer_name
        FROM transactions t
        LEFT JOIN customers c ON t.customer_id = c.id
        WHERE t.transaction_type = 'sale' AND t.customer_id = ?
      `;

      const queryParams = [filters.customerId];

      // إضافة فلتر التاريخ إذا كان موجودًا
      if (filters.startDate && filters.endDate) {
        query += ' AND t.transaction_date BETWEEN ? AND ?';
        queryParams.push(filters.startDate, filters.endDate);
      }

      // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
      query += ' ORDER BY t.transaction_date DESC';

      // تنفيذ الاستعلام
      const stmt = db.prepare(query);
      const invoices = stmt.all(...queryParams);

      // الحصول على تفاصيل كل فاتورة (الأصناف)
      const invoicesWithItems = await Promise.all(invoices.map(async (invoice) => {
        // استعلام للحصول على أصناف الفاتورة
        const itemsQuery = `
          SELECT t.id, t.transaction_id, t.item_id, t.quantity, t.price, t.total_price,
                 i.name as item_name, i.unit as barcode, i.name as description
          FROM transactions t
          LEFT JOIN items i ON t.item_id = i.id
          WHERE t.invoice_number = ?
        `;

        const itemsStmt = db.prepare(itemsQuery);
        const items = itemsStmt.all(invoice.invoice_number);

        // إضافة عدد الأصناف إلى الفاتورة
        return {
          ...invoice,
          items,
          items_count: items.length
        };
      }));

      // حساب إحصائيات الفواتير
      const totalAmount = invoicesWithItems.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);
      const paidInvoices = invoicesWithItems.filter(invoice => invoice.payment_status === 'paid').length;
      const unpaidInvoices = invoicesWithItems.length - paidInvoices;

      logSystem(`تم الحصول على فواتير العميل: ${invoicesWithItems.length} فاتورة للعميل ${filters.customerId}`, 'info');

      return {
        invoices: invoicesWithItems,
        stats: {
          totalInvoices: invoicesWithItems.length,
          totalAmount,
          paidInvoices,
          unpaidInvoices
        }
      };
    } catch (error) {
      logError(error, 'get-customer-invoices');
      logSystem(`خطأ في الحصول على فواتير العميل: ${error.message}`, 'error');

      return {
        invoices: [],
        stats: {
          totalInvoices: 0,
          totalAmount: 0,
          paidInvoices: 0,
          unpaidInvoices: 0
        },
        error: error.message
      };
    }
  });

  logSystem('تم تسجيل معالجات التقارير بنجاح', 'info');
}

// تصدير الوظائف
/**
 * تسجيل معالجات عمليات الإرجاع
 */
function registerReturnTransactionsHandlers() {
  try {
    logSystem('تسجيل معالجات عمليات الإرجاع', 'info');

    // استيراد معالجات عمليات الإرجاع
    try {
      const returnTransactionsHandlers = require('./return-transactions-handlers');

      // تسجيل معالجات عمليات الإرجاع
      try {
        returnTransactionsHandlers.registerReturnTransactionsHandlers();
        logSystem('تم استيراد وتسجيل معالجات عمليات الإرجاع بنجاح', 'info');
      } catch (registerError) {
        logError(registerError, 'تسجيل معالجات عمليات الإرجاع');
        logSystem('فشل في تسجيل بعض معالجات عمليات الإرجاع، سيتم المتابعة', 'warning');
      }
    } catch (handlersError) {
      logError(handlersError, 'استيراد معالجات عمليات الإرجاع');
      logSystem('فشل في استيراد معالجات عمليات الإرجاع، سيتم تسجيل المعالجات الأساسية فقط', 'warning');
    }

    // معالج تهيئة مدير عمليات الإرجاع
    ipcMain.handle('initialize-return-transactions-manager', async (event, database) => {
      try {
        logSystem('تهيئة مدير عمليات الإرجاع', 'info');
        const result = returnTransactionsManager.initialize(database);
        return { success: result };
      } catch (error) {
        logError(error, 'initialize-return-transactions-manager');
        return { success: false, error: error.message };
      }
    });

    // معالج إنشاء عملية إرجاع جديدة
    ipcMain.handle('create-return-transaction', async (event, returnData) => {
      try {
        logSystem(`إنشاء عملية إرجاع جديدة: ${JSON.stringify(returnData)}`, 'info');
        const result = await returnTransactionsManager.createReturnTransaction(returnData);
        return result;
      } catch (error) {
        logError(error, 'create-return-transaction');
        return { success: false, error: error.message };
      }
    });

    // معالج الحصول على الكمية المتاحة للإرجاع
    ipcMain.handle('get-available-quantity-for-return', async (event, { itemId, customerId, originalTransactionId }) => {
      try {
        logSystem(`الحصول على الكمية المتاحة للإرجاع للصنف ${itemId} والعميل ${customerId}`, 'info');
        const availableQuantity = await returnTransactionsManager.getAvailableQuantityForReturn(
          itemId,
          customerId,
          originalTransactionId
        );
        return { success: true, availableQuantity };
      } catch (error) {
        logError(error, 'get-available-quantity-for-return');
        return { success: false, availableQuantity: 0, error: error.message };
      }
    });

    // معالج الحصول على جميع عمليات الإرجاع
    ipcMain.handle('get-all-return-transactions', async (event, filters) => {
      try {
        logSystem(`الحصول على جميع عمليات الإرجاع مع الفلاتر: ${JSON.stringify(filters)}`, 'info');
        const returnTransactions = returnTransactionsManager.getAllReturnTransactions(filters);
        return { success: true, returnTransactions };
      } catch (error) {
        logError(error, 'get-all-return-transactions');
        return { success: false, returnTransactions: [], error: error.message };
      }
    });

    // معالج الحصول على عملية إرجاع بواسطة المعرف
    ipcMain.handle('get-return-transaction-by-id', async (event, returnId) => {
      try {
        logSystem(`الحصول على عملية الإرجاع بالمعرف: ${returnId}`, 'info');
        const returnTransaction = returnTransactionsManager.getReturnTransactionById(returnId);
        return { success: true, returnTransaction };
      } catch (error) {
        logError(error, 'get-return-transaction-by-id');
        return { success: false, returnTransaction: null, error: error.message };
      }
    });

    // معالج الحصول على عمليات الإرجاع للعميل
    ipcMain.handle('get-customer-return-transactions', async (event, customerId) => {
      try {
        logSystem(`الحصول على عمليات الإرجاع للعميل: ${customerId}`, 'info');
        const returnTransactions = returnTransactionsManager.getCustomerReturnTransactions(customerId);
        return { success: true, returnTransactions };
      } catch (error) {
        logError(error, 'get-customer-return-transactions');
        return { success: false, returnTransactions: [], error: error.message };
      }
    });

    // معالج مسح التخزين المؤقت لعمليات الإرجاع
    ipcMain.handle('clear-return-transactions-cache', async () => {
      try {
        logSystem('مسح التخزين المؤقت لعمليات الإرجاع', 'info');
        returnTransactionsManager.clearReturnTransactionsCache();
        return { success: true };
      } catch (error) {
        logError(error, 'clear-return-transactions-cache');
        return { success: false, error: error.message };
      }
    });

    // معالج إصلاح نظام الاسترجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('fix-retrieval-system')) {
        ipcMain.handle('fix-retrieval-system', async () => {
          try {
            logSystem('إصلاح نظام الاسترجاع', 'info');

            // استيراد وحدة إصلاح نظام الاسترجاع
            const { fixRetrievalSystem } = require('./fix-retrieval-system');

            // تنفيذ عملية الإصلاح
            const result = fixRetrievalSystem();

            logSystem(`نتيجة إصلاح نظام الاسترجاع: ${result.success ? 'نجاح' : 'فشل'} - ${result.message}`, 'info');

            return {
              success: result.success,
              message: result.message,
              details: result.details
            };
          } catch (error) {
            logError(error, 'fix-retrieval-system');
            return {
              success: false,
              message: `فشل في إصلاح نظام الاسترجاع: ${error.message}`,
              error: error.message
            };
          }
        });
      } else {
        logSystem('معالج fix-retrieval-system مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج fix-retrieval-system');
    }

    logSystem('تم تسجيل معالجات عمليات الإرجاع بنجاح', 'info');
  } catch (error) {
    logError(error, 'registerReturnTransactionsHandlers');
    throw error;
  }
}

/**
 * تسجيل معالج تسجيل الأخطاء
 */
function registerLogHandlers() {
  try {
    logSystem('تسجيل معالجات تسجيل الأخطاء', 'info');

    // معالج تسجيل الأخطاء
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('log-error')) {
        ipcMain.handle('log-error', async (event, errorData) => {
          try {
            // التحقق من صحة البيانات
            if (!errorData) {
              console.error('بيانات الخطأ غير موجودة');
              return { success: false, error: 'بيانات الخطأ غير موجودة' };
            }

            // إنشاء كائن خطأ صالح
            let errorObject;
            if (errorData.error && errorData.error instanceof Error) {
              errorObject = errorData.error;
            } else if (errorData.error && typeof errorData.error === 'object') {
              // إنشاء خطأ جديد من كائن الخطأ
              errorObject = new Error(errorData.error.message || errorData.message || 'خطأ غير معروف');
              if (errorData.error.stack) {
                errorObject.stack = errorData.error.stack;
              }
            } else {
              errorObject = new Error(errorData.message || 'خطأ غير معروف');
            }

            // تسجيل الخطأ
            logError(errorObject, errorData.context || 'log-error');
            return { success: true };
          } catch (error) {
            console.error('خطأ في تسجيل الخطأ:', error);
            return { success: false, error: error.message };
          }
        });
      } else {
        logSystem('معالج log-error مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      console.error('خطأ في تسجيل معالج log-error:', error);
    }

    logSystem('تم تسجيل معالجات تسجيل الأخطاء بنجاح', 'info');
  } catch (error) {
    console.error('خطأ في تسجيل معالجات تسجيل الأخطاء:', error);
  }
}

/**
 * تسجيل معالجات الإعدادات
 */
function registerSettingsHandlers() {
  try {
    logSystem('تسجيل معالجات الإعدادات', 'info');

    // معالج للحصول على الإعدادات
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-settings')) {
        ipcMain.handle('get-settings', async () => {
      try {
        logSystem('الحصول على الإعدادات', 'info');

        // الحصول على اتصال قاعدة البيانات
        const dbManager = require('./database-singleton').getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
        }

        // التحقق من وجود جدول settings
        const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'");
        const tableExists = tableCheckStmt.get();

        if (!tableExists) {
          logSystem('جدول settings غير موجود، جاري إنشاؤه...', 'info');

          // إنشاء جدول settings
          db.exec(`
            CREATE TABLE IF NOT EXISTS settings (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              key TEXT NOT NULL UNIQUE,
              value TEXT,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // إنشاء فهرس للمفتاح
          db.exec('CREATE UNIQUE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');

          logSystem('تم إنشاء جدول settings بنجاح', 'info');

          // إضافة الإعدادات الافتراضية
          const defaultSettings = {
            systemName: 'شركة أثاث غروب',
            address: 'للتصميم و تصنيع الأثاث والديكورات',
            phone: '+218 92-3000151',
            logoUrl: '',
            lowStockNotification: true,
            defaultMinimumQuantity: 5,
            integrationEnabled: false,
            integrationUrl: '',
            integrationApiKey: '',
            syncInterval: 30,
            isInternetConnection: false
          };

          // إضافة الإعدادات الافتراضية إلى قاعدة البيانات
          const insertStmt = db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)');

          for (const [key, value] of Object.entries(defaultSettings)) {
            insertStmt.run(key, JSON.stringify(value));
          }

          logSystem('تم إضافة الإعدادات الافتراضية بنجاح', 'info');
          return defaultSettings;
        }

        // الحصول على جميع الإعدادات
        const stmt = db.prepare('SELECT key, value FROM settings');
        const settingsRows = stmt.all();

        // تحويل الصفوف إلى كائن
        const settings = {};
        for (const row of settingsRows) {
          try {
            // معالجة خاصة لصورة الشعار
            if (row.key === 'logoUrl') {
              logSystem(`تم العثور على إعداد صورة الشعار، طول البيانات: ${row.value ? row.value.length : 0} حرف`, 'info');

              try {
                const logoValue = JSON.parse(row.value);

                // التحقق من صحة البيانات
                if (typeof logoValue === 'string' && logoValue.startsWith('data:image/')) {
                  logSystem('صورة الشعار صالحة', 'info');
                  settings[row.key] = logoValue;
                } else {
                  logSystem('تنسيق صورة الشعار غير صالح، سيتم استخدام قيمة فارغة', 'warn');
                  settings[row.key] = '';
                }
              } catch (logoError) {
                logError(logoError, 'فشل في تحليل صورة الشعار');
                settings[row.key] = '';
              }
            } else {
              // معالجة باقي الإعدادات
              settings[row.key] = JSON.parse(row.value);
            }
          } catch (parseError) {
            logError(parseError, `خطأ في تحليل قيمة الإعداد ${row.key}`);
            settings[row.key] = row.value;
          }
        }

        // إضافة الإعدادات الافتراضية إذا كانت غير موجودة
        const defaultSettings = {
          systemName: 'شركة أثاث غروب',
          address: 'للتصميم و تصنيع الأثاث والديكورات',
          phone: '+218 92-3000151',
          logoUrl: '',
          lowStockNotification: true,
          defaultMinimumQuantity: 5,
          integrationEnabled: false,
          integrationUrl: '',
          integrationApiKey: '',
          syncInterval: 30,
          isInternetConnection: false
        };

        // دمج الإعدادات الافتراضية مع الإعدادات المخزنة
        const mergedSettings = { ...defaultSettings, ...settings };

        return mergedSettings;
      } catch (error) {
        logError(error, 'get-settings');
        return {
          systemName: 'شركة أثاث غروب',
          address: 'للتصميم و تصنيع الأثاث والديكورات',
          phone: '+218 92-3000151',
          logoUrl: '',
          lowStockNotification: true,
          defaultMinimumQuantity: 5,
          integrationEnabled: false,
          integrationUrl: '',
          integrationApiKey: '',
          syncInterval: 30,
          isInternetConnection: false
        };
      }
    });
      } else {
        logSystem('معالج get-settings مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-settings');
    }

    // معالج لتحديث الإعدادات
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('update-settings')) {
        ipcMain.handle('update-settings', async (event, settings) => {
      try {
        logSystem('تحديث الإعدادات', 'info');

        // سجل معلومات عن الإعدادات المراد تحديثها (بدون عرض البيانات الكبيرة)
        const settingsInfo = Object.keys(settings).reduce((acc, key) => {
          if (key === 'logoUrl') {
            acc[key] = settings[key] ? `[Data URL - ${settings[key].length} bytes]` : '';
          } else {
            acc[key] = settings[key];
          }
          return acc;
        }, {});

        logSystem(`الإعدادات المراد تحديثها: ${JSON.stringify(settingsInfo)}`, 'info');

        // الحصول على اتصال قاعدة البيانات
        const dbManager = require('./database-singleton').getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          const errorMsg = 'فشل في الحصول على اتصال قاعدة البيانات';
          logSystem(errorMsg, 'error');
          throw new Error(errorMsg);
        }

        logSystem('تم الحصول على اتصال قاعدة البيانات بنجاح', 'info');

        // التحقق من وجود جدول settings
        const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'");
        const tableExists = tableCheckStmt.get();

        if (!tableExists) {
          logSystem('جدول settings غير موجود، جاري إنشاؤه...', 'info');

          // إنشاء جدول settings
          db.exec(`
            CREATE TABLE IF NOT EXISTS settings (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              key TEXT NOT NULL UNIQUE,
              value TEXT,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // إنشاء فهرس للمفتاح
          db.exec('CREATE UNIQUE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');

          logSystem('تم إنشاء جدول settings بنجاح', 'info');
        } else {
          logSystem('جدول settings موجود بالفعل', 'info');
        }

        // تحديث الإعدادات
        const updateStmt = db.prepare('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)');

        let updatedCount = 0;
        for (const [key, value] of Object.entries(settings)) {
          try {
            const jsonValue = JSON.stringify(value);
            logSystem(`تحديث الإعداد: ${key}, طول القيمة: ${jsonValue.length} حرف`, 'info');

            const result = updateStmt.run(key, jsonValue);
            if (result.changes > 0) {
              updatedCount++;
              logSystem(`تم تحديث الإعداد: ${key} بنجاح`, 'info');
            } else {
              logSystem(`لم يتم تحديث الإعداد: ${key}، لا توجد تغييرات`, 'warn');
            }
          } catch (updateError) {
            logError(updateError, `فشل في تحديث الإعداد: ${key}`);
            throw updateError;
          }
        }

        logSystem(`تم تحديث ${updatedCount} من الإعدادات بنجاح`, 'info');
        return { success: true, settings, updatedCount };
      } catch (error) {
        logError(error, 'update-settings');
        return { success: false, error: error.message };
      }
    });
      } else {
        logSystem('معالج update-settings مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج update-settings');
    }

    // معالج للحصول على السمة
    ipcMain.handle('get-theme', async () => {
      try {
        logSystem('الحصول على السمة', 'info');

        // الحصول على اتصال قاعدة البيانات
        const dbManager = require('./database-singleton').getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
        }

        // الحصول على السمة
        const stmt = db.prepare('SELECT value FROM settings WHERE key = ?');
        const row = stmt.get('theme');

        if (!row) {
          return { theme: 'light' };
        }

        try {
          return { theme: JSON.parse(row.value) };
        } catch (parseError) {
          return { theme: row.value };
        }
      } catch (error) {
        logError(error, 'get-theme');
        return { theme: 'light' };
      }
    });

    // معالج لتعيين السمة
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('set-theme')) {
        ipcMain.handle('set-theme', async (event, theme) => {
      try {
        logSystem(`تعيين السمة: ${theme}`, 'info');

        // الحصول على اتصال قاعدة البيانات
        const dbManager = require('./database-singleton').getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
        }

        // تحديث السمة
        const stmt = db.prepare('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)');
        stmt.run('theme', JSON.stringify(theme));

        logSystem('تم تحديث السمة بنجاح', 'info');
        return { success: true, theme };
      } catch (error) {
        logError(error, 'set-theme');
        return { success: false, error: error.message };
      }
    });
      } else {
        logSystem('معالج set-theme مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج set-theme');
    }

    // معالج لاختبار الاتصال بمنظومة المبيعات
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('test-integration-connection')) {
        ipcMain.handle('test-integration-connection', async (event, { url, apiKey, isInternet }) => {
      try {
        logSystem(`اختبار الاتصال بمنظومة المبيعات: ${url} (${isInternet ? 'إنترنت' : 'شبكة محلية'})`, 'info');

        // محاكاة اختبار الاتصال
        return new Promise((resolve) => {
          setTimeout(() => {
            // محاكاة نجاح الاتصال
            if (url && apiKey) {
              resolve({
                success: true,
                message: `تم الاتصال بنجاح بمنظومة المبيعات عبر ${isInternet ? 'الإنترنت' : 'الشبكة المحلية'}`
              });
            } else {
              resolve({
                success: false,
                message: 'فشل الاتصال: يرجى التحقق من عنوان URL ومفتاح API'
              });
            }
          }, 1500);
        });
      } catch (error) {
        logError(error, 'test-integration-connection');
        return { success: false, error: error.message };
      }
    });
      } else {
        logSystem('معالج test-integration-connection مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج test-integration-connection');
    }

    // معالج لمزامنة النظام مع منظومة المبيعات
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('sync-with-sales-system')) {
        ipcMain.handle('sync-with-sales-system', async (event, options) => {
      try {
        logSystem(`مزامنة النظام مع منظومة المبيعات: ${options.enabled ? 'مفعل' : 'غير مفعل'}`, 'info');

        // محاكاة المزامنة
        return new Promise((resolve) => {
          setTimeout(() => {
            // محاكاة نجاح المزامنة
            if (options.enabled && options.url && options.apiKey) {
              resolve({
                success: true,
                message: 'تم تفعيل المزامنة بنجاح',
                syncStatus: {
                  enabled: true,
                  lastSyncTime: new Date().toISOString(),
                  lastSyncStatus: 'success',
                  syncing: false
                }
              });
            } else if (!options.enabled) {
              resolve({
                success: true,
                message: 'تم إلغاء تفعيل المزامنة بنجاح',
                syncStatus: {
                  enabled: false,
                  lastSyncTime: null,
                  lastSyncStatus: null,
                  syncing: false
                }
              });
            } else {
              resolve({
                success: false,
                message: 'فشل في تفعيل المزامنة: يرجى التحقق من عنوان URL ومفتاح API',
                syncStatus: {
                  enabled: false,
                  lastSyncTime: null,
                  lastSyncStatus: 'error',
                  syncing: false
                }
              });
            }
          }, 1500);
        });
      } catch (error) {
        logError(error, 'sync-with-sales-system');
        return {
          success: false,
          error: error.message,
          syncStatus: {
            enabled: false,
            lastSyncTime: null,
            lastSyncStatus: 'error',
            syncing: false
          }
        };
      }
    });
      } else {
        logSystem('معالج sync-with-sales-system مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج sync-with-sales-system');
    }

    // تم إزالة معالجات Google Drive

    logSystem('تم تسجيل معالجات الإعدادات بنجاح', 'info');
  } catch (error) {
    logError(error, 'registerSettingsHandlers');
    throw error;
  }
}

module.exports = {
  registerAllHandlers
};
