import React, { useState } from 'react';
import {
  FaBoxes, FaExclamationTriangle, FaArrowCircleDown, FaArrowCircleUp,
  FaChartLine, FaMoneyBillWave, FaUserFriends, FaTools, FaUndo, FaCheck, FaTimes
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import {
  MonthlySalesChart,
  InventoryByCategoryChart,
  TopSellingItemsChart,
  MonthlyProfitChart,
  InventoryStatusChart
} from '../components/charts/DashboardCharts';
import Card from '../components/Card';
import Button from '../components/Button';
import FormattedCurrency from '../components/FormattedCurrency';
import DataTable from '../components/DataTable';
import { getCustomerNameById } from '../utils/customerUtils';
import './Dashboard.css';

const Dashboard = () => {
  // استخدام سياق التطبيق
  const { inventory, transactions, customers, loading, calculateProfits } = useApp();

  // حالة إصلاح نظام الاسترجاع
  const [isFixingSystem, setIsFixingSystem] = useState(false);
  const [fixSystemResult, setFixSystemResult] = useState(null);

  // حساب إحصائيات المخزون
  const calculateInventoryStats = () => {
    if (!inventory || inventory.length === 0) return { total: 0, low: 0, out: 0 };

    const total = inventory.length;
    const low = inventory.filter(item => item.current_quantity <= item.minimum_quantity && item.current_quantity > 0).length;
    const out = inventory.filter(item => item.current_quantity === 0).length;

    return { total, low, out };
  };

  // حساب إحصائيات المعاملات
  const calculateTransactionStats = () => {
    if (!transactions || transactions.length === 0) return { receiving: 0, withdrawal: 0, purchases: 0, sales: 0 };

    const receiving = transactions.filter(t => t.transaction_type === 'receiving').length;
    const withdrawal = transactions.filter(t => t.transaction_type === 'withdrawal').length;
    const purchases = transactions.filter(t => t.transaction_type === 'purchase').length;
    const sales = transactions.filter(t => t.transaction_type === 'sale').length;

    return { receiving, withdrawal, purchases, sales };
  };

  // الحصول على آخر المعاملات
  const getRecentTransactions = () => {
    if (!transactions || transactions.length === 0) return [];

    // ترتيب المعاملات حسب التاريخ (الأحدث أولاً) وأخذ آخر 10 معاملات
    return [...transactions]
      .sort((a, b) => new Date(b.transaction_date) - new Date(a.transaction_date))
      .slice(0, 10);
  };

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // إعداد بيانات الرسوم البيانية
  const prepareMonthlySalesData = () => {
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    const currentYear = new Date().getFullYear();

    // تهيئة مصفوفات البيانات
    const salesData = Array(12).fill(0);
    const purchasesData = Array(12).fill(0);

    // حساب المبيعات والمشتريات الشهرية
    transactions.forEach(transaction => {
      const date = new Date(transaction.transaction_date);
      if (date.getFullYear() === currentYear) {
        const month = date.getMonth();
        if (transaction.transaction_type === 'sale') {
          salesData[month] += transaction.total_price || (transaction.price * transaction.quantity) || 0;
        } else if (transaction.transaction_type === 'purchase') {
          purchasesData[month] += transaction.total_price || (transaction.price * transaction.quantity) || 0;
        }
      }
    });

    return {
      labels: months,
      sales: salesData,
      purchases: purchasesData
    };
  };

  const prepareInventoryByCategoryData = () => {
    const categories = {};

    // حساب الكميات حسب الفئة
    inventory.forEach(item => {
      const category = item.category || 'غير مصنف';
      if (!categories[category]) {
        categories[category] = 0;
      }
      categories[category] += item.current_quantity || 0;
    });

    // تحويل البيانات إلى تنسيق مناسب للرسم البياني
    const labels = Object.keys(categories);
    const values = Object.values(categories);

    return { labels, values };
  };

  const prepareTopSellingItemsData = () => {
    // إنشاء قاموس لتجميع المبيعات حسب الصنف
    const itemSales = {};

    // حساب إجمالي المبيعات لكل صنف
    transactions.forEach(transaction => {
      if (transaction.transaction_type === 'sale') {
        const itemName = transaction.item_name;
        if (!itemSales[itemName]) {
          itemSales[itemName] = 0;
        }
        itemSales[itemName] += transaction.quantity || 0;
      }
    });

    // تحويل البيانات إلى مصفوفة وترتيبها تنازلياً
    const sortedItems = Object.entries(itemSales)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5); // أخذ أعلى 5 أصناف

    return {
      labels: sortedItems.map(item => item[0]),
      values: sortedItems.map(item => item[1])
    };
  };

  const prepareMonthlyProfitData = () => {
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    const currentYear = new Date().getFullYear();

    // تهيئة مصفوفات البيانات
    const revenueData = Array(12).fill(0);
    const costsData = Array(12).fill(0);
    const profitData = Array(12).fill(0);

    // حساب الإيرادات والتكاليف الشهرية
    transactions.forEach(transaction => {
      const date = new Date(transaction.transaction_date);
      if (date.getFullYear() === currentYear) {
        const month = date.getMonth();
        const amount = transaction.total_price || (transaction.price * transaction.quantity) || 0;

        if (transaction.transaction_type === 'sale') {
          revenueData[month] += amount;
          profitData[month] += transaction.profit || 0;
        } else if (transaction.transaction_type === 'purchase') {
          costsData[month] += amount;
        }
      }
    });

    return {
      labels: months,
      revenue: revenueData,
      costs: costsData,
      profit: profitData
    };
  };

  const prepareInventoryStatusData = () => {
    const inStock = inventory.filter(item => item.current_quantity > item.minimum_quantity).length;
    const lowStock = inventory.filter(item => item.current_quantity <= item.minimum_quantity && item.current_quantity > 0).length;
    const outOfStock = inventory.filter(item => item.current_quantity === 0).length;

    return { inStock, lowStock, outOfStock };
  };

  // الحصول على الإحصائيات
  const inventoryStats = calculateInventoryStats();
  const transactionStats = calculateTransactionStats();
  const recentTransactions = getRecentTransactions();
  const profitStats = calculateProfits();

  // إعداد بيانات الرسوم البيانية
  const monthlySalesData = prepareMonthlySalesData();
  const inventoryCategoryData = prepareInventoryByCategoryData();
  const topSellingItemsData = prepareTopSellingItemsData();
  const monthlyProfitData = prepareMonthlyProfitData();
  const inventoryStatusData = prepareInventoryStatusData();

  // إصلاح نظام الاسترجاع
  const handleFixRetrievalSystem = async () => {
    try {
      setIsFixingSystem(true);
      setFixSystemResult(null);

      // استدعاء API لإصلاح نظام الاسترجاع
      const result = await window.api.invoke('fix-retrieval-system');

      setFixSystemResult({
        success: result.success,
        message: result.message || (result.success ? 'تم إصلاح نظام الاسترجاع بنجاح' : 'فشل في إصلاح نظام الاسترجاع')
      });

      // تحديث واجهة المستخدم
      try {
        await window.api.invoke('refresh-needed', { target: 'all' });
        console.log('تم إرسال إشعار بتحديث جميع البيانات');
      } catch (refreshError) {
        console.warn('تحذير: فشل في إرسال إشعار التحديث:', refreshError);
      }
    } catch (err) {
      console.error('خطأ في إصلاح نظام الاسترجاع:', err);
      setFixSystemResult({
        success: false,
        message: `خطأ في إصلاح نظام الاسترجاع: ${err.message || 'سبب غير معروف'}`
      });
    } finally {
      setIsFixingSystem(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="dashboard-page">
      <div className="dashboard-welcome">
        <h1>لوحة التحكم</h1>
        <p>مرحباً بك في نظام إدارة المخازن - اتش قروب</p>
      </div>

      {/* إحصائيات المخزون */}
      <div className="stats-grid">
        <Card
          className="stat-card-wrapper"
          noPadding
          noShadow
        >
          <div className="stat-card">
            <div className="stat-card-icon">
              <FaBoxes />
            </div>
            <div className="stat-card-value">{inventoryStats.total}</div>
            <div className="stat-card-title">إجمالي الأصناف</div>
          </div>
        </Card>

        <Card
          className="stat-card-wrapper"
          noPadding
          noShadow
        >
          <div className="stat-card">
            <div className="stat-card-icon" style={{ backgroundColor: 'rgba(243, 156, 18, 0.1)', color: 'var(--warning-color)' }}>
              <FaExclamationTriangle />
            </div>
            <div className="stat-card-value">{inventoryStats.low}</div>
            <div className="stat-card-title">أصناف تحت الحد الأدنى</div>
          </div>
        </Card>

        <Card
          className="stat-card-wrapper"
          noPadding
          noShadow
        >
          <div className="stat-card">
            <div className="stat-card-icon" style={{ backgroundColor: 'rgba(52, 152, 219, 0.1)', color: 'var(--info-color)' }}>
              <FaArrowCircleDown />
            </div>
            <div className="stat-card-value">{transactionStats.purchases}</div>
            <div className="stat-card-title">عمليات الشراء</div>
          </div>
        </Card>

        <Card
          className="stat-card-wrapper"
          noPadding
          noShadow
        >
          <div className="stat-card">
            <div className="stat-card-icon" style={{ backgroundColor: 'rgba(230, 126, 34, 0.1)', color: 'var(--accent-color)' }}>
              <FaUserFriends />
            </div>
            <div className="stat-card-value">{customers ? customers.length : 0}</div>
            <div className="stat-card-title">العملاء</div>
          </div>
        </Card>
      </div>

      {/* الرسوم البيانية التفاعلية */}
      <div className="dashboard-charts">
        {/* الصف الأول من الرسوم البيانية */}
        <div className="charts-row">
          <Card
            className="chart-card"
            style={{ flex: 2 }}
            title="المبيعات والمشتريات الشهرية"
            icon={<FaChartLine />}
          >
            <div className="chart-content" style={{ height: '300px' }}>
              <MonthlySalesChart data={monthlySalesData} />
            </div>
          </Card>

          <Card
            className="chart-card"
            style={{ flex: 1 }}
            title="حالة المخزون"
            icon={<FaBoxes />}
          >
            <div className="chart-content" style={{ height: '300px' }}>
              <InventoryStatusChart data={inventoryStatusData} />
            </div>
          </Card>
        </div>

        {/* الصف الثاني من الرسوم البيانية */}
        <div className="charts-row">
          <Card
            className="chart-card"
            style={{ flex: 1 }}
            title="المخزون حسب الفئة"
            icon={<FaBoxes />}
          >
            <div className="chart-content" style={{ height: '300px' }}>
              <InventoryByCategoryChart data={inventoryCategoryData} />
            </div>
          </Card>

          <Card
            className="chart-card"
            style={{ flex: 1 }}
            title="الأصناف الأكثر مبيعاً"
            icon={<FaArrowCircleUp />}
          >
            <div className="chart-content" style={{ height: '300px' }}>
              <TopSellingItemsChart data={topSellingItemsData} />
            </div>
          </Card>
        </div>

        {/* الصف الثالث من الرسوم البيانية */}
        <div className="charts-row">
          <Card
            className="chart-card"
            style={{ flex: 1 }}
            title="الأرباح الشهرية"
            icon={<FaMoneyBillWave />}
          >
            <div className="chart-content" style={{ height: '300px' }}>
              <MonthlyProfitChart data={monthlyProfitData} />
            </div>
          </Card>
        </div>
      </div>

      {/* إحصائيات المخزون والكميات */}
      <Card
        title="إحصائيات المخزون والكميات"
        icon={<FaChartLine />}
        className="inventory-stats-card"
      >
        <div className="stats-grid">
          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(231, 76, 60, 0.1)', color: 'var(--danger-color)' }}>
                <FaExclamationTriangle />
              </div>
              <div className="stat-card-value">{inventoryStats.out}</div>
              <div className="stat-card-title">أصناف نفذت الكمية</div>
              <div className="stat-card-subtitle">بحاجة إلى إعادة تعبئة</div>
            </div>
          </Card>

          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(52, 152, 219, 0.1)', color: 'var(--info-color)' }}>
                <FaBoxes />
              </div>
              <div className="stat-card-value">{inventory.reduce((sum, item) => sum + (item.current_quantity || 0), 0)}</div>
              <div className="stat-card-title">إجمالي الكميات</div>
              <div className="stat-card-subtitle">في المخزون</div>
            </div>
          </Card>

          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(26, 58, 95, 0.1)', color: 'var(--primary-color)' }}>
                <FaChartLine />
              </div>
              <div className="stat-card-value">
                <FormattedCurrency amount={inventory.reduce((sum, item) => sum + (item.current_quantity * item.avg_price || 0), 0)} />
              </div>
              <div className="stat-card-title">قيمة المخزون</div>
              <div className="stat-card-subtitle">بسعر التكلفة</div>
            </div>
          </Card>

          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(39, 174, 96, 0.1)', color: 'var(--success-color)' }}>
                <FaMoneyBillWave />
              </div>
              <div className="stat-card-value">
                <FormattedCurrency amount={profitStats.yearly} />
              </div>
              <div className="stat-card-title">الربح السنوي</div>
              <div className="stat-card-subtitle">للعام الحالي</div>
            </div>
          </Card>
        </div>
      </Card>

      {/* آخر المعاملات */}
      <Card
        title="آخر المعاملات"
        icon={<FaChartLine />}
        className="recent-transactions-card"
        actions={
          <Button
            variant="light"
            size="sm"
            onClick={() => window.location.href = '/reports'}
          >
            عرض كل المعاملات
          </Button>
        }
      >
        <DataTable
          columns={[
            {
              header: 'التاريخ',
              accessor: 'transaction_date',
              cell: (row) => formatDate(row.transaction_date)
            },
            {
              header: 'نوع المعاملة',
              accessor: 'transaction_type',
              cell: (row) => {
                switch (row.transaction_type) {
                  case 'purchase':
                    return <span className="badge badge-primary">شراء</span>;
                  case 'sale':
                    return <span className="badge badge-success">بيع للعميل</span>;
                  default:
                    return <span className="badge badge-secondary">{row.transaction_type}</span>;
                }
              }
            },
            {
              header: 'الصنف',
              accessor: 'item_name',
              cell: (row) => <strong>{row.item_name}</strong>
            },
            {
              header: 'الكمية',
              accessor: 'quantity'
            },
            {
              header: 'التفاصيل',
              accessor: 'details',
              cell: (row) => {
                switch (row.transaction_type) {
                  case 'purchase':
                    return <FormattedCurrency amount={row.price} />;
                  case 'sale':
                    return (
                      <div>
                        <FormattedCurrency amount={row.price} />
                        {row.customer && <span className="customer-name"> إلى {getCustomerNameById(row.customer, customers)}</span>}
                      </div>
                    );
                  default:
                    return '';
                }
              }
            }
          ]}
          data={recentTransactions}
          pagination={false}
          searchable={false}
          emptyMessage="لا توجد معاملات مسجلة بعد"
          onRowClick={(row) => console.log('تم النقر على المعاملة:', row)}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
