import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter as Router, createHash<PERSON><PERSON><PERSON>, RouterProvider } from 'react-router-dom';
// Importar las banderas futuras
import { UNSAFE_ENHANCE_TRANSITION_GROUP, UNSAFE_getPathContributingMatches } from 'react-router-dom';
import App from './App';
import { AuthProvider } from './context/AuthContext';
import { InventoryProvider } from './context/InventoryContext';
import { ThemeProvider } from './context/ThemeContext';
// استيراد وتسجيل مكتبات PDF
import './utils/pdfUtils';
import './utils/pdfLibrarySetup';
import './utils/arabicPdfSupport';
import './index.css';

// إضافة وظيفة عالمية لتحديث بيانات العميل
// سيتم تعيين هذه الوظيفة في AppContext عند تهيئة السياق
window.updateCustomerData = null;

// Configurar las banderas futuras de React Router
window.__reactRouterFutureFlags = {
  v7_startTransition: true,
  v7_relativeSplatPath: true
};

ReactDOM.render(
  <React.StrictMode>
    <Router>
      <ThemeProvider>
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </Router>
  </React.StrictMode>,
  document.getElementById('root')
);
