/* تنسيقات تقارير العملاء الموحدة */
.unified-customer-reports {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
  height: 100%;
  --primary-color: #3498db;
  --primary-light: #e3f2fd;
  --primary-dark: #2980b9;
  --secondary-color: #2ecc71;
  --secondary-light: #e8f5e9;
  --secondary-dark: #27ae60;
  --warning-color: #f39c12;
  --warning-light: #fff8e1;
  --warning-dark: #f57c00;
  --danger-color: #e74c3c;
  --danger-light: #ffebee;
  --danger-dark: #c0392b;
  --text-dark: #2c3e50;
  --text-medium: #34495e;
  --text-light: #7f8c8d;
  --background-light: #f8f9fa;
  --background-medium: #ecf0f1;
  --border-color: #ddd;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
}

/* شريط الأدوات */
.reports-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--background-light);
  padding: 12px 18px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: box-shadow var(--transition-normal);
}

.reports-toolbar:hover {
  box-shadow: var(--shadow-md);
}

.view-selector {
  display: flex;
  gap: 12px;
}

.date-filter {
  display: flex;
  align-items: center;
}

.actions {
  display: flex;
  gap: 12px;
}

/* محتوى التقرير */
.report-content {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 24px;
  border: 1px solid var(--border-color);
  transition: box-shadow var(--transition-normal);
}

.report-content:hover {
  box-shadow: var(--shadow-md);
}

/* لوحة المعلومات */
.customer-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
  background-color: #fff;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 2.2rem;
  margin-left: 18px;
  color: var(--primary-color);
  background-color: var(--primary-light);
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
  background-color: var(--primary-color);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
  color: var(--text-dark);
  transition: color var(--transition-normal);
}

.stat-card:hover .stat-value {
  color: var(--primary-dark);
}

.stat-title {
  font-size: 1rem;
  color: var(--text-light);
  margin: 8px 0 0;
  transition: color var(--transition-normal);
}

/* بطاقة أعلى العملاء */
.top-customers-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  color: #2c3e50;
}

/* حالات التحميل والخطأ */
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  margin: 20px 0;
  transition: all var(--transition-normal);
}

.loading-state:hover, .error-state:hover {
  box-shadow: var(--shadow-md);
}

/* تحسين مؤشر التحميل */
.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  border-left-color: var(--primary-color);
  animation: spin 1s cubic-bezier(0.42, 0, 0.58, 1) infinite;
  margin-bottom: 24px;
  position: relative;
}

.spinner::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 1px solid rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.5; }
  100% { transform: scale(1); opacity: 1; }
}

.loading-state p {
  font-size: 1.1rem;
  color: var(--text-medium);
  margin: 0;
  font-weight: 500;
}

/* تحسين حالة الخطأ */
.error-state {
  background-color: var(--danger-light);
  border-color: var(--danger-color);
}

.error-state svg {
  font-size: 3.5rem;
  color: var(--danger-color);
  margin-bottom: 24px;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
  40%, 60% { transform: translate3d(3px, 0, 0); }
}

.error-state h3 {
  margin: 0 0 12px;
  color: var(--danger-dark);
  font-size: 1.4rem;
}

.error-state p {
  margin: 0 0 24px;
  color: var(--text-medium);
  font-size: 1.1rem;
  max-width: 500px;
}

/* تفاصيل العميل */
.customer-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.customer-info-card {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.customer-info-section {
  flex: 1;
  min-width: 250px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
  min-width: 120px;
  color: #7f8c8d;
}

.info-value {
  color: #2c3e50;
}

/* تحسين تنسيقات الجداول */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 20px 0;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  background-color: white;
}

.data-table th {
  background-color: var(--background-light);
  color: var(--text-medium);
  font-weight: 600;
  text-align: right;
  padding: 14px 16px;
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-dark);
  transition: background-color var(--transition-fast);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:hover td {
  background-color: var(--primary-light);
}

.data-table .numeric-cell {
  text-align: left;
  font-family: 'Roboto', 'Arial', sans-serif;
  direction: ltr;
}

.data-table .status-cell {
  text-align: center;
}

.data-table .actions-cell {
  text-align: center;
  white-space: nowrap;
}

.data-table .actions-cell button {
  margin: 0 4px;
}

/* تنسيقات للطباعة */
@media print {
  .reports-toolbar {
    display: none;
  }

  .report-content {
    box-shadow: none;
    padding: 0;
    border: none;
  }

  .stat-card, .stat-card:hover {
    transform: none;
    box-shadow: none;
    border: 1px solid #ddd;
    page-break-inside: avoid;
  }

  .stat-card::before {
    display: none;
  }

  .stat-icon, .stat-card:hover .stat-icon {
    transform: none;
    background-color: #f8f9fa;
    color: #333;
  }

  .data-table {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .data-table th {
    background-color: #f8f9fa;
    color: #333;
  }

  .data-table tr:hover td {
    background-color: transparent;
  }
}

/* تقارير العملاء */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.report-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  color: #2c3e50;
}

.report-period {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.data-card {
  margin-top: 20px;
}

/* تقرير العملاء الأكثر شراءً */
.top-customers-report, .sub-customers-report, .customer-invoices-report {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.customer-items-card {
  margin-top: 20px;
}

/* تقرير فواتير العملاء */
.customer-selector-card {
  margin-bottom: 20px;
}

.customer-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  right: 10px;
  color: #7f8c8d;
}

.search-box input {
  width: 100%;
  padding: 10px 35px 10px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.9rem;
}

.customers-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 5px;
}

.customer-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.customer-item:hover {
  background-color: #f8f9fa;
}

.customer-item.selected {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.customer-icon {
  color: #3498db;
  font-size: 1.2rem;
}

.customer-name {
  font-size: 0.9rem;
  color: #2c3e50;
}

.empty-message {
  text-align: center;
  padding: 20px;
  color: #7f8c8d;
}

.invoices-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  text-align: center;
}

.stat-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.payment-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.payment-status.paid {
  background-color: #e8f5e9;
  color: #388e3c;
}

.payment-status.unpaid {
  background-color: #ffebee;
  color: #d32f2f;
}

/* تفاصيل العميل */
.customer-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.customer-info-card {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.customer-info-section {
  flex: 1;
  min-width: 250px;
}

.customer-info-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
  color: #2c3e50;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
  min-width: 120px;
  color: #7f8c8d;
}

.info-value {
  color: #2c3e50;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  text-align: center;
}

.empty-state svg {
  font-size: 3rem;
  color: #bdc3c7;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px;
  color: #7f8c8d;
}

.empty-state p {
  margin: 0;
  color: #95a5a6;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .reports-toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .view-selector {
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .customer-info-card {
    flex-direction: column;
  }

  .invoices-stats {
    flex-direction: column;
  }
}
